package com.vos.task.manage.api.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vos.kernel.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 算子信息表
 *
 * <AUTHOR>
 * @since 2021-07-20 13:41:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("lh_task_arithmetic")
public class TaskArithmetic extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long arithmeticId;

    /**
     * 算子名称
     */
    private String arithmeticName;

    /**
     * 算子支持的最大并发路数
     */
    private Integer arithmeticConcurrent;

    /**
     * 算子标识
     */
    private String arithmeticIdentify;

    /**
     * 当前版本
     */
    private String currentVersion;


    /**
     * 备注
     */
    private String remark;
}
