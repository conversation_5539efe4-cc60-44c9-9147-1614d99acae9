package com.vos.task.manage.api.rpc;


import com.vos.task.manage.api.model.entity.TaskAppAbilityLimit;

/**
 * 任务-业务方能力额度表(rpc调用)
 *
 * <AUTHOR>
 * @since  2021-07-20 13:41:13
 */
public interface TaskAppAbilityLimitRpcService {

    TaskAppAbilityLimit getTaskAppAbilityLimitByAppIdAndAbilityId(Long appId, Long abilityId);


    /**
     * 根据appId 获取已分配的应用权益
     * @param appId
     * @return
     */
    Integer getExistAppCountByAppId(Long appId);
}