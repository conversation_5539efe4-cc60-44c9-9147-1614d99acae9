package com.vos.task.manage.api.rpc;

import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19
 * @description: com.vos.task.manage.api.rpc
 */
public interface IAiAbilityCallService {

    /**
     * 加入任务队列
     *
     * @param taskAddDTO
     * @return
     */
    Boolean add(TaskAddDTO taskAddDTO);

    /**
     * 添加workflow自定义worker任务
     *
     * @param taskAddRequest
     * @return
     */
    String addWorkflowWorkerTask(TaskAddRequest taskAddRequest);


    /**
     * 异步入队列
     *
     * @param taskAddDTO
     * @return
     */
    CompletableFuture<Boolean> addAsync(TaskAddDTO taskAddDTO);


    /**
     * 批量拉取任务
     *
     * @param taskPollRequest
     * @return
     */
    List<TaskMessage> batchPoll(TaskPollRequest taskPollRequest);


    /**
     * 更新任务状态
     *
     * @param taskUpdateRequest
     * @return
     */
    Boolean updateTaskStatus(TaskUpdateRequest taskUpdateRequest);
}
