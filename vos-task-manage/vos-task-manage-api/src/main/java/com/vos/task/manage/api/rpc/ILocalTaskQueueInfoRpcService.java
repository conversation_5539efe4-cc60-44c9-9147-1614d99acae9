package com.vos.task.manage.api.rpc;

import com.linker.omos.client.domain.LineCountMessageQueueDTO;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年07月02日
 * @version: 1.0
 * @description: TODO
 */
public interface ILocalTaskQueueInfoRpcService {

    /**
     * 获取正在运行的任务的本地内存大小
     *
     * @param isAllFlag
     * @return
     */
    List<LocalMessageQueueDTO> getQueueInfo(Integer isAllFlag);

    /**
     * @param isAllFlag
     */
    Boolean clearQueueInfo(Integer isAllFlag);

    /**
     * 获取所有阻塞的队列信息
     *
     * @return
     */
    List<LineCountMessageQueueDTO> getLineCountQueueInfo();

    /**
     * 获取workflow自定义节点所有阻塞的队列信息
     *
     * @return
     */
    List<LocalMessageQueueDTO> getCustomWorkerQueueInfo();

    /**
     * 清除阻塞队列信息
     *
     * @return
     */
    Boolean clearLineCountQueueInfo(Long abilityId);

    /**
     * 清除阻塞队列信息
     *
     * @return
     */
    Boolean clearLineCountQueueInfo();

    /**
     * 清除workflow自定义节点阻塞队列信息
     *
     * @param taskName
     * @return
     */
    Boolean clearCustomLineCountByTask(String taskName);
}
