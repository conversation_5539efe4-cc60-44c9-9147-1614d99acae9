package com.vos.task.manage.api.model.dto;

import cn.hutool.core.lang.Dict;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年07月02日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class LocalMessageQueueDTO implements Serializable {

    /**
     * 能力id
     */
    private String abilityId;

    /**
     * 能力标识
     */
    private String abilityCode;

    /**
     * 排队数量
     */
    private Long lineCount;

    /**
     * 本地内存队列大小
     */
    private Long capacity;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 平均耗时
     */
    private Long rt = 0L;

    /**
     * tps
     */
    private Long tps = 0L;

    /**
     * 详细信息
     */
    private List<Dict> details = new ArrayList<>();
}
