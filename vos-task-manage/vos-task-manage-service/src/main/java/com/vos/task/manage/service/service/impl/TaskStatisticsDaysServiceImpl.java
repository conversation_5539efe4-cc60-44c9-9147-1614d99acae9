package com.vos.task.manage.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.task.manage.service.mapper.TaskStatisticsDaysMapper;
import com.vos.task.manage.api.model.entity.TaskStatisticsDays;
import com.vos.task.manage.service.service.TaskStatisticsDaysService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 调度任务统计天实现类
 *
 * <AUTHOR>
 * @since 2022-01-7 14:47:57
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStatisticsDaysServiceImpl extends ServiceImpl<TaskStatisticsDaysMapper, TaskStatisticsDays> implements TaskStatisticsDaysService {


}
