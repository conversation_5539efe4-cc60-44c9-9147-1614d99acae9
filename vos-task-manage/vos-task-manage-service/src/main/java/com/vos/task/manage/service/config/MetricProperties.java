package com.vos.task.manage.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023年12月13日
 * @version: 1.0
 * @description: TODO
 */
@Data
@Component
@ConfigurationProperties(prefix = "metric")
@RefreshScope
public class MetricProperties {

    /**
     * 是否开启vos统计
     */
    private Boolean openGuiMetric;


    /**
     * 是否开启埋点统计
     */
    private Boolean openMetric = true;


    /**
     * 是否开启执行统计
     */
    private Boolean openCallMetric;

}
