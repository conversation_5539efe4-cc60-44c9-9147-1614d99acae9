package com.vos.task.manage.service.workflow;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;
import com.vos.task.manage.service.config.WorkflowProperties;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RStream;
import org.redisson.api.RedissonClient;
import org.redisson.api.StreamMessageId;
import org.redisson.api.stream.*;
import org.redisson.client.RedisBusyException;
import org.redisson.client.RedisException;
import org.redisson.spring.data.connection.RedissonConnection;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class RedisMessageQueue implements WorkflowQueue {

    /**
     * 流名称前缀
     */
    public static final String STREAM_PREFIX = "wf:";

    /**
     * redis操作类
     */
    private StringRedisTemplate stringRedisTemplate;

    /**
     * redis操作类
     */
    RedissonClient redissonClient;

    /**
     * 工作流配置
     */
    WorkflowProperties workflowProperties;

    /**
     * 队列名称
     */
    private String streamName;

    /**
     * 组名称
     */
    private static final String GROUP_NAME = "kernel";

    /**
     * 消费者名称
     */
    private String consumerName;

    /**
     * 消费者名称
     */
    private String queueName;

    /**
     * 是否运行
     */
    private boolean running;

    private RStream<String, TaskMessage> stream;

    /**
     * 构造函数
     *
     * @param queueName
     * @param stringRedisTemplate
     */
    public RedisMessageQueue(String queueName, StringRedisTemplate stringRedisTemplate, RedissonClient redissonClient, WorkflowProperties workflowProperties) {
        this.streamName = STREAM_PREFIX + queueName;
        this.queueName = queueName;
        this.stringRedisTemplate = stringRedisTemplate;
        this.redissonClient = redissonClient;
        this.workflowProperties = workflowProperties;
        this.consumerName = String.format("%s_%s", queueName, GROUP_NAME);
        this.stream = redissonClient.getStream(streamName);
        //创建流和消费者组
        createStreamAndConsumerGroup();
    }

    @Override
    public String push(TaskMessage message) {

        if (!BooleanUtil.isTrue(running)) {
            throw new DubboBaseException(queueName + "队列未启动,请稍后重试");
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String id = message.getId();
//        .trim(TrimStrategy.MAXLEN, workflowProperties.getMaxSize()))
        StreamMessageId data = stream.add(StreamAddArgs.entries(MapUtil.of("data", message)));
        stopWatch.stop();
        log.info("msgId:{}, 入队列成功,streamName：{},streamId:{},耗时：{}ms", id, streamName, data, stopWatch.getTotalTimeMillis());
        return id;
    }

    @Override
    public List<TaskMessage> batchPoll(Integer count) {
        if (log.isTraceEnabled()) {
            log.trace("batchPoll,queueName:{},count:{}", queueName, count);
        }
        //最多拉取20条
        if (count != null && count > 20) {
            count = 20;
        }
        List<TaskMessage> taskMessages = new ArrayList<>();
        if (!BooleanUtil.isTrue(running)) {
            log.warn(queueName + "队列未启动,请稍后重试");
            return taskMessages;
        }
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            //读取消息
            Map<StreamMessageId, Map<String, TaskMessage>> streamMessageIdMapMap = stream.readGroup(GROUP_NAME, consumerName, StreamReadGroupArgs.neverDelivered().count(count));
            //确认删除消息
            if (MapUtil.isNotEmpty(streamMessageIdMapMap)) {
                Set<StreamMessageId> streamMessageIds = streamMessageIdMapMap.keySet();
                stream.ack(GROUP_NAME, streamMessageIds.toArray(new StreamMessageId[0]));
                //删除消息
                stream.remove(streamMessageIds.toArray(new StreamMessageId[0]));
                Collection<Map<String, TaskMessage>> values = streamMessageIdMapMap.values();
                stopWatch.stop();
                log.debug("拉取成功,streamName：{},总量：{},耗时：{}ms", streamName,streamMessageIds.size(), stopWatch.getTotalTimeMillis());
                return values.stream().map(Map::values).flatMap(Collection::stream).collect(Collectors.toList());
            } else {
                stopWatch.stop();
                return taskMessages;
            }
        } catch (Exception e) {
            log.error("Error reading events from stream", e);
            return taskMessages;
        }
    }


    @Override
    public LocalMessageQueueDTO getQueueInfo() {
        LocalMessageQueueDTO localMessageQueue = new LocalMessageQueueDTO();
        if (!BooleanUtil.isTrue(running)) {
            log.warn(queueName + "队列未启动,请稍后重试");
            return localMessageQueue;
        }
        long size = stream.size();
//        PendingResult pendingInfo = stream.getPendingInfo(GROUP_NAME);
//        if (log.isDebugEnabled()) {
//            log.debug("{},queueInfo:{}", streamName, JSON.toJSONString(pendingInfo));
//        }
        localMessageQueue.setAbilityCode(queueName);
        localMessageQueue.setAbilityId(queueName);
        localMessageQueue.setLineCount(size);
        localMessageQueue.setLastCallTime(size);
        localMessageQueue.setRt(size);
        localMessageQueue.setTps(size);
        return localMessageQueue;
    }

    @Override
    public void clearLineCount() {
        if (BooleanUtil.isTrue(running)) {
            running = false;
            stringRedisTemplate.delete(streamName);
            createStreamAndConsumerGroup();
        }
    }

    /**
     * 创建流和消费者组
     */
    private void createStreamAndConsumerGroup() {
        try {
            // 尝试创建消费者组，如果流不存在，它将被自动创建
            stream.createGroup(StreamCreateGroupArgs.name(GROUP_NAME).makeStream());
        } catch (RedisBusyException e) {
            log.info("Consumer group '{}' already exist for stream '{}'", GROUP_NAME, streamName);
        } catch (RedisException e) {
            // 如果消费者组已经存在，我们将得到一个错误
            if (e.getMessage().contains(" Consumer Group name already exists")) {
                log.info("Consumer group '{}' already exists for stream '{}'", GROUP_NAME, streamName);
            } else {
                // 如果是其他错误，记录日志
                log.error("Error creating consumer group '{}' for stream '{}'", GROUP_NAME, streamName, e);
            }
        }
        running = true;
    }


}
