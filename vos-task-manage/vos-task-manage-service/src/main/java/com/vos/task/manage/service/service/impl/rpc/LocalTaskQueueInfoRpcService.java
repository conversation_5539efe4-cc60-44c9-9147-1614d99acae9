package com.vos.task.manage.service.service.impl.rpc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.LineCountMessageQueueDTO;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;
import com.vos.task.manage.api.rpc.ILocalTaskQueueInfoRpcService;
import com.vos.task.manage.service.curator.AbilityCallThreadCheckService;
import com.vos.task.manage.service.workflow.RedisScheduleStrategy;
import com.vos.task.poll.api.service.rpc.TaskQueueInfoPrcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.cluster.specifyaddress.Address;
import org.apache.dubbo.rpc.cluster.specifyaddress.UserSpecifiedAddressUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年07月02日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
@DubboService
public class LocalTaskQueueInfoRpcService implements ILocalTaskQueueInfoRpcService {

    @Resource
    AbilityCallThreadCheckService abilityCallThreadCheckService;

    @DubboReference
    TaskQueueInfoPrcService taskQueueInfoPrcService;

    @Resource
    RedisScheduleStrategy redisScheduleStrategy;

    @Override
    public List<LocalMessageQueueDTO> getQueueInfo(Integer isAllFlag) {

        List<LocalMessageQueueDTO> localMessageQueue = new ArrayList<>();
        HashMap<String, String> abilityToPollInstance = abilityCallThreadCheckService.getAbilityToPollInstance();
        if (MapUtil.isNotEmpty(abilityToPollInstance)) {
            List<String> pollIps = abilityToPollInstance.values().stream().distinct().collect(Collectors.toList());
            for (String s : pollIps) {
                UserSpecifiedAddressUtil.setAddress(new Address(s, 21881, true));
                List<LocalMessageQueueDTO> localQueueInfo = taskQueueInfoPrcService.getLocalQueueInfo(isAllFlag);
                localMessageQueue.addAll(localQueueInfo);
            }
        }
        return localMessageQueue;
    }

    @Override
    public Boolean clearQueueInfo(Integer isAllFlag) {
        HashMap<String, String> abilityToPollInstance = abilityCallThreadCheckService.getAbilityToPollInstance();
        if (MapUtil.isNotEmpty(abilityToPollInstance)) {
            List<String> pollIps = abilityToPollInstance.values().stream().distinct().collect(Collectors.toList());
            for (String s : pollIps) {
                UserSpecifiedAddressUtil.setAddress(new Address(s, 21881, true));
                taskQueueInfoPrcService.clearLocalQueueInfo();
            }
        }
        return true;
    }

    @Override
    public List<LocalMessageQueueDTO> getCustomWorkerQueueInfo() {
        List<LocalMessageQueueDTO> localMessageQueue = redisScheduleStrategy.getQueueInfo();

        return localMessageQueue;
    }

    @Override
    public List<LineCountMessageQueueDTO> getLineCountQueueInfo() {

        List<LineCountMessageQueueDTO> lineCountMessageQueues = new ArrayList<>();
        //获取mq中阻塞的队列信息
        List<LineCountMessageQueueDTO> mqLineCountQueueInfo = taskQueueInfoPrcService.getMqLineCountQueueInfo();

        HashSet<String> abilityList = new HashSet<>();
        Map<String, LineCountMessageQueueDTO> mqMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(mqLineCountQueueInfo)) {
            mqMap = mqLineCountQueueInfo.stream().collect(Collectors.toMap(t -> t.getAbilityId() + "|" + t.getAbilityCode(), Function.identity()));
            abilityList.addAll(mqMap.keySet());
        }
        if (log.isDebugEnabled()) {
            log.debug("mqLineCountQueueInfo:{}", JSON.toJSONString(mqLineCountQueueInfo));
        }
        //获取本地队列中阻塞的队列信息
        Map<String, LocalMessageQueueDTO> localMap = new HashMap<>();
        List<LocalMessageQueueDTO> localMessageQueues = getQueueInfo(1).stream().filter(t -> t.getLineCount() >= 0).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(localMessageQueues)) {
            localMap = localMessageQueues.stream().collect(Collectors.toMap(t -> t.getAbilityId() + "|" + t.getAbilityCode(), Function.identity()));
            abilityList.addAll(localMap.keySet());
        }
        if (log.isDebugEnabled()) {
            log.debug("localMessageQueues:{}", JSON.toJSONString(localMessageQueues));
        }
        if (CollectionUtil.isNotEmpty(abilityList)) {
            for (String s : abilityList) {
                LineCountMessageQueueDTO lineCountMessageQueueDTO = new LineCountMessageQueueDTO();
                String[] split = s.split("\\|");
                lineCountMessageQueueDTO.setAbilityId(split[0]);
                lineCountMessageQueueDTO.setAbilityCode(split[1]);
                lineCountMessageQueueDTO.setMqLineCount(mqMap.containsKey(s) ? mqMap.get(s).getMqLineCount() : 0L);
                lineCountMessageQueueDTO.setLocalLineCount(localMap.containsKey(s) ? localMap.get(s).getLineCount() : 0L);
                lineCountMessageQueueDTO.setLineCount(lineCountMessageQueueDTO.getMqLineCount() + lineCountMessageQueueDTO.getLocalLineCount());
                if (localMap.containsKey(s)) {
                    lineCountMessageQueueDTO.setRt(localMap.get(s).getRt());
                    lineCountMessageQueueDTO.setTps(localMap.get(s).getTps());
                }
                List<Dict> details = new ArrayList<>();
                if (mqMap.containsKey(s)) {
                    details.addAll(mqMap.get(s).getDetails());
                }
                if (localMap.containsKey(s)) {
                    details.addAll(localMap.get(s).getDetails());
                }
                lineCountMessageQueueDTO.setDetails(details);
                lineCountMessageQueues.add(lineCountMessageQueueDTO);
            }
        }

        //workflow 自定义worker 队列信息
        List<LocalMessageQueueDTO> customWorkerQueueInfo = getCustomWorkerQueueInfo();
        if (CollectionUtil.isNotEmpty(customWorkerQueueInfo)) {
            for (LocalMessageQueueDTO customWorkerQueue : customWorkerQueueInfo) {
                LineCountMessageQueueDTO lineCountMessageQueueDTO = new LineCountMessageQueueDTO();
                lineCountMessageQueueDTO.setAbilityId(customWorkerQueue.getAbilityId());
                lineCountMessageQueueDTO.setAbilityCode(customWorkerQueue.getAbilityCode());
                lineCountMessageQueueDTO.setMqLineCount(customWorkerQueue.getLineCount());
                lineCountMessageQueueDTO.setLineCount(customWorkerQueue.getLineCount());
                lineCountMessageQueues.add(lineCountMessageQueueDTO);
            }
        }
        return lineCountMessageQueues;
    }

    @Override
    public Boolean clearLineCountQueueInfo(Long abilityId) {

        //清除mq中的阻塞队列信息
        taskQueueInfoPrcService.clearLineCountQueueInfo(abilityId);
        //清除本地队列中的阻塞队列信息
        HashMap<String, String> abilityToPollInstance = abilityCallThreadCheckService.getAbilityToPollInstance();
        if (MapUtil.isNotEmpty(abilityToPollInstance) && abilityToPollInstance.containsKey(abilityId.toString())) {
            String s1 = abilityToPollInstance.get(abilityId.toString());
            UserSpecifiedAddressUtil.setAddress(new Address(s1, 21881, true));
            taskQueueInfoPrcService.clearLocalQueueInfo();
        }
        return true;
    }

    @Override
    public Boolean clearLineCountQueueInfo() {
        clearQueueInfo(1);

        return null;
    }

    @Override
    public Boolean clearCustomLineCountByTask(String taskName) {

        return redisScheduleStrategy.clearCustomLineCountByTask(taskName);
    }
}
