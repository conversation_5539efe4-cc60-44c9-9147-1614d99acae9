package com.vos.task.manage.service.service.impl.rpc;

import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import com.vos.kernel.common.enums.CallBackTypeEnum;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.vos.task.manage.api.rpc.IAiAbilityCallService;
import com.vos.task.manage.service.service.TaskService;
import com.vos.task.manage.service.workflow.RedisScheduleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/27
 * @description: com.vos.task.manage.service.service.impl.rpc
 */
@Slf4j
@DubboService
public class AiAbilityCallRpcServiceImpl implements IAiAbilityCallService {

    @Resource
    TaskService taskService;

    @Resource
    RedisScheduleStrategy redisScheduleStrategy;

    @Override
    public Boolean add(TaskAddDTO taskAddDTO) {
        taskAddDTO.setTenantId(6L);
        taskAddDTO.setTaskType(1);
        if (taskAddDTO.getCallbackType() == null) {
            taskAddDTO.setCallbackType(CallBackTypeEnum.RPC.getKey());
        }
        taskAddDTO.setRpcCall(true);
        taskService.add(taskAddDTO);
        return true;
    }

    @Override
    public String addWorkflowWorkerTask(TaskAddRequest taskAddRequest) {
        //自定义worker task 名称
        String taskName = taskAddRequest.getTaskName();
        //自定义worker 域名;区分来源
        String domain = taskAddRequest.getDomain();
        String queueKey = StrUtil.isBlank(domain) ? taskName : taskName + "_" + domain;
        TaskMessage taskMessage = new TaskMessage();
        taskMessage.setPayload(taskAddRequest.getParameters());
        taskMessage.setWaitTime(taskAddRequest.getWaitTime());
        taskMessage.setBizMeta(taskAddRequest.getBizMeta());
        taskMessage.setCallbackUrl(taskAddRequest.getCallbackUrl());
        return redisScheduleStrategy.get(StrUtil.trim(queueKey)).push(taskMessage);
    }

    /**
     * 异步压测下来cpu占用较高
     *
     * @param taskAddDTO
     * @return
     */
    @Override
    public CompletableFuture<Boolean> addAsync(TaskAddDTO taskAddDTO) {
        return CompletableFuture.completedFuture(add(taskAddDTO));
    }

    @Override
    public List<TaskMessage> batchPoll(TaskPollRequest taskPollRequest) {
        //自定义worker task 名称
        String taskName = taskPollRequest.getTaskDefName();
        //自定义worker 域名;区分来源
        String domain = taskPollRequest.getDomain();
        String queueKey = StrUtil.isBlank(domain) ? taskName : taskName + "_" + domain;

        return redisScheduleStrategy.get(StrUtil.trim(queueKey)).batchPoll(taskPollRequest.getBatchSize());
    }

    @Override
    public Boolean updateTaskStatus(TaskUpdateRequest taskUpdateRequest) {
        return true;
    }
}
