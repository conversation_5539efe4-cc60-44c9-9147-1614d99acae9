<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.core.service.mapper.TTaskSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.kernel.core.api.domain.TTaskSub">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parentid" property="parentid"/>
        <result column="categoryid" property="categoryid"/>
        <result column="status" property="status"/>
        <result column="columns" property="columns"/>
        <result column="type" property="type"/>
        <result column="SubTypes" property="SubTypes"/>
        <result column="addtime" property="addtime"/>
        <result column="updatetime" property="updatetime"/>
        <result column="CompleteTime" property="CompleteTime"/>
        <result column="errmsg" property="errmsg"/>
        <result column="isdel" property="isdel"/>
        <result column="data_type" property="dataType"/>
    </resultMap>

    <select id="getSubResultCount" parameterType="int" resultType="com.vos.kernel.core.api.vo.query.TaskSubCountVo">
        SELECT
            t1.NAME AS subName,
            t1.STATUS AS STATUS,
            t1.type AS aiType,
            t1.updatetime AS updateTime,
            count( t2.id ) AS count
        FROM
            t_task_sub t1
            LEFT JOIN tb_video_task_result t2 ON t2.task_sub_id = t1.id
            AND t2.STATUS = 2
        WHERE
            t1.parentid = #{taskId}
    </select>

    <select id="getCountByTaskId" parameterType="integer" resultType="int">
        select count(1) from t_task_sub where isdel = 0 and parentid =#{taskId}
    </select>

    <!--获取应用健康度-->
    <select id="getTaskTypeHealthy" resultType="java.lang.Integer">
        select count(*) from (
        select distinct
        tts.id
        from t_task_sub tts
        inner join t_task tt on tts.parentid = tt.id and tt.isdel = 0 and tt.enable = 1
        inner join t_task_type ttt on tts.type = ttt.id and ttt.isdel = 0
        inner join tb_video_task_setting tvts on tvts.task_id = tt.id and tvts.is_del = 0
        inner join tb_video_task_time tvtt on tvtt.task_id = tt.id
        where tts.isdel = 0
        <if test="null != orgId and '' != orgId">
            and tt.orgcode = #{orgId}
        </if>
        <if test="null != status">
            and ttt.status = #{status}
        </if>
        <if test="null != nowTime and '' != nowTime">
            and <![CDATA[tvtt.start_time <= #{nowTime}]]>
            and <![CDATA[tvtt.end_time >= #{nowTime}]]>
        </if>
            group by tts.type
        ) a
    </select>

    <select id="getByTaskId" resultType="com.vos.kernel.core.api.domain.TTaskSub">
        SELECT
            a.*,
            b.ability
        FROM
            t_task_sub a
                LEFT JOIN t_task_type b ON a.type = b.id
        WHERE
            a.parentid = #{taskId}
          AND a.data_type = 2
          AND a.isdel = 0
    </select>

    <select id="getSubWithOrderTypeByTaskId" resultType="com.vos.kernel.core.api.domain.TTaskSub">
        SELECT
            a.*,
            b.order_type
        FROM
            t_task_sub a
                LEFT JOIN t_task c ON a.parentid = c.id
                LEFT JOIN t_task_type_org_setting b ON a.type = b.type_id AND a.mu_ai_type = b.mu_type_id AND b.org_id = c.orgcode AND b.is_del = 0
        WHERE
            a.parentid = #{taskId}
          AND a.data_type = 2
          AND a.isdel = 0
    </select>

    <select id="getDeviceRun" resultType="map">
        SELECT
            ttbv.task_id as taskId,
            c.`name`,
            a.type as aiTypeId,
            a.mu_ai_type as muTypeId
        FROM
            t_task_sub a
                LEFT JOIN t_task_bind_video ttbv on ttbv.task_id = a.parentid
                LEFT JOIN tb_data_info_video b ON ttbv.video_id = b.id
                LEFT JOIN t_task c ON c.id = ttbv.task_id
        WHERE
            b.id = #{deviceId}
          AND a.isdel = 0
    </select>

    <select id="getTypeTotal" resultType="integer">
        SELECT count(type) as typeTotal  FROM t_task_sub
        where isdel =0
        <if test="null != taskId">
            and parentid =  #{taskId}
        </if>

    </select>

    <delete id="delSub">
        DELETE tts FROM
        t_task AS tt
        LEFT JOIN t_task_sub AS tts ON tts.task_code = tt.task_code
        WHERE tt.orgcode = #{authenticationId} and tts.task_type_code = #{taskTypeCode}
    </delete>
</mapper>
