<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.core.service.mapper.VideoTaskResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.kernel.core.api.domain.VideoTaskResultEntity">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="del" />
        <result column="type" property="type" />
        <result column="event_time" property="eventTime" />
        <result column="task_id" property="taskId" />
        <result column="key_image" property="keyImage" />
        <result column="key_video" property="keyVideo" />
        <result column="description" property="description" />
        <result column="task_setting_id" property="taskSettingId" />
        <result column="task_time_id" property="taskTimeId" />
        <result column="set_name" property="setName" />
        <result column="callback_image" property="callbackImage" />
        <result column="status" property="status" />
        <result column="is_check" property="isCheck" />
        <result column="video_id" property="videoId" />
        <result column="task_sub_id" property="taskSubId" />
        <result column="ai_type" property="aiType" />
        <result column="mu_ai_type" property="muAiType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="gif_url" property="gifUrl" />
        <result column="good_status" property="goodStatus" />
        <result column="is_sample" property="sample" />
        <result column="api_callback_status" property="apiCallbackStatus" />
        <result column="send_flag" property="sendFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, is_del, type, event_time, task_id, key_image, key_video, description, task_setting_id, task_time_id, set_name, callback_image, status, is_check, video_id, task_sub_id, ai_type, mu_ai_type, start_time, end_time, gif_url, good_status, is_sample, api_callback_status, send_flag
    </sql>


    <insert id="createTable">
        CREATE TABLE if not exists `tb_video_task_result_alarm_${tableName}`  like  tb_video_task_result;
    </insert>


    <resultMap type="com.vos.kernel.core.api.domain.VideoTaskResultEntity" id="TbVideoTaskResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="del" column="is_del" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="eventTime" column="event_time" jdbcType="TIMESTAMP"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
        <result property="keyImage" column="key_image" jdbcType="VARCHAR"/>
        <result property="keyVideo" column="key_video" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="taskSettingId" column="task_setting_id" jdbcType="INTEGER"/>
        <result property="taskTimeId" column="task_time_id" jdbcType="INTEGER"/>
        <result property="setName" column="set_name" jdbcType="VARCHAR"/>
        <result property="callbackImage" column="callback_image" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isCheck" column="is_check" jdbcType="INTEGER"/>
        <result property="videoId" column="video_id" jdbcType="INTEGER"/>
        <result property="taskSubId" column="task_sub_id" jdbcType="INTEGER"/>
        <result property="aiType" column="ai_type" jdbcType="INTEGER"/>
        <result property="muAiType" column="mu_ai_type" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="gifUrl" column="gif_url" jdbcType="VARCHAR"/>
        <result property="goodStatus" column="good_status" jdbcType="INTEGER"/>
        <result property="sample" column="is_sample" jdbcType="INTEGER"/>
        <result property="apiCallbackStatus" column="api_callback_status" jdbcType="INTEGER"/>
    </resultMap>
    <!--查询单个-->
    <select id="orgAlarmDataById" resultMap="TbVideoTaskResultMap">
        select id,
               create_time,
               update_time,
               is_del,
               type,
               event_time,
               task_id,
               key_image,
               key_video,
               description,
               task_setting_id,
               task_time_id,
               set_name,
               callback_image,
               status,
               is_check,
               video_id,
               task_sub_id,
               ai_type,
               mu_ai_type,
               start_time,
               end_time,
               gif_url,
               good_status,
               is_sample,
               api_callback_status
        from `tb_video_task_result`
        where id = #{id}
    </select>

    <select id="filterNotSequential" parameterType="long" resultType="long">
        SELECT
        t1.id
        FROM
        tb_video_task_result t1
        LEFT JOIN t_task t3 on t1.task_id = t3.id
        LEFT JOIN t_task_type_org_setting t2 on t2.org_id = t3.orgcode and t1.mu_ai_type = t2.mu_type_id and t2.is_del =
        0
        WHERE
        t1.id IN
        <foreach collection="eventIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and t2.sequential_status = 1
    </select>

    <select id="getListAiAndVideo" resultType="com.vos.kernel.core.api.dto.TaskResultWithAbilityAndVideoDTO">
        SELECT
        t1.id,
        t2.action_id aiTypeId,
        t2.ability,
        t1.mu_ai_type,
        t1.ai_type,
        t1.video_id,
        t1.event_time,
        t1.key_image,
        t2.name aiTypeName,
        t3.device_serial videoName,
        t1.create_time,
        t3.set_name,
        t3.channel,
        t1.create_time,
        t3.classify_id classifyId,
        t5.set_name classifyName,
        t6.`name` taskName,
        t6.is_key_point,
        tttos.msg_desc
        FROM
        tb_video_task_result t1
        LEFT JOIN t_task_type t2 on t2.task_type_code = t1.ai_type
        LEFT JOIN tb_data_info_video t3 on t3.video_code = t1.video_id
        LEFT JOIN tb_data_info_video t5 on t3.classify_id = t5.id
        LEFT JOIN t_task t6 on t6.task_code = t1.task_id
        LEFT JOIN t_task_type_org_setting tttos on tttos.type_id = t1.ai_type and tttos.org_id = t6.orgcode and tttos.is_del = 0
        where
        t1.id in
        <foreach collection="eventIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getBoxbyTypeCode" resultType="com.vos.kernel.core.api.dto.BboxTypeCodeDTO">
        SELECT
        t1.b_box_info,ai_type
        FROM
        tb_video_task_result t1
        INNER JOIN
        ( SELECT MAX( id ) AS id FROM tb_video_task_result where is_del = 0 and status = 2 and create_time >= CURDATE()
            and ai_type in
            <foreach collection="taskTypeCodes" item="item" close=")" open="(" separator=",">
             #{item}
            </foreach>
            GROUP BY ai_type
        ) t2 ON t1.id = t2.id
    </select>


</mapper>
