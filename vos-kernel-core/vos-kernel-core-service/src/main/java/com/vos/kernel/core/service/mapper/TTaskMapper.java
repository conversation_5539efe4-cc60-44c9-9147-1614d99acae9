package com.vos.kernel.core.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.vos.kernel.core.api.domain.TTask;
import com.vos.kernel.core.api.dto.TaskRunConfigDTO;
import com.vos.kernel.core.api.vo.TellAiParam;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Mapper
public interface TTaskMapper extends BaseMapper<TTask> {

    String getApiStr(@Param("apiId") Long apiId);

    /**
     * 获取任务执行信息
     *
     * @param taskCode
     * @param timeId
     * @return
     */
    TaskRunConfigDTO getTaskRunConfig(@Param("taskCode") String taskCode, @Param("timeId") Long timeId);

    List<TellAiParam> getTellAiByVideoIdList(@Param("videoCodeList") String videoCodeList, @Param("ability") String ability);
}

