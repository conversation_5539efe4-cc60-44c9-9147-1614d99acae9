package com.vos.kernel.core.service.service.batisplus.impl;

import cn.hutool.core.util.StrUtil;
import com.vos.kernel.core.api.domain.VideoTaskResultEntity;
import com.vos.kernel.core.api.dto.BboxTypeCodeDTO;
import com.vos.kernel.core.api.dto.params.ExportExcelAndZipResp;
import com.vos.kernel.core.service.config.AlarmDynamicTableThreadHolder;
import com.vos.kernel.core.service.mapper.VideoTaskResultMapper;
import com.vos.kernel.core.service.service.batisplus.IVideoTaskResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.kernel.data.api.model.entity.VideoTaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 视频任务结果详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
@Slf4j
public class VideoTaskResultServiceImpl extends ServiceImpl<VideoTaskResultMapper, VideoTaskResultEntity> implements IVideoTaskResultService {

    @Resource
    VideoTaskResultMapper videoTaskResultMapper;

    @Override
    public synchronized Integer insertOrgAlarmData(VideoTaskResultEntity result, String alarmTableName) {
        Integer integer = 0;
        try {
            AlarmDynamicTableThreadHolder.setTableNameHolder(alarmTableName.toLowerCase());
            integer = videoTaskResultMapper.insert(result);
        }catch (BadSqlGrammarException e){
            createTable(alarmTableName);
            integer = videoTaskResultMapper.insert(result);
        } catch (Exception e) {
            log.error("插入动态表名错误", e);

        } finally {
            AlarmDynamicTableThreadHolder.clearOrgHolder();
        }
        return integer;
    }

    @Override
    public void createTable(String tableName) {
        if (StrUtil.isNotBlank(tableName)) {
            log.info("创建 {} 告警结果表", tableName);
            videoTaskResultMapper.createTable(tableName.toLowerCase());
        }
    }

    @Override
    public VideoTaskResultEntity orgAlarmDataById(Long resultId, String orgId) {
        return videoTaskResultMapper.orgAlarmDataById(resultId, orgId);
    }

    @Override
    public List<ExportExcelAndZipResp> getNeedExportList(String orgCode, Long aiTypeId, String aiName) {
        return videoTaskResultMapper.getNeedExportList(orgCode, aiTypeId, aiName);
    }

    @Override
    public List<BboxTypeCodeDTO> getBoxbyTypeCode(List<String> taskTypeCodes) {
        return videoTaskResultMapper.getBoxbyTypeCode(taskTypeCodes);
    }


}
