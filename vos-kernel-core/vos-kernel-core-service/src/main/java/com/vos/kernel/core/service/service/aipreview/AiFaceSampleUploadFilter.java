package com.vos.kernel.core.service.service.aipreview;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linker.basic.utils.IdWorker;
import com.vos.kernel.core.api.domain.SampleUserPhotoEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.entity.constants.AITrainingSampleLibraryKey;
import com.vos.kernel.core.service.api.IFaceSampleService;
import com.vos.kernel.common.enums.ExtraEnhanceAiEnum;
import com.vos.kernel.core.service.service.batisplus.ISampleUserFaceService;
import com.vos.kernel.core.service.service.batisplus.ISampleUserPhotoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/3
 * @description: com.vos.kernel.core.service.service.aipreview
 */
@Slf4j
@Service
public class AiFaceSampleUploadFilter implements AiPreViewBaseFilter {

    @Resource
    ISampleUserPhotoService sampleUserPhotoService;

    @Resource
    ISampleUserFaceService sampleUserFaceService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    IFaceSampleService faceSampleService;

    @Value("${sample.init_index_url}")
    private String sampleInitIndexUrl;


    @Override
    public void doFilter(AiPreViewCallDTO aiPreViewCall, AiRuntimeContextDTO context, AiPreViewFilterChain filterChain) {

        TaskTypeEntity ability = context.getAbility();
        if (ExtraEnhanceAiEnum.FACE_RECOGNIZE.getCode().equals(ability.getActionId()) && BooleanUtil.isTrue(context.getWaitAiResult())
                && aiPreViewCall.getAbilityConfig() != null) {
            List<Long> compareSample = aiPreViewCall.getAbilityConfig().getCompareSample();

            if (CollectionUtil.isNotEmpty(compareSample)) {
                log.info("人脸识别效果预览，AI样本库处理");
                List<SampleUserPhotoEntity> sampleUserPhotoEntities = listSampleUserPhoto(compareSample);
                List<AiUploadUserFaceDTO> aiUploadUserFaces = new ArrayList<>();
                HashMap<String, String> faceImgIdTpUserMap = new HashMap<>(16);
                String sendToAiFaceGroupId = sampleUserFaceService.getSendToAiFaceGroupId(aiPreViewCall.getOperator(), aiPreViewCall.getAbilityId());
                String compareSampleStr = compareSample.stream().map(Object::toString).collect(Collectors.joining("a"));
                sendToAiFaceGroupId = (sendToAiFaceGroupId + aiPreViewCall.getDupFilterTaskCode() + compareSampleStr).toLowerCase();
                context.setFaceSampleIndexId(sendToAiFaceGroupId);
                if (CollectionUtil.isNotEmpty(sampleUserPhotoEntities)) {
                    for (SampleUserPhotoEntity i : sampleUserPhotoEntities) {
                        //判断是否之前已上传
                        Object o = stringRedisTemplate.opsForHash().get("FACE:" + sendToAiFaceGroupId, i.getSamplePhotoId().toString());
                        if (ObjectUtil.isNull(o)) {
                            AiUploadUserFaceDTO aiUploadUserFace = new AiUploadUserFaceDTO();
                            aiUploadUserFace.setIndexId(sendToAiFaceGroupId);
                            aiUploadUserFace.setData(i.getSamplePath());
                            aiUploadUserFace.setSrcType("url");
                            aiUploadUserFace.setOrgId(aiPreViewCall.getOperator());
                            aiUploadUserFace.setVideoId(aiPreViewCall.getVideoId());
                            //识别效果无需插入SampleUserPhotoVideo；使用ID；
                            aiUploadUserFace.setImageId(i.getSamplePhotoId().toString());
                            faceImgIdTpUserMap.put(aiUploadUserFace.getImageId(), i.getSampleUserId().toString());
                            aiUploadUserFaces.add(aiUploadUserFace);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(aiUploadUserFaces)) {
                        List<String> imageIdList = aiUploadUserFaces.stream().map(AiUploadUserFaceDTO::getImageId).collect(Collectors.toList());
                        context.setFaceSampleImageIdList(imageIdList);
                        //设置人脸imgID对应compareSample
                        stringRedisTemplate.opsForHash().putAll("FACE:" + sendToAiFaceGroupId, faceImgIdTpUserMap);
                        //请求ai
                        AiUserFaceUploadRequestDTO aiUserFaceUploadRequest = new AiUserFaceUploadRequestDTO();
                        aiUserFaceUploadRequest.setSrc(aiUploadUserFaces);
                        aiUserFaceUploadRequest.setSchedulingCenter("");
                        aiUserFaceUploadRequest.setTaskId(String.valueOf(IdWorker.nextId()));
                        try {
                            AiUserFaceUploadResultDTO aiUserFaceUploadResult = faceSampleService.uploadSampleInfoToAi(aiUserFaceUploadRequest);
                            log.info("人脸识别效果预览，上传AI样本库结果：{}", JSON.toJSONString(aiUserFaceUploadResult));
                        } catch (Exception e) {
                            log.error("人脸识别效果预览，上传AI样本库异常", e);
                        }

                    } else {
                        log.info("效果预览比对库已上传无需重复上传");
                    }

                }
            }
        }

        //执行后续链路
        context.setPos(context.getPos() + 1);
        filterChain.doFilter(aiPreViewCall, context, filterChain);
    }


    /**
     * 获取样本下的图片
     *
     * @param sampleUserId
     * @return
     */
    private List<SampleUserPhotoEntity> listSampleUserPhoto(List<Long> sampleUserId) {
        LambdaQueryWrapper<SampleUserPhotoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SampleUserPhotoEntity::getIsDel, 0);
        if (CollectionUtil.isNotEmpty(sampleUserId)) {
            queryWrapper.in(SampleUserPhotoEntity::getSampleUserId, sampleUserId);
        }
        return sampleUserPhotoService.list(queryWrapper);
    }

    /**
     * 删除效果预览时，临时上传ai的比对样本库
     *
     * @param context
     */
    public void deleteSampleImage(AiRuntimeContextDTO context) {
        TaskTypeEntity ability = context.getAbility();
        if (ExtraEnhanceAiEnum.FACE_RECOGNIZE.getCode().equals(ability.getActionId()) && CollectionUtil.isNotEmpty(context.getFaceSampleImageIdList())) {
            String faceSampleIndexId = context.getFaceSampleIndexId();
            List<String> faceSampleImageIdList = context.getFaceSampleImageIdList();
            log.info("开始删除效果预览时，临时上传ai的比对样本库，{},{}", faceSampleIndexId, JSON.toJSONString(faceSampleIndexId));
            String docIds = String.join(",", faceSampleImageIdList);
            String url = sampleInitIndexUrl + faceSampleIndexId.toLowerCase() + "/docs/" + docIds;
            faceSampleService.deleteSampleToAi(url);
        }
    }
}
