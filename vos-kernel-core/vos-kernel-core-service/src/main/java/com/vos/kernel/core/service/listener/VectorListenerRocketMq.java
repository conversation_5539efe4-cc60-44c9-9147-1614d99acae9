package com.vos.kernel.core.service.listener;

import com.alibaba.fastjson.JSONObject;
import com.vos.kernel.common.common.Constants;
import com.vos.kernel.core.service.service.ICallBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = Constants.CONSUMER_GROUP + "_vectorSubmit", topic = Constants.VECTOR_SUBMIT)
public class VectorListenerRocketMq implements RocketMQListener<String> {

    @Autowired
    ICallBackService callBackService;

    @Override
    public void onMessage(String message) {

        try {
            JSONObject jsonObject = JSONObject.parseObject(message);
            callBackService.vectorMqCallback(jsonObject);
        } catch (Exception e) {
            log.debug("数据处理异常sampleFeedbackPhoto:e=", e);
        }
    }
}
