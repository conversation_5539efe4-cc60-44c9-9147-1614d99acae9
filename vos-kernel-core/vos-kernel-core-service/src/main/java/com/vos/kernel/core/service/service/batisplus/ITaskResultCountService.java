package com.vos.kernel.core.service.service.batisplus;

import com.vos.kernel.core.api.domain.AbilityEntity;
import com.vos.kernel.core.api.domain.TaskResultCountEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vos.kernel.core.service.entity.OmHubStatisticsDTO;
import com.vos.kernel.core.service.entity.UserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
public interface ITaskResultCountService extends IService<TaskResultCountEntity> {


    /**
     * 获取某一天统计信息根据算法类型
     *
     * @param date
     * @return
     */
    List<OmHubStatisticsDTO> getStatisticsByAbilityEnum(String date);

    /**
     * 租户信息
     *
     * @param startDate
     * @param endDate
     * @param tenantId
     * @return
     */
    List<OmHubStatisticsDTO> getStatisticsByTenant(String startDate, String endDate, String tenantId);

    /**
     * 获取所有用户信息
     *
     * @return
     */
    List<UserDTO> getUserList();

    /**
     * 算法信息
     *
     * @param abilityId
     * @return
     */
    AbilityEntity getAbility(String abilityId);

    /**
     * 算法信息
     *
     * @param
     * @return
     */
    List<AbilityEntity> getAbilityList();
}
