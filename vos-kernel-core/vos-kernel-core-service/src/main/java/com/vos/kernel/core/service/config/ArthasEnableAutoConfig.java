package com.vos.kernel.core.service.config;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.arthas.spring.ArthasProperties;
import com.alibaba.arthas.spring.StringUtils;
import com.taobao.arthas.agent.attach.ArthasAgent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/7
 * @description: com.vos.kernel.common.config
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({ArthasProperties.class})
@ConditionalOnProperty(name = "spring.arthas.enabled", matchIfMissing = true)
public class ArthasEnableAutoConfig {

    @Autowired
    ConfigurableEnvironment environment;


    @ConditionalOnMissingBean
    @Bean
    public ArthasAgent arthasAgent(@Autowired @Qualifier("arthasConfigMap") Map<String, String> arthasConfigMap,
                                   @Autowired ArthasProperties arthasProperties) throws Throwable {

        String enableArthas = environment.getProperty("spring.arthas.enabled");
        if (StrUtil.isBlank(enableArthas) || BooleanUtil.isFalse(Boolean.valueOf(enableArthas))) {
            log.info("自定义arthasAgent以不启用");
            return new ArthasAgent();
        }

        arthasConfigMap = StringUtils.removeDashKey(arthasConfigMap);
        ArthasProperties.updateArthasConfigMapDefaultValue(arthasConfigMap);

        String appName = environment.getProperty("spring.application.name");
        if (arthasConfigMap.get("appName") == null && appName != null) {
            arthasConfigMap.put("appName", appName);
        }

        String agentId = StrUtil.isBlank(System.getenv("HOSTNAME")) ? StrUtil.uuid() : System.getenv("HOSTNAME");
        if (arthasConfigMap.get("agentId") == null && agentId != null) {
            arthasConfigMap.put("agentId", agentId);
        }
        // 给配置全加上前缀
        Map<String, String> mapWithPrefix = new HashMap<String, String>(arthasConfigMap.size());
        for (Map.Entry<String, String> entry : arthasConfigMap.entrySet()) {
            mapWithPrefix.put("arthas." + entry.getKey(), entry.getValue());
        }

        final ArthasAgent arthasAgent = new ArthasAgent(mapWithPrefix, arthasProperties.getHome(),
                arthasProperties.isSlientInit(), null);

        arthasAgent.init();
        log.info("Arthas agent start success,agentId:{}", agentId);
        return arthasAgent;

    }

}
