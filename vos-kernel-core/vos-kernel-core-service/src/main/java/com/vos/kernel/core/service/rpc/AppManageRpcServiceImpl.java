package com.vos.kernel.core.service.rpc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.BizIdUtil;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.entity.AbilityInstallResultDTO;
import com.vos.kernel.common.entity.OmInfoGetDTO;
import com.vos.kernel.common.enums.AbilityOmTypeEnum;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.EnterpriseCodeUtils;
import com.vos.kernel.core.api.domain.*;
import com.vos.kernel.core.api.dto.OmCallBackMqDTO;
import com.vos.kernel.core.api.entity.constants.RedisKey;
import com.vos.kernel.core.api.entity.constants.RocketMqTopic;
import com.vos.kernel.core.api.exception.AppManageException;
import com.vos.kernel.core.api.rpc.AppManageRpcService;
import com.vos.kernel.core.api.rpc.IAIAbilityRpcService;
import com.vos.kernel.core.api.rpc.TTaskTypeRpcService;
import com.vos.kernel.core.api.rpc.TUserAuthenticationRpcService;
import com.vos.kernel.core.api.vo.*;
import com.vos.kernel.core.api.vo.query.VideoSonTaskVo;
import com.vos.kernel.core.api.vo.query.VideoTaskTimeVo;
import com.vos.kernel.core.service.config.AbilityConfigProperties;
import com.vos.kernel.core.service.config.OmPageCallbackProperties;
import com.vos.kernel.core.service.convert.LicenseDataDtoMapper;
import com.vos.kernel.core.service.enums.*;
import com.vos.kernel.core.service.mapper.*;
import com.vos.kernel.core.service.service.IComparisonAbilityInitService;
import com.vos.kernel.core.service.service.batisplus.IAbilityService;
import com.vos.kernel.data.api.rpc.TaskService;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.*;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import com.vos.task.automated.api.model.enums.OmInstallStatusEnum;
import com.vos.task.automated.api.service.rpc.IAbilityAutomatedRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18
 * @description: 应用管理服务类
 */
@Slf4j
@Service
@DubboService
public class AppManageRpcServiceImpl implements AppManageRpcService {

    @Resource
    private TTaskTypeRpcService tTaskTypeService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TTaskTypeMapper taskTypeMapper;

    @Autowired
    private TTaskSubMapper taskSubMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TTaskTypeConfigAiMapper tTaskTypeConfigAiMapper;

    @DubboReference
    private TUserAuthenticationRpcService tUserAuthenticationRpcService;

    @Resource
    private TApplyEquityMapper tApplyEquityMapper;

    @Resource
    private TbOrgCheckMapper tbOrgCheckMapper;

    @Autowired
    private TTaskTypeRpcService tTaskTypeRpcService;

    @Autowired
    private TTaskTypeOrgSettingMapper taskTypeOrgSettingMapper;

    @Resource
    private TUserAuthenticationMapper tUserAuthenticationMapper;

    @Resource
    private AbilityMapper abilityMapper;
    @Resource
    private OmPageCallbackProperties omPageCallbackPrcoperties;

    @Value("${webdav.base}")
    private String webdavUrl;

    @Value("${webdav.home}")
    private String webdavHome;

    @Value("${om.huizhi.url}")
    private String omHuiZhiUrl;

    @Value("${om.profile}")
    private Integer profile;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private TaskService taskService;

    @DubboReference
    private IAbilityAutomatedRpcService abilityAutomatedRpcService;

    @Resource
    IAIAbilityRpcService abilityRpcService;

    @Resource
    LicenseDataDtoMapper licenseDataDtoMapper;

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Resource
    IAbilityService abilityService;

    @Resource
    IComparisonAbilityInitService comparisonAbilityInitService;


    @Resource
    AbilityConfigProperties abilityConfigProperties;

    public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Map<String, String> uploadOMToWebDav(MultipartFile omFile) {
        if (omFile == null) {
            throw new AppManageException("500", "om包不能为空");
        }
        Date date = new Date();
        long time = date.getTime();
        String timestamp = String.valueOf(time);
        String filePath = "";
        try {
            filePath = upload(omFile);
            stringRedisTemplate.opsForValue().set(RedisKey.OM_TIMESTAMP + timestamp, "1");
            // webdav接口上传
            //String name = omFile.getOriginalFilename();
            //InputStream inputStream = omFile.getInputStream();
            //filePath = webDavService.uploadWebDav(webDavVo, "om/" + time + "/" + name, inputStream);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppManageException("500", "上传om包失败");
        }
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("filePath", filePath);
        resultMap.put("timestamp", timestamp);
        return resultMap;
    }


    String upload(MultipartFile file) throws Exception {
        // 文件名称
        String fileName = file.getOriginalFilename();
        Date date = new Date();
        long time = date.getTime();
        String filePath = webdavUrl + "omPacket/" + time + "/" + fileName;
        // 复制文件
        File webDavHomeOm = new File(webdavHome + "omPacket/");
        if (!webDavHomeOm.exists()) {
            webDavHomeOm.mkdir();
        }

        File webDavHomeOmTime = new File(webdavHome + "omPacket/" + time + "/");
        if (!webDavHomeOmTime.exists()) {
            webDavHomeOmTime.mkdir();
        }
        File targetFile = new File(webdavHome + "omPacket/" + time + "/" + fileName);
        FileUtils.writeByteArrayToFile(targetFile, file.getBytes());
        return filePath;
    }

    @Override
    public String enableCallback(JSONObject jsonObject) {
        if (Objects.nonNull(jsonObject)) {
            Integer statusInt = jsonObject.getInteger("statusInt");
            String appVersionId = jsonObject.getString("appVersionId");
            String versionNum = jsonObject.getString("versionNum");
            String omVersionId = jsonObject.getString("omVersionId");
            String abilityUrl = jsonObject.getString("abilityUrl");
            String slave = jsonObject.getString("slave");
            String slaveIp = jsonObject.getString("slaveIp");
            String failedMessage = jsonObject.getString("failedMessage");
            Boolean isMatchType = jsonObject.getBoolean("isMatchType");
            String matchUploadUrl = jsonObject.getString("matchUploadUrl");
            TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery()
                    .eq(TTaskType::getAbility, omVersionId)
                    .eq(TTaskType::getIsdel, false).orderByAsc(TTaskType::getCreateTime).last("limit 1"));

            if (null == taskType) {
                log.error("获取能力失败：{}", JSON.toJSONString(jsonObject));
                return "";
            }
            if (statusInt == 5) {
                taskType.setStatus(OmInstallStatusEnum.OFFLINE.getStatus());
                taskType.setAppStatus(OmInstallStatusEnum.OFFLINE.getStatus());
                taskType.setErrorMsg(failedMessage);
            } else {
                taskType.setAppStatus(statusInt);
                taskType.setStatus(statusInt);
                if (0 == statusInt) {
                    taskType.setErrorMsg("");
                }
                taskType.setIsMatchType(isMatchType);
                taskType.setMatchUploadUrl(matchUploadUrl);
            }
            taskTypeMapper.updateById(taskType);

            String omCallBackUrl = stringRedisTemplate.opsForValue().get(RedisKey.ENABLE_CALL_BACK + taskType.getTaskTypeCode());

            OmCallBackMqDTO omCallBackMqDTO = new OmCallBackMqDTO();
            omCallBackMqDTO.setAppStatus(statusInt);
            omCallBackMqDTO.setTaskTypeCode(taskType.getTaskTypeCode());
            omCallBackMqDTO.setActionId(taskType.getActionId());
            omCallBackMqDTO.setOmCallBackUrl(omCallBackUrl);
            omCallBackMqDTO.setAbilityUrl(abilityUrl);
            omCallBackMqDTO.setSlave(slave);
            omCallBackMqDTO.setSlaveIp(slaveIp);
            omCallBackMqDTO.setFailedMessage(failedMessage);
            omCallBackMqDTO.setIsMatchType(isMatchType);
            omCallBackMqDTO.setMatchUploadUrl(matchUploadUrl);
            rocketMQTemplate.syncSend(RocketMqTopic.OM_CALL_BACK_TOPIC, MessageBuilder.withPayload(omCallBackMqDTO).build());
            stringRedisTemplate.delete(RedisKey.ENABLE_CALL_BACK + taskType.getTaskTypeCode());
            return taskType.getActionId();

        }
        return "";
    }

    @Override
    public String installCallback(JSONObject jsonObject) {
        if (Objects.nonNull(jsonObject)) {
            Integer statusInt = jsonObject.getInteger("statusInt");
            String appVersionId = jsonObject.getString("appVersionId");
            String versionNum = jsonObject.getString("versionNum");
            String omVersionId = jsonObject.getString("omVersionId");
            String abilityUrl = jsonObject.getString("abilityUrl");
            Long abilityId = jsonObject.getLong("abilityId");
            String slave = jsonObject.getString("slave");
            String slaveIp = jsonObject.getString("slaveIp");
            String failedMessage = jsonObject.getString("failedMessage");
            Boolean isMatchType = jsonObject.getBoolean("isMatchType");
            String matchUploadUrl = jsonObject.getString("matchUploadUrl");
            TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery()
                    .eq(TTaskType::getAbility, omVersionId)
                    .eq(TTaskType::getIsdel, false).orderByAsc(TTaskType::getCreateTime).last("limit 1"));

            if (null == taskType) {
                log.error("获取能力失败：{}", JSON.toJSONString(jsonObject));
                return "";
            }
            if (statusInt == 5) {
                // 删除当前应用
                taskType.setIsdel(true);
                taskType.setStatus(AppStatusEnum.ABNORMAL.type);
                taskType.setAppStatus(AppStatusEnum.DELETED.type);
                taskType.setErrorMsg(failedMessage);
            } else {
                taskType.setAppStatus(statusInt);
                taskType.setStatus(statusInt);
                taskType.setIsMatchType(isMatchType);
                taskType.setMatchUploadUrl(matchUploadUrl);

            }
            taskTypeMapper.updateById(taskType);

            String omCallBackUrl = stringRedisTemplate.opsForValue().get(RedisKey.INSTALL_CALL_BACK + taskType.getTaskTypeCode());

            OmCallBackMqDTO omCallBackMqDTO = new OmCallBackMqDTO();
            omCallBackMqDTO.setAbilityEnum(taskType.getAbilityEnum());
            omCallBackMqDTO.setAppStatus(statusInt);
            omCallBackMqDTO.setTaskTypeCode(taskType.getTaskTypeCode());
            omCallBackMqDTO.setActionId(taskType.getActionId());
            omCallBackMqDTO.setAbilityUrl(abilityUrl);
            omCallBackMqDTO.setAbilityId(abilityId);
            omCallBackMqDTO.setSlave(slave);
            omCallBackMqDTO.setSlaveIp(slaveIp);
            omCallBackMqDTO.setFailedMessage(failedMessage);
            omCallBackMqDTO.setOmCallBackUrl(omCallBackUrl);
            omCallBackMqDTO.setIsMatchType(isMatchType);
            omCallBackMqDTO.setMatchUploadUrl(matchUploadUrl);
            rocketMQTemplate.syncSend(RocketMqTopic.OM_CALL_BACK_TOPIC, MessageBuilder.withPayload(omCallBackMqDTO).build());
            stringRedisTemplate.delete(RedisKey.INSTALL_CALL_BACK + taskType.getTaskTypeCode());
            return taskType.getActionId();
        }
        return "";
    }

    @Override
    public void updateCallback(JSONObject jsonObject) {
        if (Objects.nonNull(jsonObject)) {
            Integer statusInt = jsonObject.getInteger("statusInt");
            String appVersionId = jsonObject.getString("appVersionId");
            String versionNum = jsonObject.getString("versionNum");
            String omVersionId = jsonObject.getString("omVersionId");
            String abilityUrl = jsonObject.getString("abilityUrl");
            String slave = jsonObject.getString("slave");
            String slaveIp = jsonObject.getString("slaveIp");
            String failedMessage = jsonObject.getString("failedMessage");
            Boolean isMatchType = jsonObject.getBoolean("isMatchType");
            String matchUploadUrl = jsonObject.getString("matchUploadUrl");
            TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery()
                    .eq(TTaskType::getAbility, omVersionId)
                    .eq(TTaskType::getIsdel, false).orderByAsc(TTaskType::getCreateTime).last("limit 1"));

            if (null == taskType) {
                log.error("获取能力失败：{}", JSON.toJSONString(jsonObject));
                return;
            }
            Optional.ofNullable(taskType).map(e -> {
                if (statusInt != 5) {
                    taskType.setAppStatus(statusInt);
                    taskType.setStatus(statusInt);
                    taskType.setIsMatchType(isMatchType);
                    taskType.setMatchUploadUrl(matchUploadUrl);
                }
                taskTypeMapper.updateById(taskType);

                String omCallBackUrl = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_CALL_BACK + taskType.getTaskTypeCode());

                OmCallBackMqDTO omCallBackMqDTO = new OmCallBackMqDTO();
                omCallBackMqDTO.setAbilityEnum(taskType.getAbilityEnum());
                omCallBackMqDTO.setAppStatus(statusInt);
                omCallBackMqDTO.setTaskTypeCode(taskType.getTaskTypeCode());
                omCallBackMqDTO.setActionId(taskType.getActionId());
                omCallBackMqDTO.setAbilityUrl(abilityUrl);
                omCallBackMqDTO.setSlave(slave);
                omCallBackMqDTO.setSlaveIp(slaveIp);
                omCallBackMqDTO.setFailedMessage(failedMessage);
                omCallBackMqDTO.setOmCallBackUrl(omCallBackUrl);
                omCallBackMqDTO.setIsMatchType(isMatchType);
                omCallBackMqDTO.setMatchUploadUrl(matchUploadUrl);
                rocketMQTemplate.syncSend(RocketMqTopic.OM_CALL_BACK_TOPIC, MessageBuilder.withPayload(omCallBackMqDTO).build());
                stringRedisTemplate.delete(RedisKey.UPDATE_CALL_BACK + taskType.getTaskTypeCode());
                return null;
            });
        }
    }

    //离线激活回调
    @Override
    public void offlineActivationCallback(JSONObject jsonObject) {
        if (Objects.nonNull(jsonObject)) {
            Integer statusInt = jsonObject.getInteger("statusInt");
            String appVersionId = jsonObject.getString("appVersionId");
            String versionNum = jsonObject.getString("versionNum");
            String omVersionId = jsonObject.getString("omVersionId");
            String abilityUrl = jsonObject.getString("abilityUrl");
            String slave = jsonObject.getString("slave");
            String slaveIp = jsonObject.getString("slaveIp");
            String failedMessage = jsonObject.getString("failedMessage");
            Boolean isMatchType = jsonObject.getBoolean("isMatchType");
            String matchUploadUrl = jsonObject.getString("matchUploadUrl");
            TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery()
                    .eq(TTaskType::getAbility, omVersionId)
                    .eq(TTaskType::getIsdel, false).orderByAsc(TTaskType::getCreateTime).last("limit 1"));

            if (null == taskType) {
                log.error("获取能力失败：{}", JSON.toJSONString(jsonObject));
                return;
            }
            Optional.ofNullable(taskType).map(e -> {
                if (statusInt == 5) {
                    // 删除当前应用
                    taskType.setStatus(OmInstallStatusEnum.INACTIVE.getStatus());
                    taskType.setAppStatus(OmInstallStatusEnum.INACTIVE.getStatus());
                    taskType.setErrorMsg(failedMessage);
                } else {
                    taskType.setAppStatus(statusInt);
                    taskType.setStatus(statusInt);
                    taskType.setIsMatchType(isMatchType);
                    taskType.setMatchUploadUrl(matchUploadUrl);
                }

                taskTypeMapper.updateById(taskType);
                String omCallBackUrl = stringRedisTemplate.opsForValue().get(RedisKey.OFFLINEACTIVATION_CALL_BACK + taskType.getTaskTypeCode());
                OmCallBackMqDTO omCallBackMqDTO = new OmCallBackMqDTO();
                omCallBackMqDTO.setAbilityEnum(taskType.getAbilityEnum());
                omCallBackMqDTO.setAppStatus(statusInt);
                omCallBackMqDTO.setTaskTypeCode(taskType.getTaskTypeCode());
                omCallBackMqDTO.setActionId(taskType.getActionId());
                omCallBackMqDTO.setAbilityUrl(abilityUrl);
                omCallBackMqDTO.setSlave(slave);
                omCallBackMqDTO.setSlaveIp(slaveIp);
                omCallBackMqDTO.setFailedMessage(failedMessage);
                omCallBackMqDTO.setOmCallBackUrl(omCallBackUrl);
                omCallBackMqDTO.setIsMatchType(isMatchType);
                omCallBackMqDTO.setMatchUploadUrl(matchUploadUrl);
                rocketMQTemplate.syncSend(RocketMqTopic.OM_CALL_BACK_TOPIC, MessageBuilder.withPayload(omCallBackMqDTO).build());
                stringRedisTemplate.delete(RedisKey.OFFLINEACTIVATION_CALL_BACK + taskType.getTaskTypeCode());
                return null;
            });
        }
    }

    @Override
    public void onlineInstallCallback(JSONObject jsonObject) {
        if (Objects.nonNull(jsonObject)) {
            Integer statusInt = jsonObject.getInteger("statusInt");
            String appVersionId = jsonObject.getString("appVersionId");
            String versionNum = jsonObject.getString("versionNum");
            String omVersionId = jsonObject.getString("omVersionId");
            String abilityUrl = jsonObject.getString("abilityUrl");
            String slave = jsonObject.getString("slave");
            String slaveIp = jsonObject.getString("slaveIp");
            String failedMessage = jsonObject.getString("failedMessage");
            Boolean isMatchType = jsonObject.getBoolean("isMatchType");
            String matchUploadUrl = jsonObject.getString("matchUploadUrl");
            TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery()
                    .eq(TTaskType::getAbility, omVersionId)
                    .eq(TTaskType::getIsdel, false).orderByAsc(TTaskType::getCreateTime).last("limit 1"));

            if (null == taskType) {
                log.error("获取能力失败：{}", JSON.toJSONString(jsonObject));
                return;
            }
            Optional.ofNullable(taskType).map(e -> {
                if (statusInt == 5) {
                    // 删除当前应用
                    taskType.setIsdel(true);
                    taskType.setStatus(AppStatusEnum.ABNORMAL.type);
                    taskType.setAppStatus(AppStatusEnum.DELETED.type);
                    taskType.setErrorMsg(failedMessage);
                } else {
                    taskType.setAppStatus(statusInt);
                    taskType.setStatus(statusInt);
                    taskType.setIsMatchType(isMatchType);
                    taskType.setMatchUploadUrl(matchUploadUrl);
                }
                taskTypeMapper.updateById(taskType);

                String omCallBackUrl = stringRedisTemplate.opsForValue().get(RedisKey.ONLINEINSTALL_CALL_BACK + taskType.getTaskTypeCode());

                OmCallBackMqDTO omCallBackMqDTO = new OmCallBackMqDTO();
                omCallBackMqDTO.setAbilityEnum(taskType.getAbilityEnum());
                omCallBackMqDTO.setAppStatus(statusInt);
                omCallBackMqDTO.setTaskTypeCode(taskType.getTaskTypeCode());
                omCallBackMqDTO.setActionId(taskType.getActionId());
                omCallBackMqDTO.setAbilityUrl(abilityUrl);
                omCallBackMqDTO.setSlave(slave);
                omCallBackMqDTO.setSlaveIp(slaveIp);
                omCallBackMqDTO.setFailedMessage(failedMessage);
                omCallBackMqDTO.setOmCallBackUrl(omCallBackUrl);
                omCallBackMqDTO.setIsMatchType(isMatchType);
                omCallBackMqDTO.setMatchUploadUrl(matchUploadUrl);
                rocketMQTemplate.syncSend(RocketMqTopic.OM_CALL_BACK_TOPIC, MessageBuilder.withPayload(omCallBackMqDTO).build());
                stringRedisTemplate.delete(RedisKey.ONLINEINSTALL_CALL_BACK + taskType.getTaskTypeCode());
                return null;
            });
        }
    }


    @Override
    public AbilityInstallResultDTO installWithStrategy(AppManageReqVO reqVO, JSONObject decryptData, String authCode) {
        if (StrUtil.isBlank(reqVO.getFileName())) {
            throw new BusinessException("om下载地址不能为空");
        }
        if (StrUtil.isBlank(reqVO.getInstallCallbackUrl())) {
            throw new BusinessException("om包安装回调地址不能为空");
        }
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);
        // 导入的时候校验当前超管或者租户的应用数量是否已经超出安装限制
        if (tUserAuthentication.getIsAdmin() == 0) {
            Integer appLimit = decryptData.getInteger("appLimit");
            //fyx备注后改
            Integer count = tTaskTypeService.countByUser();
            if (count >= appLimit) {
                throw new AppManageException("500", "应用数量已达上限，无法继续安装");
            }
        }
        //获取用户信息 fyx备注后改
        String orgCode = tUserAuthentication.getAuthenticationId();
        String omFileName = reqVO.getFileName().substring(reqVO.getFileName().lastIndexOf("/") + 1);
        String filePath = reqVO.getFileName().substring(0, reqVO.getFileName().lastIndexOf("/") + 1);
        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);
        if (StrUtil.isBlank(userCode)) {
            throw new BusinessException("当前用户userCode不能为空");
        }

        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }
        Boolean isAdmin = true;
        if (tUserAuthentication.getIsAdmin() == 0) {
            isAdmin = false;
        }
        OmAbilityInstallDTO omAbilityInstallDTO = new OmAbilityInstallDTO();
        omAbilityInstallDTO.setOmFileName(omFileName);
        omAbilityInstallDTO.setFilePath(filePath);
        omAbilityInstallDTO.setDeviceIdentity(deviceIdentity);
        omAbilityInstallDTO.setAppKey(key);
        omAbilityInstallDTO.setCallBackUrl(omPageCallbackPrcoperties.getInstallCallback());//"http://vos-kernel-business.vos:30002/app/manage/install/callback"
        omAbilityInstallDTO.setIsAdmin(isAdmin);
        // 请求汇智
        LicenseDataDto abilityInstalledDTO = abilityAutomatedRpcService.omAbilityInstall(omAbilityInstallDTO);
        log.info("注册算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));
        abilityInstalledDTO.setExpireDate(abilityInstalledDTO.getExpireDate() + " 23:59:59");
        abilityInstalledDTO.setCreateTime(LocalDateTime.now().toString());
        AbilityInstalledDTO abilityInstalledInfo = licenseDataDtoMapper.abilityInstalledInfo(abilityInstalledDTO);
        abilityInstalledInfo.setAppKey(key);
        abilityInstalledInfo.setAuId(orgCode);
        if (tUserAuthentication.getIsAdmin() == 1) {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType());
        } else {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.omImport.getAbilityType());
        }

        abilityInstalledInfo.setIsMatchType(abilityInstalledDTO.getIsMatchType());
        abilityInstalledInfo.setMatchUploadUrl(abilityInstalledDTO.getMatchUploadUrl());
        abilityInstalledInfo.setOmGpuInfo(abilityInstalledDTO.getOmGpuInfo());

        TaskTypeEntity taskTypeEntity = abilityRpcService.addAbility(abilityInstalledInfo);
        //判断是否是比对算法
        if (AbilityApiTypeEnum.COMPARE.getCodeInteger().equals(abilityInstalledDTO.getAbilityEnum())) {
            log.info("初始化比对算法");
            comparisonAbilityInitService.initComparisonAbilityIndex(taskTypeEntity.getTaskTypeCode(), orgCode);
        }

        taskTypeEntity.setIsdel(taskTypeEntity.getDel());
        stringRedisTemplate.opsForValue().set(RedisKey.INSTALL_CALL_BACK + taskTypeEntity.getTaskTypeCode(), reqVO.getInstallCallbackUrl());
        taskTypeEntity.setApplicationConfigJson(abilityInstalledInfo.getApplicationConfigJson());
        AbilityInstallResultDTO abilityInstallResult = new AbilityInstallResultDTO();
        abilityInstallResult.setAppName(taskTypeEntity.getName());
        abilityInstallResult.setTaskTypeCode(taskTypeEntity.getTaskTypeCode());
        abilityInstallResult.setOmGpuType(abilityInstalledDTO.getOmGpuType());
        abilityInstallResult.setOmResourceNeed(abilityInstalledDTO.getOmResourceNeed());
        return abilityInstallResult;
    }

    @Override
    public OmInfoDTO getOmInfo(OmInfoGetDTO omInfoGet) {

        String omFileName = omInfoGet.getFileName().substring(omInfoGet.getFileName().lastIndexOf("/") + 1);
        String filePath = omInfoGet.getFileName().substring(0, omInfoGet.getFileName().lastIndexOf("/") + 1);
        if (StrUtil.isBlank(omFileName) || StrUtil.isBlank(filePath)) {
            log.error("安装文件参数缺失:{},{},{}", omInfoGet.getFileName(), omFileName, filePath);
            throw new AiAutomatedException("安装失败，安装文件参数缺失");
        }
        OmAbilityInstallDTO omAbilityInstallDTO = new OmAbilityInstallDTO();
        omAbilityInstallDTO.setOmFileName(omFileName);
        omAbilityInstallDTO.setFilePath(filePath);
        omAbilityInstallDTO.setAbilityEnum(omInfoGet.getAbilityEnum());
        omAbilityInstallDTO.setTaskTypeCode(omInfoGet.getTaskTypeCode());

        //算法解析
        OmInfoDTO abilityInstalledDTO = abilityAutomatedRpcService.getOmAbilityInfo(omAbilityInstallDTO);
        log.info("om算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));

        return abilityInstalledDTO;
    }

    @Override
    public String install(AppManageReqVO reqVO, JSONObject decryptData, String authCode) {

        Assert.hasText(reqVO.getFileName(), "om下载地址不能为空");
        Assert.hasText(reqVO.getInstallCallbackUrl(), "om包安装回调地址不能为空");

        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        // 导入的时候校验当前超管或者租户的应用数量是否已经超出安装限制
        if (tUserAuthentication.getIsAdmin() == 0) {
            Integer appLimit = decryptData.getInteger("appLimit");
            //fyx备注后改
            Integer count = tTaskTypeService.countByUser();
            if (count >= appLimit) {
                throw new AppManageException("500", "应用数量已达上限，无法继续安装");
            }
        }
        //获取用户信息 fyx备注后改
        String orgCode = tUserAuthentication.getAuthenticationId();

        String omFileName = reqVO.getFileName().substring(reqVO.getFileName().lastIndexOf("/") + 1);
        String filePath = reqVO.getFileName().substring(0, reqVO.getFileName().lastIndexOf("/") + 1);

        // 文件路径
        Map<String, String> paramMap = new HashMap<>();

        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");

        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }
        Boolean isAdmin = true;
        if (tUserAuthentication.getIsAdmin() == 0) {
            isAdmin = false;
        }
        OmAbilityInstallDTO omAbilityInstallDTO = new OmAbilityInstallDTO();
        omAbilityInstallDTO.setOmFileName(omFileName);
        omAbilityInstallDTO.setFilePath(filePath);
        omAbilityInstallDTO.setDeviceIdentity(deviceIdentity);
        omAbilityInstallDTO.setAppKey(key);
        omAbilityInstallDTO.setCallBackUrl(omPageCallbackPrcoperties.getInstallCallback());//"http://vos-kernel-business.vos:30002/app/manage/install/callback"
        omAbilityInstallDTO.setIsAdmin(isAdmin);
        // 请求汇智
        LicenseDataDto abilityInstalledDTO = abilityAutomatedRpcService.omAbilityInstall(omAbilityInstallDTO);
        log.info("注册算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));
        abilityInstalledDTO.setExpireDate(abilityInstalledDTO.getExpireDate() + " 23:59:59");
        abilityInstalledDTO.setCreateTime(LocalDateTime.now().toString());
        AbilityInstalledDTO abilityInstalledInfo = licenseDataDtoMapper.abilityInstalledInfo(abilityInstalledDTO);
        abilityInstalledInfo.setAppKey(key);
        abilityInstalledInfo.setAuId(orgCode);
        if (tUserAuthentication.getIsAdmin() == 1) {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType());
        } else {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.omImport.getAbilityType());
        }

        abilityInstalledInfo.setIsMatchType(abilityInstalledDTO.getIsMatchType());
        abilityInstalledInfo.setMatchUploadUrl(abilityInstalledDTO.getMatchUploadUrl());
        abilityInstalledInfo.setOmGpuInfo(abilityInstalledDTO.getOmGpuInfo());

        TaskTypeEntity taskTypeEntity = abilityRpcService.addAbility(abilityInstalledInfo);
        //判断是否是比对算法
        if (AbilityApiTypeEnum.COMPARE.getCodeInteger().equals(abilityInstalledDTO.getAbilityEnum())) {
            log.info("初始化比对算法");
            comparisonAbilityInitService.initComparisonAbilityIndex(taskTypeEntity.getTaskTypeCode(), orgCode);
        }

        taskTypeEntity.setIsdel(taskTypeEntity.getDel());
        stringRedisTemplate.opsForValue().set(RedisKey.INSTALL_CALL_BACK + taskTypeEntity.getTaskTypeCode(), reqVO.getInstallCallbackUrl());
        taskTypeEntity.setApplicationConfigJson(abilityInstalledInfo.getApplicationConfigJson());
        return JSONObject.toJSONString(taskTypeEntity);
    }

    @Override
    public String installV3(JSONObject decryptData, String authCode) {

        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        // 导入的时候校验当前超管或者租户的应用数量是否已经超出安装限制
        if (tUserAuthentication.getIsAdmin() == 0) {
            Integer appLimit = decryptData.getInteger("appLimit");
            //fyx备注后改
            Integer count = tTaskTypeService.countByUser();
            if (count >= appLimit) {
                throw new AppManageException("500", "应用数量已达上限，无法继续安装");
            }
        }
        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");
        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }

        //获取用户信息 fyx备注后改
        String orgCode = tUserAuthentication.getAuthenticationId();

        V3AbilityInstallUseConfigDTO v3AbilityInstallUseConfigDTO = new V3AbilityInstallUseConfigDTO();
        v3AbilityInstallUseConfigDTO.setAppKey(key);
        v3AbilityInstallUseConfigDTO.setCallBackUrl(omPageCallbackPrcoperties.getInstallCallback());//"http://vos-kernel-business.vos:30002/app/manage/install/callback"
        // 请求汇智
        LicenseDataDto abilityInstalledDTO = abilityAutomatedRpcService.addV3AbilityWithConfig(v3AbilityInstallUseConfigDTO);
        log.debug("注册算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));
        abilityInstalledDTO.setExpireDate(abilityInstalledDTO.getExpireDate() + " 23:59:59");
        abilityInstalledDTO.setCreateTime(LocalDateTime.now().toString());
        AbilityInstalledDTO abilityInstalledInfo = licenseDataDtoMapper.abilityInstalledInfo(abilityInstalledDTO);
        abilityInstalledInfo.setAppKey(key);
        abilityInstalledInfo.setAuId(orgCode);
        abilityInstalledInfo.setVersionNum("V3");
        abilityInstalledDTO.setDeviceIdentity(deviceIdentity);
        if (tUserAuthentication.getIsAdmin() == 1) {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType());
        } else {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.omImport.getAbilityType());
        }

        TaskTypeEntity taskTypeEntity = abilityRpcService.addAbility(abilityInstalledInfo);
        taskTypeEntity.setIsdel(taskTypeEntity.getDel());
        return JSONObject.toJSONString(taskTypeEntity);
    }

    @Override
    public String installV35(AppManageReqVO reqVO, JSONObject decryptData, String authCode) {
        Assert.hasText(reqVO.getInstallCallbackUrl(), "om包安装回调地址不能为空");
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        // 导入的时候校验当前超管或者租户的应用数量是否已经超出安装限制
        if (tUserAuthentication.getIsAdmin() == 0) {
            Integer appLimit = decryptData.getInteger("appLimit");
            //fyx备注后改
            Integer count = tTaskTypeService.countByUser();
            if (count >= appLimit) {
                throw new AppManageException("500", "应用数量已达上限，无法继续安装");
            }
        }
        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");
        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }

        //获取用户信息 fyx备注后改
        String orgCode = tUserAuthentication.getAuthenticationId();

        V35AbilityInstallUseConfigDTO v35AbilityInstallUseConfigDTO = new V35AbilityInstallUseConfigDTO();
        v35AbilityInstallUseConfigDTO.setAppKey(key);
        v35AbilityInstallUseConfigDTO.setCallBackUrl(omPageCallbackPrcoperties.getInstallCallback());
        // 请求汇智
        LicenseDataDto abilityInstalledDTO = abilityAutomatedRpcService.addV35AbilityWithConfig(v35AbilityInstallUseConfigDTO);
        log.debug("注册算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));
        abilityInstalledDTO.setExpireDate(abilityInstalledDTO.getExpireDate() + " 23:59:59");
        abilityInstalledDTO.setCreateTime(LocalDateTime.now().toString());
        AbilityInstalledDTO abilityInstalledInfo = licenseDataDtoMapper.abilityInstalledInfo(abilityInstalledDTO);
        abilityInstalledInfo.setAppKey(key);
        abilityInstalledInfo.setAuId(orgCode);
        abilityInstalledInfo.setVersionNum("V35");
        abilityInstalledDTO.setDeviceIdentity(deviceIdentity);
        if (tUserAuthentication.getIsAdmin() == 1) {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType());
        } else {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.omImport.getAbilityType());
        }

        TaskTypeEntity taskTypeEntity = abilityRpcService.addAbility(abilityInstalledInfo);
        taskTypeEntity.setIsdel(taskTypeEntity.getDel());
        stringRedisTemplate.opsForValue().set(RedisKey.INSTALL_CALL_BACK + taskTypeEntity.getTaskTypeCode(), reqVO.getInstallCallbackUrl());
        return JSONObject.toJSONString(taskTypeEntity);
    }

    @Override
    public void uninstall(AppManageReqVO reqVO) {
        Assert.hasText(reqVO.getTaskTypeCode(), "om包唯一标识不能为空");
        String appKey = DubboDataContext.getAppKeyHolder();

        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(appKey);

        TTaskType taskType = commonFun(reqVO.getTaskTypeCode());
        OmAbilityUninstallDTO omAbilityUninstallDTO = new OmAbilityUninstallDTO();
        omAbilityUninstallDTO.setOmAppId(taskType.getAbility());
        omAbilityUninstallDTO.setAppKey(appKey);
        //注册算法
        if (BooleanUtil.isTrue(taskType.getIsRegister())) {
            abilityRpcService.removeAbility(reqVO.getTaskTypeCode());
            abilityAutomatedRpcService.registerAppUninstall(omAbilityUninstallDTO);
            abilityService.deleteTaskTypeEntityByCode(taskType.getTaskTypeCode());
            abilityService.deleteAbilityEntity(taskType.getAbility());
            return;
        }

        if (ObjectUtil.isNotNull(profile)) {
            if ((profile == 1 || profile == 2) && taskType.getStatus() == 0 && !"V300101000".equals(taskType.getActionId())) {
                throw new AppManageException("500", "私有化部署请将应用下架");
            }
        }
        if (isExistTask(taskType.getTaskTypeCode())) {
            throw new AppManageException("500", "请先将应用从任务中移除");
        }

        // 如果是超管导入的om包应用，需要校验一些信息
        if (Objects.equals(taskType.getAbilityType(), AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType())) {
            // 如果是普通租户，不允许卸载超管导入的om包
            if (tUserAuthentication.getIsAdmin() == 0) {
                throw new AppManageException("500", "普通租户不允许卸载超管导入的om包");
            }
            //fyx备注后改 --是否业务方判断
            // 如果是超管卸载超管导入om包应用，需要校验关联权益
            if (tUserAuthentication.getIsAdmin() == 1) {

                LambdaQueryWrapper<TApplyEquity> lambdaQueryWrapper = Wrappers.<TApplyEquity>lambdaQuery()
                        .last("limit 100000");
                lambdaQueryWrapper.eq(TApplyEquity::getTaskTypeCode, taskType.getTaskTypeCode());
                lambdaQueryWrapper.ne(TApplyEquity::getAuthenticationId, tUserAuthentication.getAuthenticationId());
                List<TApplyEquity> tApplyEquities = tApplyEquityMapper.selectList(lambdaQueryWrapper);

                if (CollectionUtils.isNotEmpty(tApplyEquities)) {
                    throw new AppManageException("500", "当前超管导入的应用已经被分配权益，请处理后再卸载");
                }
            }
        }

        // 请求汇智
        abilityAutomatedRpcService.omAppUninstall(omAbilityUninstallDTO);
        abilityRpcService.removeAbility(reqVO.getTaskTypeCode());
        abilityService.deleteTaskTypeEntityByCode(taskType.getTaskTypeCode());
        abilityService.deleteAbilityEntity(taskType.getAbility());
//        taskService.deleteInvented(orgCode,taskType.getTaskTypeCode());
    }

    @Override
    public String update(AppManageReqVO reqVO, String authCode) {
        Assert.hasText(reqVO.getFileName(), "om包名称不能为空");
        Assert.hasText(reqVO.getTaskTypeCode(), "om包唯一标识不能为空");
        Assert.hasText(reqVO.getUpdateCallbackUrl(), "om包更新回调地址不能为空");

        TTaskType taskType = commonFun(reqVO.getTaskTypeCode());
        // 文件路径
        Map<String, String> paramMap = new HashMap<>();

        //获取用户信息 fyx备注后改
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");
        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }

        String filePath = reqVO.getFileName().substring(0, reqVO.getFileName().lastIndexOf("/") + 1);
        String omFileName = reqVO.getFileName().substring(reqVO.getFileName().lastIndexOf("/") + 1);

        // 请求更新rpc
        OmAbilityUpdateDTO updateInfo = new OmAbilityUpdateDTO();
        updateInfo.setOmAppId(taskType.getAbility());
        updateInfo.setFilePath(filePath);
        updateInfo.setOmFileName(omFileName);
        updateInfo.setDeviceIdentity(deviceIdentity);
        updateInfo.setAppKey(key);
        updateInfo.setCallBackUrl(omPageCallbackPrcoperties.getUpdateCallback());//"http://vos-kernel-business.vos:30002/app/manage/update/callback"

        LicenseDataDto packageVo = abilityAutomatedRpcService.omAbilityUpdate(updateInfo);

        taskType.setName(packageVo.getAppName());
        taskType.setAppDescription(packageVo.getAppDescription());
        taskType.setVersionNum(packageVo.getVersionNum());
        taskType.setAppCover(packageVo.getAppCover());
        taskType.setComPower(packageVo.getComPower());
        taskType.setConcurrent(packageVo.getConcurrent());
        taskType.setAuthType(packageVo.getAuthType());
        taskType.setCustomName(packageVo.getCustomName());
        taskType.setExpireDate(DateUtil.parseTime(packageVo.getExpireDate() + " 23:59:59"));
        taskType.setRemark(packageVo.getRemark());
        taskType.setOmOrgCode(packageVo.getOrgCode());
        taskType.setReflowUrl(packageVo.getReflowUrl());
        taskType.setCreateUser(packageVo.getCreateUser());
        taskType.setCreateTime(new Date());
        // 判断是否过期
        if (LocalDate.parse(packageVo.getExpireDate()).isBefore(LocalDate.now())) {
            taskType.setStatus(AppStatusEnum.ABNORMAL.type);
            taskType.setAppStatus(AppStatusEnum.EXPIRED.type);
        } else {
            taskType.setStatus(packageVo.getStatus().getStatus());
            taskType.setAppStatus(packageVo.getStatus().getStatus());
        }
        taskType.setUpdatetime(new Date());
        taskTypeMapper.updateById(taskType);
        // 更新om包里面的application_config.json内容
        if (StringUtils.isNotBlank(packageVo.getApplicationConfigJson())) {
            TTaskTypeConfigAi tTaskTypeConfigAi = new TTaskTypeConfigAi();
            tTaskTypeConfigAi.setConfig(packageVo.getApplicationConfigJson());
            LambdaUpdateWrapper<TTaskTypeConfigAi> updateWrapper = Wrappers.<TTaskTypeConfigAi>lambdaUpdate()
                    .eq(TTaskTypeConfigAi::getTaskTypeCode, taskType.getTaskTypeCode());
            tTaskTypeConfigAiMapper.update(tTaskTypeConfigAi, updateWrapper);
        }
        stringRedisTemplate.opsForValue().set(RedisKey.UPDATE_CALL_BACK + taskType.getTaskTypeCode(), reqVO.getUpdateCallbackUrl());

        return JSONObject.toJSONString(taskType);
    }

    @Override
    public void rollback(AppManageReqVO reqVO, String authCode) {
        TTaskType taskType = commonFun(reqVO.getTaskTypeCode());
        Map<String, String> paramMap = new HashMap<>();

        //获取用户信息 fyx备注后改
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");

        paramMap.put("omAppId", taskType.getAbility());
        paramMap.put("deviceIdentity", userCode);
        paramMap.put("omCallBackUrl", omHuiZhiUrl + "/app/manage/rollback/callback");
        //fyx备注后改
        // 请求汇智
        JSONObject responseJson = null;// httpToHuiZhi(taskAutomatedUrl + "/om/operation/omAppRollback", JSONObject.toJSONString(paramMap));
        Integer code = responseJson.getInteger("code");
        JSONObject data = responseJson.getJSONObject("data");
        String message = responseJson.getString("message");
        if (data == null || code != 200) {
            log.info("请求汇智接口失败");
            throw new AppManageException("500", message);
        }
        OmPackageVo packageVo = JSONObject.parseObject(data.toJSONString(), OmPackageVo.class);
        taskType.setName(packageVo.getAppName());
        taskType.setAppDescription(packageVo.getAppDescription());
        taskType.setAppId(packageVo.getAppId());
        taskType.setAppVersionId(packageVo.getAppVersionId());
        taskType.setVersionNum(packageVo.getVersionNum());
        taskType.setVersionAlias(packageVo.getVersionAlias());
        taskType.setVersionDescription(packageVo.getVersionDescription());
        taskType.setAppCover(packageVo.getAppCover());
        taskType.setComPower(packageVo.getComPower());
        taskType.setConcurrent(packageVo.getConcurrent());
        taskType.setAuthType(packageVo.getAuthType());
        taskType.setCustomName(packageVo.getCustomName());

        taskType.setExpireDate(DateUtil.parseTime(packageVo.getExpireDate() + " 23:59:59"));

        taskType.setDeviceIdentity(packageVo.getDeviceIdentity());
        taskType.setDeviceLimit(packageVo.getDeviceLimit());
        taskType.setRemark(packageVo.getRemark());
        taskType.setOmOrgCode(packageVo.getOrgCode());
        taskType.setReflowUrl(packageVo.getReflowUrl());
        taskType.setVersionId(packageVo.getAppVersionId() + "-" + packageVo.getAppId() + "-" + packageVo.getOrgCode());
        taskType.setCreateUser(packageVo.getCreateUser());

        taskType.setCreateTime(DateUtil.parseDate(packageVo.getCreateTime()));
        // 判断是否过期
        if (LocalDate.parse(packageVo.getExpireDate()).isBefore(LocalDate.now())) {
            taskType.setStatus(AppStatusEnum.ABNORMAL.type);
            taskType.setAppStatus(AppStatusEnum.EXPIRED.type);
        } else {
            taskType.setStatus(AppStatusEnum.SUCCESS.type);
            taskType.setAppStatus(AppStatusEnum.SUCCESS.type);
        }
        taskType.setUpdatetime(new Date());
        taskTypeMapper.updateById(taskType);
        //应用唯一键更改
        // 更新任务状态
        List<TTaskSub> subList = taskSubMapper.selectList(Wrappers.<TTaskSub>lambdaQuery()
                .eq(TTaskSub::getTaskTypeCode, taskType.getTaskTypeCode())
                .eq(TTaskSub::getIsdel, false));
        if (CollectionUtil.isNotEmpty(subList)) {
            List<String> collect = subList.stream().map(TTaskSub::getTaskCode).collect(Collectors.toList());
            List<TTask> taskList = taskMapper.selectList(Wrappers.<TTask>lambdaQuery()
                    .in(TTask::getTaskCode, collect)
                    .eq(TTask::getIsdel, false));
            if (CollectionUtil.isNotEmpty(taskList)) {
                taskList.forEach(e -> {
                    String taskCode = e.getTaskCode();
                    Object obj = stringRedisTemplate.opsForHash().get(RedisKey.TASK_VIDEO_KEY, taskCode + "");
                    if (Objects.nonNull(obj)) {
                        VideoTaskTimeVo taskTime = JSONObject.parseObject(obj.toString(), VideoTaskTimeVo.class);
                        List<VideoSonTaskVo> sonTaskList = taskTime.getSonTaskList();
                        sonTaskList.forEach(v -> v.setAppStatus(AppStatusEnum.SUCCESS.type));
                        taskTime.setSonTaskList(sonTaskList);
                        String toJSONString = JSONObject.toJSONString(taskTime);
                        stringRedisTemplate.opsForHash().put(RedisKey.TASK_VIDEO_KEY, taskCode + "", toJSONString);
                    }
                });
            }
        }
    }


    // 因为现在回调接口没有实际调用，所以在回退版本里面暂时不写逻辑了，不过预留接口
    @Override
    public void rollbackCallback(JSONObject jsonObject) {
    }


    @Override
    public void determine(String timestamp) {
        if (StrUtil.isNotBlank(timestamp)) {
            stringRedisTemplate.opsForValue().set(RedisKey.OM_TIMESTAMP + timestamp, "1");
        }
    }

    @Override
    public void cancel(String timestamp) {
        if (StrUtil.isNotBlank(timestamp)) {
            stringRedisTemplate.delete(RedisKey.OM_TIMESTAMP + timestamp);
        }
    }

    @Override
    public Map<String, Integer> getAbnormalCount(String orgId) {
        // 未删除且状态不是正常的应用数量
        Map<String, Integer> result = new HashMap<>();
        result.put("count", getAbnormalCountNum(orgId));
        return result;
    }

    private Integer getAbnormalCountNum(String orgId) {
        String orgCode = orgId;
        List<TTaskType> selfBuiltList = taskTypeMapper.selectList(Wrappers.<TTaskType>lambdaQuery()
                .eq(TTaskType::getOrgcode, orgCode)
                .eq(TTaskType::getIsdel, false)
                .eq(TTaskType::getAbilityType, AbilityTypeEnum.SelfBuilt.getAbilityType()));
        List<TTaskType> taskTypeList = taskTypeMapper.selectList(Wrappers.<TTaskType>lambdaQuery()
                .eq(TTaskType::getOrgcode, orgCode)
                .eq(TTaskType::getIsdel, false)
                .isNotNull(TTaskType::getAppId)
                .notIn(TTaskType::getAppStatus, 0, 1, 4));
        return taskTypeList.size() + selfBuiltList.size();
    }

    public TTaskType commonFun(String taskTypeCode) {
        if (StringUtils.isEmpty(taskTypeCode)) {
            throw new AppManageException("500", "应用唯一值不能为空");
        }
        TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery().eq(TTaskType::getTaskTypeCode, taskTypeCode).eq(TTaskType::getIsdel, false));
        if (taskType == null) {
            throw new AppManageException("500", "未匹配到应用");
        }
        return taskType;
    }

    //应用唯一键更改
    @Override
    public boolean isExistTask(String taskTypeCode) {
        // 应用是否添加到任务中
        List<TTaskSub> taskSubList = taskSubMapper.selectList(Wrappers.<TTaskSub>lambdaQuery()
                .eq(TTaskSub::getTaskTypeCode, taskTypeCode)
                .eq(TTaskSub::getIsdel, false));
        if (CollectionUtil.isNotEmpty(taskSubList)) {
            List<String> collect = taskSubList.stream().map(TTaskSub::getTaskCode).collect(Collectors.toList());
            // 任务均已被删除时
            List<TTask> taskList = taskMapper.selectList(Wrappers.<TTask>lambdaQuery()
                    .in(TTask::getTaskCode, collect)
                    .eq(TTask::getIsdel, false)
                    .isNull(TTask::getInvented));
            if (ObjectUtil.isEmpty(taskList)) {
                return false;
            }
        }
        return taskSubList.size() > 0;
    }

    @Override
    public boolean switchApp(AppManageSwitchDTO switchDTO) {
        TTaskType taskType = commonFun(switchDTO.getTaskTypeCode());
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        //启用
        if (switchDTO.getEnable() == 1) {
            if (!taskType.getStatus().equals(OmInstallStatusEnum.OFFLINE.getStatus())) {
                throw new AppManageException("500", "应用非停用状态不可启用");
            }

            // 如果是超管导入的om包应用，需要校验一些信息
            if (Objects.equals(taskType.getAbilityType(), AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType())) {
                // 如果是普通租户，不允许开启超管导入的om包
                if (tUserAuthentication.getIsAdmin() == 0) {
                    throw new AppManageException("500", "普通租户不允许开启超管导入的om包");
                }
            }
            Boolean enableOmAbility = abilityAutomatedRpcService.enableOmAbility(taskType.getAbility(), "", key);
            if (BooleanUtil.isTrue(enableOmAbility)) {
                taskType.setAppStatus(OmInstallStatusEnum.SUCCESS.getStatus());
                taskType.setStatus(OmInstallStatusEnum.SUCCESS.getStatus());
                taskTypeMapper.updateById(taskType);
                return true;
            } else {
                return false;
            }

        } else {
            if (!taskType.getStatus().equals(OmInstallStatusEnum.SUCCESS.getStatus())) {
                throw new AppManageException("500", "应用非启用状态不可停用");
            }
            // 如果是超管导入的om包应用，需要校验一些信息
            if (Objects.equals(taskType.getAbilityType(), AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType())) {
                // 如果是普通租户，不允许关闭超管导入的om包
                if (tUserAuthentication.getIsAdmin() == 0) {
                    throw new AppManageException("500", "普通租户不允许下架超管导入的om包");
                }
                // 如果是超管卸载超管导入om包应用，需要校验关联权益
                if (tUserAuthentication.getIsAdmin() == 1) {
                    LambdaQueryWrapper<TApplyEquity> lambdaQueryWrapper = Wrappers.<TApplyEquity>lambdaQuery()
                            .last("limit 100000");
                    lambdaQueryWrapper.eq(TApplyEquity::getTaskTypeCode, taskType.getTaskTypeCode());
                    lambdaQueryWrapper.ne(TApplyEquity::getAuthenticationId, tUserAuthentication.getAuthenticationId());
                    List<TApplyEquity> tApplyEquities = tApplyEquityMapper.selectList(lambdaQueryWrapper);

                    if (CollectionUtils.isNotEmpty(tApplyEquities)) {
                        throw new AppManageException("500", "当前超管导入的应用已经被分配权益，请处理后再下架");
                    }
                }
            }
            Boolean aBoolean = abilityAutomatedRpcService.disableOmAbility(taskType.getAbility(), "", key);
            if (BooleanUtil.isTrue(aBoolean)) {
                taskType.setAppStatus(OmInstallStatusEnum.OFFLINE.getStatus());
                taskType.setStatus(OmInstallStatusEnum.OFFLINE.getStatus());
                taskTypeMapper.updateById(taskType);
                abilityService.deleteTaskTypeEntityByCode(taskType.getTaskTypeCode());
                abilityService.deleteAbilityEntity(taskType.getAbility());
                return true;
            } else {
                abilityService.deleteTaskTypeEntityByCode(taskType.getTaskTypeCode());
                abilityService.deleteAbilityEntity(taskType.getAbility());
                return false;
            }
        }
    }

    @Override
    public String enable(AppManageReqVO reqVO) {

        TTaskType taskType = commonFun(reqVO.getTaskTypeCode());

        OmAppPreHandleDTO statusResetInfo = new OmAppPreHandleDTO();
        statusResetInfo.setOmAppId(taskType.getAbility());
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);
        statusResetInfo.setAppKey(key);
//        statusResetInfo.setCallBackUrl();
        //启用
        if (reqVO.getEnable() == 1) {
            Assert.hasText(reqVO.getEnableCallbackUrl(), "om包上架回调地址不能为空");

            if (taskType.getStatus().equals(OmInstallStatusEnum.INSTALLING.getStatus())) {
                throw new AppManageException("500", "应用上架中，请稍等。");
            }
            if (taskType.getStatus().equals(OmInstallStatusEnum.SUCCESS.getStatus())) {
                throw new AppManageException("500", "已上架应用不可重复上架！");
            }
            // 如果是超管导入的om包应用，需要校验一些信息
            if (Objects.equals(taskType.getAbilityType(), AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType())) {
                // 如果是普通租户，不允许开启超管导入的om包
                if (tUserAuthentication.getIsAdmin() == 0) {
                    throw new AppManageException("500", "普通租户不允许开启超管导入的om包");
                }
            }
            statusResetInfo.setCloseType(false);
            statusResetInfo.setCallBackUrl(omPageCallbackPrcoperties.getEnableCallback());
            // 请求汇智
            log.debug("调用上架参数：{}", JSONObject.toJSONString(statusResetInfo));
            abilityAutomatedRpcService.omAbilityStatusReset(statusResetInfo);
            abilityRpcService.updateAbilityEnableStatus(reqVO.getTaskTypeCode(), true);
            taskType.setAppStatus(OmInstallStatusEnum.INSTALLING.getStatus());
            taskType.setStatus(OmInstallStatusEnum.INSTALLING.getStatus());
            taskTypeMapper.updateById(taskType);
            stringRedisTemplate.opsForValue().set(RedisKey.ENABLE_CALL_BACK + taskType.getTaskTypeCode(), reqVO.getEnableCallbackUrl());

            return "上架成功，请等待应用上架完毕。";
        } else if (reqVO.getEnable() == 0) {
            if (isExistTask(taskType.getTaskTypeCode())) {
                throw new AppManageException("500", "请先将应用从任务中移除");
            }

            // 如果是超管导入的om包应用，需要校验一些信息
            if (Objects.equals(taskType.getAbilityType(), AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType())) {
                // 如果是普通租户，不允许关闭超管导入的om包
                if (tUserAuthentication.getIsAdmin() == 0) {
                    throw new AppManageException("500", "普通租户不允许下架超管导入的om包");
                }
                // 如果是超管卸载超管导入om包应用，需要校验关联权益
                if (tUserAuthentication.getIsAdmin() == 1) {
                    LambdaQueryWrapper<TApplyEquity> lambdaQueryWrapper = Wrappers.<TApplyEquity>lambdaQuery()
                            .last("limit 100000");
                    lambdaQueryWrapper.eq(TApplyEquity::getTaskTypeCode, taskType.getTaskTypeCode());
                    lambdaQueryWrapper.ne(TApplyEquity::getAuthenticationId, tUserAuthentication.getAuthenticationId());
                    List<TApplyEquity> tApplyEquities = tApplyEquityMapper.selectList(lambdaQueryWrapper);

                    if (CollectionUtils.isNotEmpty(tApplyEquities)) {
                        throw new AppManageException("500", "当前超管导入的应用已经被分配权益，请处理后再下架");
                    }
                }
            }

            statusResetInfo.setCloseType(true);
            log.debug("调用下架参数：{}", JSONObject.toJSONString(statusResetInfo));
            abilityAutomatedRpcService.omAbilityStatusReset(statusResetInfo);
            abilityRpcService.updateAbilityEnableStatus(reqVO.getTaskTypeCode(), false);
            taskType.setAppStatus(OmInstallStatusEnum.OFFLINE.getStatus());
            taskType.setStatus(OmInstallStatusEnum.OFFLINE.getStatus());
            taskTypeMapper.updateById(taskType);
            abilityService.deleteTaskTypeEntityByCode(taskType.getTaskTypeCode());
            abilityService.deleteAbilityEntity(taskType.getAbility());
            return "下架成功";
        } else {
            throw new AppManageException("500", "enable参数不合法");
        }
    }

    @Override
    public CalculateMsgListResp calculateMsgList(CalculateMsgListReq req) {

        String authId = DubboDataContext.getAuthIdHolder();
        String muAiTypeId = req.getMuAiTypeId();
        String taskTypeId = req.getTaskTypeId();

        TTaskTypeOrgSetting vo = getSettingByMuTypeIdAndOrgId(muAiTypeId, authId, taskTypeId);
        if (vo != null) {
            String msgDesc = vo.getMsgDesc();
            Integer msgType = vo.getMsgType();
            String msgWater = vo.getMsgWater();
            String msgCondition = vo.getMsgCondition();
            Integer orderType = vo.getOrderType();

            Long id = vo.getId();
            Boolean sequentialStatus = vo.getSequentialStatus();

            List<TaskMsgVo> list = new ArrayList<>();

            if (TaskTypeMsgEnum.ONE.getCode().equals(msgType)) {
                // 单选
                list.add(new TaskMsgVo()
                        .setMsgDesc(msgDesc)
                        .setMsgWater(msgWater)
                        .setMsgCondition(msgCondition)
                        .setStatus(true)
                        .setSort(0)
                );
            } else if (TaskTypeMsgEnum.TWO.getCode().equals(msgType)) {
                // 双选
                String[] descArray = msgDesc.split("&");
                String[] waterArray = msgWater.split("&");
                String[] conditionArray = msgCondition.split("&");

                for (int i = 0; i < descArray.length; i++) {
                    String desc = descArray[i];
                    String water = waterArray[i];
                    String condition = conditionArray[i];
                    list.add(new TaskMsgVo()
                            .setMsgDesc(desc)
                            .setMsgWater(water)
                            .setMsgCondition(condition)
                            .setStatus(i == 0)
                            .setSort(i)
                    );
                }
            }
            return new CalculateMsgListResp()
                    .setMsgType(msgType)
                    .setSequentialStatus(sequentialStatus)
                    .setId(id)
                    .setTaskMsgVoList(list)
                    .setOrderType(orderType);
        }

        return null;
    }

    @Override
    public TTaskTypeOrgSetting saveCalculateMsg(TTaskTypeOrgSetting req) {

        if (req.getId() == null) {
            throw new AppManageException("500", "id不能为空");
        } else {
            Boolean sequentialStatus = req.getSequentialStatus();
            if (sequentialStatus) {
                String typeId = req.getTypeId();
                TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery().eq(TTaskType::getTaskTypeCode, typeId));

                if (taskType == null || !taskType.getIsSequential()) {
                    throw new AppManageException("500", "该应用不支持后置处理");
                }
            }
            taskTypeOrgSettingMapper.updateById(req);

            String appKey = DubboDataContext.getAppKeyHolder();

            TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(appKey);
            if (ObjectUtil.isEmpty(tUserAuthentication)) {
                throw new AppManageException("500", "认证失败");
            }
            String orgId = tUserAuthentication.getAuthenticationId();

            // 查寻该应用对应的任务
            List<String> taskIdList = taskTypeMapper.getTaskIdListByMuId(req.getMuTypeId(), orgId);
            for (String taskCode : taskIdList) {
                taskService.resetRedisTask(taskCode);
            }
            //删除redis告警设置缓存
            Set<String> videoKeys = scanKeys("conf:video:" + "*");
            Set<String> operatorKeys = scanKeys("conf:operator:" + "*");
            Set<String> waterKeys = scanKeys("conf:water:" + "*");
            stringRedisTemplate.delete(videoKeys);
            stringRedisTemplate.delete(operatorKeys);
            stringRedisTemplate.delete(waterKeys);
        }
        return req;
    }

    /**
     * 统计数据获取
     *
     * @param pattern
     * @return
     */
    public Set<String> scanKeys(String pattern) {
        return stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = new HashSet<>();
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(100).build());
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
            return keys;
        });
    }

    /**
     * 验证地址是否有效
     *
     * @param ipAddress
     * @return
     */
    public boolean checkHealth(String ipAddress) {
        try {
            URL url = new URL(ipAddress);
            Socket socket = new Socket();
            InetSocketAddress address = new InetSocketAddress(url.getHost(), url.getPort() == -1 ? url.getDefaultPort() : url.getPort());
            socket.connect(address, 2000);
            socket.close();
            return true;
        } catch (IOException e) {
            log.error("{},连接错误:", ipAddress);
        }
        return false;
    }

    @Override
    public TTaskType syncInstall(AbilityAddWithOutOmDTO abilityAdd) {

        Pattern p = Pattern.compile("^([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\\\\\\\\/])+$");

        Matcher m = p.matcher(abilityAdd.getServerUrl());
        if (!m.matches()) {
            throw new RuntimeException("地址格式错误");
        }
        String serverUrl = abilityAdd.getServerUrl();
        if (!checkHealth(serverUrl)) {
            throw new AppManageException("500", serverUrl + ";地址连接不通");
        }
        String abilityId = abilityAdd.getAbilityId();
        String abilityName = abilityAdd.getAbilityName();
        if (abilityName.length() > 64) {
            throw new AppManageException("500", "能力名称长度不可大于32");
        }
        TTaskType type = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery().eq(TTaskType::getAbility, abilityId).eq(TTaskType::getIsdel, false));
        if (ObjectUtil.isNotEmpty(type)) {
            throw new AppManageException("500", "应用不可重复注册");
        }
//        QueryWrapper<TUserAuthentication> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda()
//                .eq(TUserAuthentication::getDelFlag, 0)
//                .eq(TUserAuthentication::getIsDisabled, 1)
//                .eq(TUserAuthentication::getIsAdmin, 1);
//        TUserAuthentication tUserAuthentication = tUserAuthenticationMapper.selectOne(queryWrapper);

        abilityAdd.setSuitTypes("1");
        abilityAdd.setType(2);
        abilityAdd.setClassifyId(26L);
        if (abilityAdd.getMaxOperator() == null) {
            abilityAdd.setMaxOperator(2);
        }
        abilityAdd.setAbilityCode(abilityAdd.getAbilityId());
        abilityAdd.setFormula("");
        abilityAdd.setOperatorId(0L);
        abilityAdd.setAbilityRemark(abilityAdd.getAbilityId());
//        abilityAdd.setAppKey(tUserAuthentication.getAppKey());
        if (BooleanUtil.isTrue(abilityAdd.getIsMatchType()) && StrUtil.isBlank(abilityAdd.getMatchUploadUrl())) {
            throw new DubboBaseException("比对类型算法需配置比对库上传服务地址");
        }
        //rpc方式调用
        AbilityEntityDTO abilityEntityDTO = abilityAutomatedRpcService.addAbilityWithOutOm(abilityAdd);

        QueryWrapper<TTaskType> qw = new QueryWrapper<>();

        qw.lambda().select(TTaskType::getAbility).isNotNull(TTaskType::getAbility).eq(TTaskType::getIsdel, 0).groupBy(TTaskType::getAbility);

        List<TTaskType> tTaskTypes = taskTypeMapper.selectList(qw);

        Set<String> abilityList = tTaskTypes.stream().map(TTaskType::getAbility).collect(Collectors.toSet());

        if (!abilityList.contains(abilityEntityDTO.getAbilityId())) {
            // om包应用版本号和回流地址
            String versionId = abilityEntityDTO.getVersionId();
            String reflowUrl = abilityEntityDTO.getReflowUrl();
            String taskTypeCode = BizIdUtil.nextId("TaskType");
            TTaskType taskType = new TTaskType().setName(abilityName).setParentid(2).setVersionNum("V1.0.0").setAbility(abilityEntityDTO.getAbilityId()).setAbilityType(AbilityTypeEnum.COMMON.getAbilityType()).setDataType(DataTypeEnum.VIDEO.getCode())
                    .setActionId(abilityEntityDTO.getAbilityId()).setMsgWater("注意！当前画面出现告警，请关注！").setMsgDesc("当前画面出现告警").setMsgType(1).setMsgCondition(abilityName).setVersionId(versionId).setReflowUrl(reflowUrl).setIsdel(false)
                    .setAddtime(new Date()).setUpdatetime(new Date()).setIscreate(true).setNeedoperation(false).setTaskTypeCode(taskTypeCode).setAppStatus(0).setStatus(0)
                    .setAbilityEnum(abilityAdd.getAbilityEnum())
                    .setCreateTime(new Date())
                    .setIsRegister(true)
                    .setChatModelName(abilityAdd.getChatModelName())
                    .setChatApiKey(abilityAdd.getChatApiKey())
                    .setOrgcode(abilityAdd.getAuthenticationId()).setIsMatchType(abilityAdd.getIsMatchType()).setMatchUploadUrl(abilityAdd.getMatchUploadUrl());
            taskTypeMapper.insert(taskType);

            TTaskTypeConfigAi configAi = new TTaskTypeConfigAi().setTaskTypeId(taskType.getId().longValue()).setBelieve(abilityConfigProperties.getBelieve()).setMagnify("1")
                    .setDisplayBelieve(abilityConfigProperties.getDisplayBelieve()).setDisplayBox(abilityConfigProperties.getDisplayBox())
                    .setConfig(abilityAdd.getApplicationJson())
                    .setCreateTime(new Date()).setUpdateTime(new Date()).setTaskTypeCode(taskTypeCode);
            tTaskTypeConfigAiMapper.insert(configAi);

            TApplyEquity tApplyEquity = new TApplyEquity();
            tApplyEquity.setAuthenticationId(abilityAdd.getAuthenticationId());
            tApplyEquity.setTaskTypeCode(taskTypeCode);
            tApplyEquityMapper.insert(tApplyEquity);
            //判断是否是比对算法
            if (AbilityApiTypeEnum.COMPARE.getCodeInteger().equals(abilityAdd.getAbilityEnum())) {
                log.info("初始化比对算法");
                comparisonAbilityInitService.initComparisonAbilityIndex(taskType.getTaskTypeCode(), abilityAdd.getAuthenticationId());
            }
            taskType.setActionId(StrUtil.subBefore(taskType.getAbility(), "-", true));
            return taskType;
        }
        return null;
    }

    @Override
    public void initComparisonAbilityIndex(String taskTypeCode, String authIdHolder) {
        comparisonAbilityInitService.initComparisonAbilityIndex(taskTypeCode, authIdHolder);
    }

    @Override
    public List<String> blockUpdate(List<Integer> ids) {

        LambdaQueryWrapper<TTaskType> query = Wrappers.<TTaskType>lambdaQuery()
                .in(TTaskType::getAppStatus, 8);
        query.in(TTaskType::getIsdel, false);
        List<TTaskType> taskTypeList = taskTypeMapper.selectList(query);

        List<String> taskTypeCodeList = taskTypeList.stream().map(TTaskType::getTaskTypeCode).collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(ids)) {
            LambdaQueryWrapper<AbilityEntity> queryWrapper = Wrappers.<AbilityEntity>lambdaQuery()
                    .in(AbilityEntity::getOperatorId, ids);
            queryWrapper.in(AbilityEntity::getDel, 0);
            List<AbilityEntity> abilityEntityList = abilityMapper.selectList(queryWrapper);

            if (ObjectUtil.isNotEmpty(abilityEntityList)) {
                List<String> abilityIdList = abilityEntityList.stream().map(AbilityEntity::getAbilityId).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(abilityIdList)) {
                    LambdaQueryWrapper<TTaskType> queryWrapperType = Wrappers.<TTaskType>lambdaQuery()
                            .in(TTaskType::getAbility, abilityIdList);
                    queryWrapperType.in(TTaskType::getIsdel, false);
                    List<TTaskType> tTaskTypes = taskTypeMapper.selectList(queryWrapperType);
                    if (ObjectUtil.isNotEmpty(tTaskTypes)) {
                        List<String> taskTypeCodes = tTaskTypes.stream().map(TTaskType::getTaskTypeCode).collect(Collectors.toList());

                        List<String> newCode = new ArrayList<>();

                        if (ObjectUtil.isNotEmpty(taskTypeCodeList)) {
                            newCode = taskTypeCodeList.stream().filter(o -> !taskTypeCodes.contains(o)).collect(Collectors.toList());
                        }


                        tTaskTypeService.lambdaUpdate().set(TTaskType::getAppStatus, 8).set(TTaskType::getUpdatetime, new Date()).in(TTaskType::getTaskTypeCode, taskTypeCodes).update();

                        if (ObjectUtil.isNotEmpty(newCode)) {
                            tTaskTypeService.lambdaUpdate().set(TTaskType::getAppStatus, 0).set(TTaskType::getUpdatetime, new Date()).in(TTaskType::getTaskTypeCode, newCode).update();
                        }

                        return taskTypeCodes;
                    } else {
                        if (ObjectUtil.isNotEmpty(taskTypeCodeList)) {
                            tTaskTypeService.lambdaUpdate().set(TTaskType::getAppStatus, 0).set(TTaskType::getUpdatetime, new Date()).in(TTaskType::getTaskTypeCode, taskTypeCodeList).update();
                        }
                    }
                }
            }
        } else {
            if (ObjectUtil.isNotEmpty(taskTypeCodeList)) {
                tTaskTypeService.lambdaUpdate().set(TTaskType::getAppStatus, 0).set(TTaskType::getUpdatetime, new Date()).in(TTaskType::getTaskTypeCode, taskTypeCodeList).update();
            }
        }
        return null;
    }

    @Override
    public String onlineInstall(AppManageReqVO reqVO, JSONObject decryptData, String authCode) {
        Assert.hasText(reqVO.getFileName(), "om包名称不能为空");
        Assert.hasText(reqVO.getActivationName(), "激活文件名称不能为空");
        Assert.hasText(reqVO.getOnlineInstallCallbackUrl(), "om包在线安装回调地址不能为空");

        //获取用户信息 fyx备注后改
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);
        // 导入的时候校验当前超管或者租户的应用数量是否已经超出安装限制
        if (tUserAuthentication.getIsAdmin() == 0) {
            Integer appLimit = decryptData.getInteger("appLimit");
            //fyx备注后改
            Integer count = tTaskTypeService.countByUser();
            if (count >= appLimit) {
                throw new AppManageException("500", "应用数量已达上限，无法继续安装");
            }
        }
        String orgCode = tUserAuthentication.getAuthenticationId();
        // 文件路径
        Map<String, String> paramMap = new HashMap<>();

        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");

        // deviceIdentity，如果是超管，那么值是系统设备标识，如果是普通租户，那么就是用户标识
        String deviceIdentity;
        if (tUserAuthentication.getIsAdmin() == 1) {
            deviceIdentity = authCode;
        } else {
            deviceIdentity = userCode;
        }
        Boolean isAdmin = true;
        if (tUserAuthentication.getIsAdmin() == 0) {
            isAdmin = false;
        }


        String filePath = reqVO.getFileName().substring(0, reqVO.getFileName().lastIndexOf("/") + 1);
        String omFileName = reqVO.getFileName().substring(reqVO.getFileName().lastIndexOf("/") + 1);
        String activeFileName = reqVO.getActivationName().substring(reqVO.getActivationName().lastIndexOf("/") + 1);

        OmAbilityInstallDTO omAbilityInstallDTO = new OmAbilityInstallDTO();
        omAbilityInstallDTO.setFilePath(filePath);
        omAbilityInstallDTO.setOmFileName(omFileName);
        omAbilityInstallDTO.setActiveFileName(activeFileName);
        omAbilityInstallDTO.setDeviceIdentity(deviceIdentity);
        omAbilityInstallDTO.setAppKey(key);
        omAbilityInstallDTO.setCallBackUrl(omPageCallbackPrcoperties.getOnlineInstallCallback());//"http://vos-kernel-business.vos:30002/app/manage/install/callback"
        omAbilityInstallDTO.setIsAdmin(isAdmin);

        // 请求安装
        LicenseDataDto abilityInstalledDTO = abilityAutomatedRpcService.omAbilityInstall(omAbilityInstallDTO);
        log.debug("注册算法返回参数：{}", JSONObject.toJSONString(abilityInstalledDTO));
        abilityInstalledDTO.setExpireDate(abilityInstalledDTO.getExpireDate() + " 23:59:59");
        abilityInstalledDTO.setCreateTime(LocalDateTime.now().toString());
        AbilityInstalledDTO abilityInstalledInfo = licenseDataDtoMapper.abilityInstalledInfo(abilityInstalledDTO);
        abilityInstalledInfo.setAppKey(key);
        abilityInstalledInfo.setAuId(orgCode);
        if (tUserAuthentication.getIsAdmin() == 1) {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.SUPER_USER_OM_IMPORT.getAbilityType());
        } else {
            abilityInstalledInfo.setAbilityType(AbilityTypeEnum.omImport.getAbilityType());
        }

        TaskTypeEntity taskTypeEntity = abilityRpcService.addAbility(abilityInstalledInfo);
        taskTypeEntity.setIsdel(taskTypeEntity.getDel());
        stringRedisTemplate.opsForValue().set(RedisKey.INSTALL_CALL_BACK + taskTypeEntity.getTaskTypeCode(), reqVO.getOnlineInstallCallbackUrl());

        return JSONObject.toJSONString(taskTypeEntity);
    }


    @Override
    public String offlineActivation(AppManageReqVO reqVO, String authCode) {
        Assert.hasText(reqVO.getActivationName(), "激活文件名称不能为空");
        Assert.hasText(reqVO.getTaskTypeCode(), "om包唯一标识不能为空");
        Assert.hasText(reqVO.getOfflineActivationCallbackUrl(), "om包激活回调地址不能为空");

        String appkey = DubboDataContext.getAppKeyHolder();
        TTaskType taskType = commonFun(reqVO.getTaskTypeCode());

        if (taskType.getStatus().equals(OmInstallStatusEnum.INSTALLING.getStatus())) {
            throw new AppManageException("500", "应用激活中，请稍等。");
        }
        if (taskType.getStatus().equals(OmInstallStatusEnum.SUCCESS.getStatus())) {
            throw new AppManageException("500", "已激活应用不可重复激活！");
        }

        //获取用户信息 fyx备注后改
        String key = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(key);

        String userCode = EnterpriseCodeUtils.getEnterpriseCode(tUserAuthentication.getAppKey(), authCode);

        Assert.hasText(userCode, "当前用户userCode不能为空");


        String filePath = reqVO.getActivationName().substring(0, reqVO.getActivationName().lastIndexOf("/") + 1);
        String activeFileName = reqVO.getActivationName().substring(reqVO.getActivationName().lastIndexOf("/") + 1);

        OmAppOfflineActiveDTO omAppOfflineActiveDTO = new OmAppOfflineActiveDTO();
        omAppOfflineActiveDTO.setFilePath(filePath);
        omAppOfflineActiveDTO.setActiveFileName(activeFileName);
        omAppOfflineActiveDTO.setOmAppId(taskType.getAbility());
        omAppOfflineActiveDTO.setDeviceIdentity(userCode);
        omAppOfflineActiveDTO.setAppKey(appkey);
        // 请求汇智
        //fyx备注后改--请求应用安装

        OmAppOfflineActiveDTO offlineActive = new OmAppOfflineActiveDTO();
        offlineActive.setFilePath(filePath);
        offlineActive.setActiveFileName(activeFileName);
        offlineActive.setOmAppId(taskType.getAbility());
        offlineActive.setDeviceIdentity(userCode);
        offlineActive.setAppKey(appkey);
        offlineActive.setCallBackUrl(omPageCallbackPrcoperties.getOfflineActivationCallback());//"http://vos-kernel-business.vos:30002/app/manage/offlineActivation/callback"
        String versionId = abilityAutomatedRpcService.omHubOfflineAbilityActive(offlineActive);

        if (StringUtils.isEmpty(versionId)) {
            throw new AppManageException("500", "激活失败");
        }
        taskType.setVersionId(versionId);
        taskType.setDeviceIdentity(userCode);
        taskType.setUpdatetime(new Date());
        taskType.setAppStatus(OmInstallStatusEnum.INSTALLING.getStatus());
        taskType.setStatus(OmInstallStatusEnum.INSTALLING.getStatus());
        taskTypeMapper.updateById(taskType);
        stringRedisTemplate.opsForValue().set(RedisKey.OFFLINEACTIVATION_CALL_BACK + taskType.getTaskTypeCode(), reqVO.getOfflineActivationCallbackUrl());

        return versionId;
    }


    public TTaskTypeOrgSetting getSettingByMuTypeIdAndOrgId(String muAiTypeId, String orgId, String taskTypeId) {
        String params = new StringBuffer()
                .append("muAiTypeId=")
                .append(muAiTypeId)
                .append("&orgId=")
                .append(orgId)
                .append("&taskTypeId=")
                .append(taskTypeId)
                .toString();

        TTaskTypeOrgSetting tTaskTypeOrgSetting = tTaskTypeRpcService.getSettingByMuTypeIdAndOrgId(muAiTypeId, orgId, taskTypeId);
        if (ObjectUtil.isNotEmpty(tTaskTypeOrgSetting)) {
            return tTaskTypeOrgSetting;
        }
        return null;
    }

}
