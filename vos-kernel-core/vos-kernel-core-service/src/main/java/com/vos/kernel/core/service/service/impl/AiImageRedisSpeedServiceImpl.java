package com.vos.kernel.core.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.vos.kernel.core.api.exception.AiRunTimeException;
import com.vos.kernel.core.service.service.IAiImageRedisSpeedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/23
 * @description: com.vos.kernel.core.service.service.impl
 */
@Slf4j
@Service
public class AiImageRedisSpeedServiceImpl implements IAiImageRedisSpeedService {

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Override
    public String getImageData(String imageKey) {
        if (StrUtil.isBlank(imageKey)) {
            throw new AiRunTimeException("获取缓存图片参数有误");
        }
        return stringRedisTemplate.opsForValue().get(imageKey);
    }

    @Override
    public void deleteImageCacheData(String imageKey) {
        if (StrUtil.isBlank(imageKey)) {
            return;
        }
        stringRedisTemplate.delete(imageKey);
    }

    @Override
    public void deleteBatchImageCacheData(List<String> imageKeys) {
        if (CollectionUtil.isEmpty(imageKeys)) {
            return;
        }
        stringRedisTemplate.delete(imageKeys);
    }
}
