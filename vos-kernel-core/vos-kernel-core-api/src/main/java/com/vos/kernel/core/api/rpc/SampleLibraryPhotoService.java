package com.vos.kernel.core.api.rpc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.vos.kernel.core.api.domain.*;
import com.vos.kernel.core.api.dto.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vos.kernel.core.api.vo.SampleLibraryPhotoVo;
import com.vos.kernel.core.api.vo.TSampleLibraryFeedbackPhotoQueryLabelVo;
import com.vos.kernel.core.api.vo.TSampleUserParam;
import com.vos.kernel.core.api.vo.TSampleUserVo;
import com.vos.kernel.core.api.vo.libraryFeedback.TSampleLibraryFeedbackPhotoQueryVideoNumAndPhotoNumVo;
import com.vos.kernel.core.api.vo.libraryFeedback.TSampleLibraryFeedbackPhotoQueryVideoVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 样本库详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
public interface SampleLibraryPhotoService extends IService<SampleLibraryPhoto> {


    PageInfo<SampleLibraryPhoto> listTSampleLibraryPhotoPage(SampleLibraryPhotoListDTO sampleLibraryPhotoListDTO);


    PageInfo<SampleLibraryPhoto> listTSampleLibraryPhoto(SampleLibraryPhotoListDTO sampleLibraryPhotoListDTO);

    List<SampleLibraryPhoto> listSampleLibraryPhoto(Long sampleId, Long resultId);

//    boolean saveSamplePhoto(SampleLibraryPhoto tSampleLibraryPhotoVo, VideoTaskResult taskResult, String orgCode);
//
//    boolean removeSampleLibraryPhoto(List<Long> samplePhotoIds, boolean flag);

    void saveSync(SampleLibraryFeedbackPhotoSaveDTO saveDTO);

    SampleLibraryFeedbackPhoto save(SampleLibraryFeedbackPhotoSaveDTO saveDTO);

    String uploadSample(String data, String operatorId);

    String copySampleImage(String data, String operatorId);

    String uploadSampleImage(String data, String operatorId);

    DeleteIBaseImageDTO getDeleteIBaseImageDTO(List<Long> idList, String name);

    List<BBox> getBBoxList(List<SampleLibraryFeedbackPhotoCoordinateDTO> photoCoordinateDTOList);

    InsertIBaseImageDTO getInsertIBaseImageDTO(List<SampleLibraryFeedbackPhotoCoordinate> coordinateList, SampleLibraryFeedbackPhoto image);

    void deleteIBaseData(List<Long> sampleLibraryPhotoIds, TaskTypeConfigVideoEntity taskTypeConfigVideo);

    Boolean saveTSampleLibraryPhoto(SampleLibraryPhotoVo sampleLibraryPhotoVo, String orgId, Long resultId);

    Boolean saveSamplePhoto(SampleLibraryPhotoVo tSampleLibraryPhotoVo, VideoTaskResultEntity taskResult, String orgCode);

    void handleCallout(List<SampleLibraryPhoto> markedOlds, List<DrawHtmlJsonVo> markedNews,
                       String keyImage, Long sampleId, Long resultId);

    Boolean removeSampleLibraryPhoto(List<Long> samplePhotoIds, boolean flag);


    /**
     * 获取应用label
     *
     * @param labelDTO
     * @return
     */
    TSampleLibraryFeedbackPhotoQueryLabelVo queryLabel(TSampleLibraryFeedbackPhotoQueryLabelDTO labelDTO);

    List<SampleLibraryPhoto> detail(List<Long> samplePhotoId);

    List<TSampleLibraryFeedbackPhotoQueryVideoVo> getCountByVideoCode(TSampleLibraryFeedbackPhotoQueryVideoNumAndPhotoNumDTO dto);

    void deleteList(List<Long> samplePhotoIds);

    IPage<TSampleUserVo> listSampleUserPage(TSampleUserParam param);
}
