package com.vos.kernel.core.api.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员样本照片表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Getter
@Setter
@TableName("t_sample_user_photo")
public class SampleUserPhotoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "sample_photo_id", type = IdType.AUTO)
    private Long samplePhotoId;

    /**
     * 样本人员id
     */
    @TableField("sample_user_id")
    private Long sampleUserId;

    /**
     * ai返回的人脸id
     */
    @TableField("sample_user_face_id")
    private String sampleUserFaceId;

    /**
     * 样本照片路径
     */
    @TableField("sample_path")
    private String samplePath;

    /**
     * 删除标记 0：未删除 1：已删除
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 1表示来源本地，2表示来源匹配样本库
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
