package com.vos.kernel.core.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * api配置输出字段表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TbApiField extends Model<TbApiField> {

	private static final long serialVersionUID =  119309811818398701L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	/**
	 * tb_api表主键
	 */
	private Long apiId;

	/**
	 * 字段名
	 */
	private String fieldName;

	/**
	 * 字段注释
	 */
	private String fieldComment;

	/**
	 * 字段类型
	 */
	private String fieldType;

	/**
	 * 输出字段名，默认和字段名相同
	 */
	private String fieldOutputName;

	/**
	 * 是否启用 1启用 0禁用
	 */
	private Integer enable;

	/**
	 * 是否删除 1删除 0没有删除
	 */
	private Integer isDeleted;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

}
