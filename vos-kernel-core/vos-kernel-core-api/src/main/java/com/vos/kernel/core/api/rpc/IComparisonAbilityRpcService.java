package com.vos.kernel.core.api.rpc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.vos.kernel.core.api.dto.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/1
 * @description: com.vos.kernel.core.api.rpc
 */
public interface IComparisonAbilityRpcService {

    /**
     * 比对库创建
     *
     * @param comparisonIndexAddParam
     * @return
     */
    ComparisonGroupDTO comparisonCreated(ComparisonIndexAddParamDTO comparisonIndexAddParam);


    /**
     * 比对库创建
     *
     * @param comparisonIndexAddParam
     * @return
     */
    ComparisonGroupDTO comparisonCreatedV2(ComparisonIndexAddV2ParamDTO comparisonIndexAddParam);


    /**
     * 比对库插入更新
     *
     * @param comparisonIndexAddParam
     * @return
     */
    ComparisonGroupDTO comparisonUpsert(ComparisonIndexUpsertParamDTO comparisonIndexAddParam);


    /**
     * 比对库更新
     *
     * @param comparisonIndexEditeParam
     * @return
     */
    ComparisonGroupDTO comparisonEdited(ComparisonIndexEditeParamDTO comparisonIndexEditeParam);

    @Transactional(rollbackFor = Exception.class)
    ComparisonGroupDTO comparisonPhotoCreate(ComparisonIndexEditeParamDTO comparisonIndexEditeParam);

    /**
     * 比对库删除
     *
     * @param comparisonIndexDeleteParam
     * @return
     */
    Boolean comparisonDeleted(ComparisonIndexDeleteParamDTO comparisonIndexDeleteParam);

    /**
     * 比对库删除
     *
     * @param comparisonIndexDeleteParam
     * @return
     */
    Boolean comparisonDeletedV2(ComparisonIndexDeleteV2ParamDTO comparisonIndexDeleteParam);

    /**
     * 比对库删除
     *
     * @param comparisonIndexDeleteParam
     * @return
     */
    Boolean comparisonDeletedIndex(ComparisonIndexDeleteV2ParamDTO comparisonIndexDeleteParam);


    /**
     * 获取比对库信息
     *
     * @param comparisonIndexGetParam
     * @return
     */
    List<ComparisonGroupDTO> comparisonListGet(ComparisonIndexGetParamDTO comparisonIndexGetParam);

    IPage<ComparisonGroupDTO> comparisonListGetList(ComparisonIndexGetParamDTO dto);


    /**
     * 卸载删除索引
     *
     * @param comparisonUninstallSample
     * @return
     */
    Boolean uninstallComparisonSample(ComparisonUninstallSampleDTO comparisonUninstallSample);
}
