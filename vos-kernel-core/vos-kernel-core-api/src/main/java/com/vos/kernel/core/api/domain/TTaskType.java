package com.vos.kernel.core.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TTaskType extends Model<TTaskType> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用唯一键
     */
    private String taskTypeCode;

    private String name;

    private Integer parentid;

    @TableField(exist = false)
    private Integer parentTypeId;

    private String ability;

    private Integer handlertype;

    private Date addtime;

    private Date updatetime;

    private Boolean isdel;

    private Boolean iscreate;

    private Boolean needoperation;

    @TableField("engineId")
    private String engineId;

    private String contains;
    /**
     * 数据类型 1文本 2视频流
     */
    private Integer dataType;

    /**
     * 行为编号
     */
    private String actionId;

    /**
     * chatModelName
     */
    private String chatModelName;

    /**
     * chatApiKey
     */
    private String chatApiKey;

    /**
     * 行为描述
     */
    private String actionDes;

    /**
     * 是否需要配置快照的图片
     */
    private Boolean needCamera;

    /**
     * 是否支持时序
     */
    private Boolean isSequential;

    /**
     * 是否审核
     */
    private Boolean isCheck;


    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 应用状态  1 正常 ；-1 异常
     */
    private Integer status;

    @TableField(exist = false)
    private Integer muAiType;

    /**
     * 场景名
     */
    @TableField(exist = false)
    private String sceneName;
    /**
     * 场景ID
     */
    @TableField(exist = false)
    private Integer sceneId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    /**
     * 提示词类型
     */
    private Integer msgType;

    /**
     * 提示词描述 用……分隔
     */
    private String msgDesc;


    /**
     * 能力类型：识别算法 0；比对算法1；视频流算法：2；v3算法：3；其他算法：99
     */
    private Integer abilityEnum;

    /**
     * 提示词水印 用……分隔
     */
    private String msgWater;

    /**
     * 提示词情况 用&分隔
     */
    private String msgCondition;

    /**
     * om包应用版本号
     */
    private String versionId;

    /**
     * 回流地址
     */
    private String reflowUrl;

    /**
     * 抽帧频率
     */
    private String frequency;

    /**
     * 协同预测
     */
    private Boolean forecast;

    /**
     * 是否隐藏
     */
    private Boolean isHide;

    /**
     * 能力类型(0普通,1串行,2大模型,3自建)
     */
    private Integer abilityType;

    @TableField(exist = false)
    private String abilityTypeText;

    /**
     * 租户数
     */
    @TableField(exist = false)
    private Integer userNums;

    /**
     * 0表示不可以，1表示可以
     */
    @TableField(exist = false)
    private Integer isRollback;

    /**
     * 机构id
     */
    private String orgcode;

    /**
     * 冷启动配置
     */
    @TableField(exist = false)
    private TTaskTypeCold taskTypeCold;

    @TableField(exist = false)
    private Integer coldTypeLimit = 1;

    /**
     * 是否已经启动
     */
    @TableField(exist = false)
    private Long servingId;


    /**
     * 是否画图
     */
    private Boolean isDraw;

    /**
     * 是否传区域点位给AI
     */
    private Boolean isPositions;

    /**
     * 设备id
     */
    @TableField(exist = false)
    private Long videoId;

    /**
     * 告警发生时间
     */
    @TableField(exist = false)
    private Date eventTime;

    /**
     * 图片
     */
    @TableField(exist = false)
    private String keyImage;

    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String videoName;

    /**
     * 任务名称
     */
    @TableField(exist = false)
    private String taskName;

    /**
     * 设备分组名称
     */
    @TableField(exist = false)
    private String classifyName;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用版本ID
     */
    private String appVersionId;

    /**
     * 版本
     */
    private String versionNum;

    /**
     * 应用描述
     */
    private String appDescription;

    /**
     * 版本别名
     */
    private String versionAlias;

    /**
     * 版本描述
     */
    private String versionDescription;

    /**
     * 应用图标
     */
    private String appCover;

    /**
     * 算力消耗
     */
    private Integer comPower;

    /**
     * 并发数
     */
    private Integer concurrent;

    /**
     * 授权类型
     */
    private Integer authType;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 有效期
     */
    private Date expireDate;

    /**
     * 设备标识
     */
    private String deviceIdentity;

    /**
     * 设备上限
     */
    private Integer deviceLimit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 生产租户ID
     */
    private String omOrgCode;

    /**
     * 应用状态
     */
    private Integer appStatus;

    /**
     * 生产者
     */
    private String createUser;

    /**
     * 应用部署包生成日期
     */
    private Date createTime;

    /**
     * 服务地址(特定情况使用)
     */
    private String serverUrl;


    /**
     * 有没有被使用
     */
    @TableField(exist = false)
    private Boolean isUsed;

    /**
     * 是否启用禁用(1: 启用,2: 禁用)
     */
    private Integer enable;

    /**
     * 是否激活(0: 激活,1: 未激活)
     */
    private Integer activationStatus;

    /**
     * 是否比对算法
     */
    private Boolean isMatchType;

    /**
     * 比对库上传地址
     */
    private String matchUploadUrl;


    /**
     * om包gpu信息
     */
    private String omGpuInfo;

    /**
     * 是否注册算法
     */
    private Boolean isRegister;

    /**
     * Aaas上架状态
     */
    private Integer modelStatus;

    /**
     * 模型厂家标签
     */
    private String modelManufacturer;

    /**
     * 模型功能标签
     */
    private String modelFeatureLabels;
}
