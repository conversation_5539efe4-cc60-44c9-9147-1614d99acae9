package com.vos.kernel.core.api.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员样本库关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Getter
@Setter
@TableName("t_sample_user")
public class SampleUserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "sample_user_id", type = IdType.AUTO)
    private Long sampleUserId;

    /**
     * 人员姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 人员id
     */
    @TableField("user_id")
    private String userId;

    /**
     * ai人脸库id
     */
    @TableField("face_group_id")
    private String faceGroupId;

    /**
     * 人脸库分组标志（默认为人脸） 1：人脸
     */
    @TableField("face_group_flag")
    private Integer faceGroupFlag;

    /**
     * 删除标记 0：未删除 1：已删除
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
