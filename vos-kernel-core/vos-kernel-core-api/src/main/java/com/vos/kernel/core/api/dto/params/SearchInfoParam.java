package com.vos.kernel.core.api.dto.params;

import com.vos.kernel.core.api.dto.VectorSearchInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SearchInfoParam implements Serializable {

    private String filterStr;
    private String uniqueFiled;
    private String url;
    private String keyValue;
    private Integer searchType;
    @ApiModelProperty("置信度分值")
    private BigDecimal score;

    private List<VectorSearchInfoDTO> vectorList;
    private List<String> sonAppSourceIdList;
}
