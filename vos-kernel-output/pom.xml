<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.vos.kernel</groupId>
        <artifactId>vos-kernel</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>vos-kernel-output</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>vos-kernel-xxljob</name>
    <description>vos-kernel-xxljob</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.linker</groupId>
            <artifactId>web-starter</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.spullara.mustache.java</groupId>
            <artifactId>compiler</artifactId>
            <version>0.9.6</version>
        </dependency>


        <!--        &lt;!&ndash;xxl-job&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.vos.kernel.common</groupId>-->
<!--            <artifactId>xxl-job-starter</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.vos.kernel</groupId>-->
<!--            <artifactId>common-storage</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; MinIO &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.minio</groupId>-->
<!--            <artifactId>minio</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; AWS S3 兼容oss、minio &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.amazonaws</groupId>-->
<!--            <artifactId>aws-java-sdk-s3</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>druid-spring-boot-starter</artifactId>-->
<!--        </dependency>-->


<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;mybatis-plus代码生成器&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-generator</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.freemarker</groupId>-->
<!--            <artifactId>freemarker</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>mysql</groupId>-->
<!--            <artifactId>mysql-connector-java</artifactId>-->
<!--        </dependency>-->


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.linker.omos</groupId>
            <artifactId>omos-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.apache.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.apache.dubbo</groupId>-->
<!--            <artifactId>dubbo-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.apache.dubbo</groupId>-->
<!--            <artifactId>dubbo-registry-nacos</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.linker</groupId>-->
<!--            <artifactId>core-base</artifactId>-->
<!--            <version>1.0.4-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.vos.kernel.data</groupId>-->
<!--            <artifactId>vos-kernel-data-api</artifactId>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <!-- maven 打包时跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.yaml</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <mainClass>com.vos.kernel.xxljob.XxljobApplication</mainClass>
                            <finalName>${project.artifactId}</finalName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
<!--            &lt;!&ndash; maven 打包时跳过测试 &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <version>2.22.2</version>-->
<!--                <configuration>-->
<!--                    <skip>true</skip>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.8.1</version>-->
<!--            </plugin>-->

<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-jar-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <archive>-->
<!--                        <manifest>-->
<!--                            <addClasspath>true</addClasspath>-->
<!--                            &lt;!&ndash; 运行时需要额外加载读取 jar 包的位置 &ndash;&gt;-->
<!--                            <classpathPrefix>libs/</classpathPrefix>-->
<!--                            <mainClass>com.vos.kernel.xxljob.XxljobApplication</mainClass>-->
<!--                        </manifest>-->
<!--                    </archive>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-dependency-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>copy</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>copy-dependencies</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            &lt;!&ndash; 将 jar 包复制到 mvn 项目的构建路径下，例如 项目名/target/libs/ 目录下&ndash;&gt;-->
<!--                            <outputDirectory>${project.build.directory}/libs</outputDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

        </plugins>
    </build>

</project>
