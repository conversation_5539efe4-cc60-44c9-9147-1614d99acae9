package com.vos.kernel.xxljob.models;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年02月14日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class OutputParameters implements Serializable {

    /**
     * 输入类型：输入/自定义
     */
    private String outputType;

    /**
     * 自定义输入类型时传,否则为null
     */
    private String outputTypeName;

    /**
     * 输出内容
     */
    private String outputContent;


    /**
     * true为开启流式输出
     */
    private boolean stream;

    /**
     * 通道默认写这个值
     */
    private String channel;

    /**
     *输入变量列表
     */
    private List<CustomInput> customInput;


}
