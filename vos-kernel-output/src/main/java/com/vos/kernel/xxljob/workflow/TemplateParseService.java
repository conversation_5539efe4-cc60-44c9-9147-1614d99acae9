package com.vos.kernel.xxljob.workflow;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.utils.Md5Utils;
import com.vos.kernel.xxljob.models.CustomInput;
import com.vos.kernel.xxljob.models.OutputParameters;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025年02月14日
 * @version: 1.0
 * @description: TODO
 */
@Service
public class TemplateParseService {

    /**
     * 模版工厂
     */
    private MustacheFactory mf = new DefaultMustacheFactory();


    /**
     * 模版缓存
     */
    private ConcurrentHashMap<String, Mustache> templateMap = new ConcurrentHashMap<>(32);

    Pattern pattern = Pattern.compile("\\{\\{([^}]+)\\}\\}");

    /**
     * 解析模版
     *
     * @param message
     * @return
     */
    public String parseTemplate(OutputParameters message) {
        // 渲染模板
        StringWriter writer = new StringWriter();
        try {
            // 定义模板字符串
            String templateString = message.getOutputContent();
            String md5 = Md5Utils.getMd5(templateString);
            Mustache mustache;
            if (templateMap.containsKey(md5)) {
                mustache = templateMap.get(md5);
            } else {
                // 创建Mustache对象
                mustache = mf.compile(new StringReader(templateString), md5);
                templateMap.putIfAbsent(md5, mustache);
            }
            List<CustomInput> customInput = message.getCustomInput();

            // 创建数据模型
            Map<String, Object> data = new HashMap<>();
            if (CollectionUtil.isNotEmpty(customInput)) {
                for (CustomInput c : customInput) {
                    data.put(c.getVariableName(), c.getValue());
                }
            }
            mustache.execute(writer, data);
        } catch (Exception e) {
            throw new BusinessException("模版解析异常 ." + e.getMessage());
        }
        return writer.toString();
    }

    /**
     * 解析模版
     *
     * @param message
     * @return
     */
    public String parseTemplateReg(OutputParameters message) {
        // 渲染模板
        StringBuffer result = new StringBuffer();
        // 创建数据模型
        Map<String, String> variables = new HashMap<>();
        List<CustomInput> customInput = message.getCustomInput();
        if (CollectionUtil.isNotEmpty(customInput)) {
            for (CustomInput c : customInput) {
                variables.put(c.getVariableName(), c.getValue());
            }
        }
        try {
            // 定义模板字符串
            String templateString = message.getOutputContent();
            Matcher matcher = pattern.matcher(templateString);
            while (matcher.find()) {
                // 获取匹配到的占位符中的内容
                String placeholder = matcher.group(1).trim();
                // 从变量映射中查找对应的值
                String replacement = variables.getOrDefault(placeholder, "");
                if (StrUtil.isBlank(replacement)) {
                    throw new BusinessException("变量替换失败:" + placeholder);
                }
                // 替换匹配到的内容
                matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
            }
            // 追加剩余的部分
            matcher.appendTail(result);
        } catch (Exception e) {
            throw new BusinessException("模版解析异常 ." + e.getMessage());
        }
        return result.toString();
    }



}
