package com.vos.kernel.xxljob.workflow;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.linker.core.utils.IdWorker;
import com.linker.omos.client.config.WorkflowTaskContext;
import com.linker.omos.client.config.WorkflowTaskListener;
import com.linker.omos.client.config.WorkflowTaskMessageListener;
import com.vos.kernel.xxljob.models.AgentMessage;
import com.vos.kernel.xxljob.models.AgentStreamMessage;
import com.vos.kernel.xxljob.models.OutputParameters;
import com.vos.kernel.xxljob.models.TaskCustomResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2024年12月13日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
@WorkflowTaskMessageListener(taskDefName = "process_output")
public class WorkflowCustomTask implements WorkflowTaskListener<OutputParameters, TaskCustomResult> {

    @Resource
    TemplateParseService templateParseService;

    @Resource
    RedisMessageProducer redisMessageProducer;

    public static final String STREAM_NAME_KEY_PREFIX = "agent_os:conversation:output:";

    @Override
    public TaskCustomResult onTask(OutputParameters message, WorkflowTaskContext workflowTaskContext) {
        log.info("接收到消息：{}", JSON.toJSONString(message));

        String outPutStr = templateParseService.parseTemplateReg(message);
        if (StrUtil.isBlank(message.getChannel())) {
            workflowTaskContext.log("非对话模式");
            //无需调试，直接返回
            return new TaskCustomResult(outPutStr);
        }

        if (BooleanUtil.isTrue(message.isStream())) {
            //自定分割返回
            splitMsg(message.getChannel(), outPutStr, message.getOutputType(), message.getOutputTypeName());
        } else {
            oneShotMsg(message.getChannel(), outPutStr, message.getOutputType(), message.getOutputTypeName());
        }
        return new TaskCustomResult(outPutStr);
    }


    /**
     * 分割数据
     *
     * @param channel
     * @param msg
     * @param outputType
     * @param outputTypeName
     */
    private void splitMsg(String channel, String msg, String outputType, String outputTypeName) {
        List<String> strings = splitStringRandomly(msg);
        String type = "custom".equals(outputType) && StrUtil.isNotBlank(outputTypeName) ? outputTypeName : "node_output";
        String sessionId = STREAM_NAME_KEY_PREFIX + channel;
        String s = String.valueOf(IdWorker.nextId());
//        Random random = new Random();
        for (String msgSplit : strings) {
            AgentStreamMessage agentStreamMessage = new AgentStreamMessage();
            agentStreamMessage.setEvent("conversation.message.delta");
            AgentMessage agentMessage = new AgentMessage();
            agentMessage.setSession(s);
            agentMessage.setConversationId(channel);
            agentMessage.setEndTime(new Date().getTime());
            agentMessage.setStatus("delta");
            agentMessage.setIsFinish(false);
            agentMessage.setContentType("text");
            agentMessage.setContent(msgSplit);
            agentMessage.setType(type);
            agentStreamMessage.setData(agentMessage);
            redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage);
//            ThreadUtil.safeSleep(random.nextInt(10)+3);
        }
        redisMessageProducer.sendSimpleMessage(sessionId, buildCompletedStreamMsg(sessionId, channel, type));
        redisMessageProducer.streamMessageExpire(sessionId);
    }

    /**
     * 3-10随机字符
     *
     * @param input
     * @return
     */
    private static List<String> splitStringRandomly(String input) {
        List<String> resultList = new ArrayList<>();
        Random random = new Random();
        int index = 0;
        while (index < input.length()) {
            // 生成 3 到 10 之间的随机数
            int length = random.nextInt(5) + 2;
            if (index + length > input.length()) {
                length = input.length() - index;
            }
            resultList.add(input.substring(index, index + length));
            index += length;
        }
        return resultList;
    }


    /**
     * 一次性吐出
     *
     * @param channel
     * @param msg
     */
    private void oneShotMsg(String channel, String msg, String outputType, String outputTypeName) {

        String type = "custom".equals(outputType) && StrUtil.isNotBlank(outputTypeName) ? outputTypeName : "node_output";
        String sessionId = STREAM_NAME_KEY_PREFIX + channel;
        AgentStreamMessage agentStreamMessage = new AgentStreamMessage();
        AgentMessage agentMessage = new AgentMessage();
        agentMessage.setSession(String.valueOf(IdWorker.nextId()));
        agentMessage.setConversationId(channel);
        agentMessage.setEndTime(new Date().getTime());
        agentStreamMessage.setEvent("conversation.message.delta");
        agentMessage.setStatus("delta");
        agentMessage.setIsFinish(false);
        agentMessage.setContentType("text");
        agentMessage.setContent(msg);
        agentMessage.setType(type);
        agentStreamMessage.setData(agentMessage);
        redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage);
        redisMessageProducer.sendSimpleMessage(sessionId, buildCompletedStreamMsg(sessionId, channel, type));
        redisMessageProducer.streamMessageExpire(sessionId);
    }


    /**
     * 结束信息
     *
     * @param sessionId
     * @param channel
     * @return
     */
    private AgentStreamMessage buildCompletedStreamMsg(String sessionId, String channel, String type) {
        AgentStreamMessage agentStreamMessage = new AgentStreamMessage();
        AgentMessage agentMessage = new AgentMessage();
        agentMessage.setSession(sessionId);
        agentMessage.setConversationId(channel);
        agentMessage.setEndTime(new Date().getTime());
        agentStreamMessage.setEvent("conversation.message.completed");
        agentMessage.setStatus("completed");
        agentMessage.setIsFinish(true);
        agentMessage.setContentType("text");
        agentMessage.setContent("");
        agentMessage.setType(type);
        agentStreamMessage.setData(agentMessage);
        return agentStreamMessage;
    }

}
