server:
  port: 8087
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    threads:
      # 最大工作线程数，默认200, 4核8g内存，线程数经验值800
      # 操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好。
      max: 200
      # 最小工作空闲线程数，默认10, 适当增大一些，以便应对突然增长的访问量
      min-spare: 10
    # 等待队列长度，默认100
    accept-count: 100
    max-connections: 100

spring:
  application:
    name: kernel-xxljob
#  datasource: #连接池配置
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *************************************************************************************************************************
#    username: root
#    password: Ff1z@TOFr^iwd%Ra
  ######################## Nacos ########################
  cloud:
    nacos:
      config:
        server-addr: *********:8848
        file-extension: yaml
        namespace: 69f8ac19-e75e-41f9-9df7-35f97299dda5
        extension-configs:
          - dataId: task-poll-config.yaml
            group: DEFAULT_GROUP
            refresh: false
        enabled: false
      discovery:
        server-addr: **********:8848
        namespace: 69f8ac19-e75e-41f9-9df7-35f97299dda5
        enabled: false
  #        ip: ${DUBBO_IP_TO_REGISTRY}
  #        port: ${DUBBO_PORT_TO_REGISTRY}


##分布式调度任务
#xxl:
#  job:
#    enabled: false
#    admin:
#      addresses: http://***********:32533/xxl-job-admin
#    executor:
#      appname: dev-xxl-executor-server
#      ip:
#      port: 9790
#      logpath: data/applogs/xxl-job/jobhandler
#      logretentiondays: 30
#      executeTimeout: 600
#    access-token: default_token
#
#
#linker-storage:
#  default-platform: minio
#  minio:
#    - platform: minio # 存储平台标识
#      enable-storage: true  # 启用存储
#      accessKey: hhsA2oWVw05CknHg
#      secretKey: AFQUnjPT3QQ7B22LiGdZax2VvzwZG6hK
#      endPoint: http://**********:9000
#      domain: https://test-om.linker.cc/minio/temp/
#      bucketName: temp

#dubbo:
#  application:
#    name: kernel-xxljob
#    register-mode: instance
#    version: 1.0.0
#  protocol:
#    name: dubbo
#    port: -1
#  # 注册中心配置
#  registry:
#    id: kernel-xxljob
#    parameters:
#      #      命名空间
#      namespace: d4e4c1f2-f609-4870-a2ad-474812ff4012
#    address: nacos://**********:8848
#  #  metadata-report:
#  #    address: nacos://**********:8848
#  # 消费者相关配置
#  consumer:
#    # 支持校验注解
#    validation: true
#    # 超时时间
#    timeout: 30000
#    # 初始化检查
#    check: false
#    filter: dubboTraceFilter

workflow:
  client:
    enabled: true
    endpoint: http://***********:30002/
    app-secret: ZDFhZGUyMzM1OTU2ZDdlZjFhNDdAVHVoZ0Y5NDA=
    tasks:
      - task-def-name: test-custom-tasks
        closed: true


























