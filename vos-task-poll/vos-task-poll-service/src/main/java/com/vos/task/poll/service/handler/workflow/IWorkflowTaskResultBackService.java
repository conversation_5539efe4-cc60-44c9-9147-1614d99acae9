package com.vos.task.poll.service.handler.workflow;

import com.vos.kernel.common.workflow.WorkflowCallBackContent;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
public interface IWorkflowTaskResultBackService {

    /**
     * 回调编排，更改状态、输出结果
     *
     * @param abilityId
     * @param url
     * @param workflowCallBackContent
     */
    void workflowTaskResultBack(Long abilityId, String url, WorkflowCallBackContent workflowCallBackContent);
}
