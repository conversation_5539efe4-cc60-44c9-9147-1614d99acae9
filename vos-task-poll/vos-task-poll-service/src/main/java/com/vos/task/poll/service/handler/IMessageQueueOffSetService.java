package com.vos.task.poll.service.handler;

import org.apache.rocketmq.common.message.Message;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/21
 * @description: com.vos.task.poll.service.handler
 */
public interface IMessageQueueOffSetService {

    /**
     * 获取消费进度
     *
     * @param queueOffsetKey
     * @return
     */
    long getMessageQueueOffset(String queueOffsetKey);


    /**
     * 更正消费进度
     *
     * @param queueOffsetKey
     * @param offSet
     */
    void setMessageQueueOffset(String queueOffsetKey, Long offSet);


    /**
     * 设置最大消费进度
     * @param offsetTable
     */
    void setRatioAbilityMaxOffset(HashMap<String, String> offsetTable);

    /**
     * 获取队列消费进度
     *
     * @return
     */
    HashMap<String, Long> getAbilityConsumeOffset();

    /**
     * 设置最大消费进度
     *
     * @return
     */
    HashMap<String, Long> getRatioAbilityMaxOffset();

    /**
     * 保存数据防止丢失
     *
     * @param scheduleKey
     * @param list
     */
    void saveCache(String scheduleKey, List<Message> list);

    /**
     * 加载数据防止丢失
     *
     * @param scheduleKey
     * @return
     */
    List<Message> loadCache(String scheduleKey);

    /**
     * 删除缓存
     *
     * @param scheduleKey
     */
    void removeCache(String scheduleKey);
}
