package com.vos.task.poll.service.handler.ability;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.linker.log.enums.MetricPattern;
import com.linker.log.utils.TraceLogUtil;
import com.linker.omos.client.config.ItemCodeEnum;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.kernel.common.utils.EncryptUtil;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.manage.api.rpc.TaskRpcService;
import com.vos.task.poll.api.entity.dto.CheckServingDto;
import com.vos.task.poll.api.entity.dto.SchedulingCenterDto;
import com.vos.task.poll.service.config.FileProperties;
import com.vos.task.poll.service.handler.AbilityHttpConcurrentLimit;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年09月03日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public abstract class AbstractAbilityClient extends ModelClient implements AbilityClient {

    @Value("${check.serving.url}")
    private String checkServingUrl;

    @Resource
    FileProperties fileProperties;

    /**
     * ai 异步回调地址
     */
    @Value("${async.back.url}")
    private String asyncBackUrl;

    /**
     * 算法类型
     */
    protected List<Integer> abilityType;

    @Resource
    AbilityClientHolder abilityClientHolder;

    @Resource
    ITaskCacheRpcService taskCacheRpcService;

    @DubboReference
    private TaskRpcService taskRpcService;

    /**
     * 主机名
     */
    protected String hostName;

    @PostConstruct
    private void init() {
        abilityClientHolder.register(abilityType, this);
        this.hostName = System.getenv("HOSTNAME");
    }


    @Override
    public void requestAbility(TaskSub taskSub, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {
        doRequestAbility(taskSub, abilityHttpConcurrentLimit, abilityRequestInfo);
    }

    @Override
    public void fastFail(TaskSub taskSub) {
        if (StrUtil.isNotBlank(taskSub.getExtend())) {
            int i = RandomUtil.randomInt(1, 10);
            JSONObject jsonObject = JSON.parseObject(taskSub.getExtend());
            String extra = jsonObject.getString("deviceId") + "," + jsonObject.getString("bizTaskId") + "," + jsonObject.getString("abilityCode");
            TraceLogUtil.trace(ItemCodeEnum.SF_8EGE0ARZY6R.getCode(), MetricPattern.CUSTOM, "", i, "0", jsonObject.getLong("frameBizTime"), extra);
        }
        fastFailHandler(taskSub);
    }

    /**
     * 请求算法
     *
     * @param taskSub
     * @param abilityHttpConcurrentLimit
     * @param abilityRequestInfo
     */
    protected abstract void doRequestAbility(TaskSub taskSub, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo);

    /**
     * 请求回调
     *
     * @param taskSub
     */
    protected abstract void callBackNormal(TaskSub taskSub);

    /**
     * 快速失败
     *
     * @param taskSub
     */
    public abstract void fastFailHandler(TaskSub taskSub);

    /**
     * 构造算法请求参数
     *
     * @param taskSub
     * @param asyncType
     * @return
     */
    protected JSONObject buildRequestData(TaskSub taskSub, Integer asyncType) {
        String requestJson = taskSub.getRequestJson();
        JSONObject requestData = JSON.parseObject(requestJson);

        // 调度中心自定义参数
        SchedulingCenterDto schedulingCenterDto = new SchedulingCenterDto();
        schedulingCenterDto.setTenantId(taskSub.getTenantId().toString());
        schedulingCenterDto.setAppId(taskSub.getAppId().toString());
        schedulingCenterDto.setTaskId(taskSub.getTaskId().toString());
        schedulingCenterDto.setSubTaskId(taskSub.getSubTaskId().toString());
        schedulingCenterDto.setAbilityId(taskSub.getAbilityId().toString());

        //调用异步底层能力需要自定义组装心跳和回调接口地址参数
        if (asyncType == 1) {
            schedulingCenterDto.setCallback(asyncBackUrl);
        }

        // 组装校验serving的参数
        CheckServingDto checkServingDto = new CheckServingDto();

        String checkUrl = fileProperties != null && StrUtil.isNotBlank(fileProperties.getInnerAddress()) ? fileProperties.getInnerAddress() + checkServingUrl : checkServingUrl;
        String url = String.format(checkUrl, taskSub.getTenantId().toString(), taskSub.getAppId().toString(), taskSub.getOmAppId());
//        log.debug("解密url" + url);
        checkServingDto.setUrl(EncryptUtil.encryptStr(url));
        schedulingCenterDto.setCheckServingDto(checkServingDto);
        requestData.put("schedulingCenter", schedulingCenterDto);
        return requestData;
    }

    /**
     * base64传递图片；需要替换图片
     *
     * @param requestData
     * @return
     */
    protected Map<String, String> base64ImageMap(JSONObject requestData, Boolean clearRightNow) {

        //如果base64传递图片；需要替换图片
        JSONArray src = requestData.getJSONArray("src");
        Map<String, String> imgMap = new HashMap<>(16);
        if (null == src || src.size() == 0) {
            return imgMap;
        }
        for (int i = 0; i < src.size(); i++) {
            JSONObject jsonObject = src.getJSONObject(i);
            String srcType = jsonObject.getString("srcType");
            if ("base64".equals(srcType)) {
                String data = jsonObject.getString("data");
                //不能立即删除，等待所有任务执行完成后删除
                if (!data.equals("") && !data.contains("base64") && data.length() < 100) {
                    String base64 = taskCacheRpcService.getBase64Cache(data, clearRightNow);
                    if (log.isDebugEnabled()) {
                        log.debug("=======>data:{},获取到base64信息：{}<========", data, StrUtil.subWithLength(base64, 0, 200));
                    }
                    //标识是否base64图片
                    imgMap.put("base64", data);
                    jsonObject.put("data", base64);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("获取到imgMap信息：{}", imgMap);
        }
        return imgMap;
    }

    /**
     * 异常处理
     *
     * @param taskSub
     * @param errorMsg
     */
    public void errorRequestAbilityResult(TaskSub taskSub, String errorMsg) {

        if (BooleanUtil.isTrue(taskSub.getBatchRequest())) {
            errorRequestAbilityResultForMergeTask(taskSub, errorMsg);
        } else {
            // 处理时间
            Date now = new Date();
            Date handleStartTime = taskSub.getHandleStartTime() == null ? now : taskSub.getHandleStartTime();
            taskSub.setHandleStatus(SubTaskStatusEnum.ABILITY_ERROR.getKey());
            taskSub.setHandleFinishTime(now);
            taskSub.setHandleTime((now.getTime() - handleStartTime.getTime()));
            taskSub.setUpdateTime(now);
            JSONObject jsonObject = new JSONObject();
            JSONObject errorJsonObject = new JSONObject();
            jsonObject.put("code", 500);
            errorJsonObject.put("message", errorMsg);
            jsonObject.put("body", errorJsonObject);
            jsonObject.put("isSuccess", false);
            taskSub.setRequestResult(JSON.toJSONString(jsonObject));
            taskCacheRpcService.updateTaskSubCache(taskSub);
            //回调处理
            callBackNormal(taskSub);
        }
    }


    /**
     * 批处理任务错误处理
     *
     * @param taskSub
     * @param errorMsg
     */
    protected void errorRequestAbilityResultForMergeTask(TaskSub taskSub, String errorMsg) {

        List<TaskSub> batchMergedTaskSub = taskSub.getBatchMergedTaskSub();

        //循环处理
        for (TaskSub subTask : batchMergedTaskSub) {
            subTask.setHandleStartTime(taskSub.getHandleStartTime());
            subTask.setHandleDirectByMq(taskSub.getHandleDirectByMq());
            subTask.setTaskServerRouterId(taskSub.getTaskServerRouterId());
            subTask.setRequestUrl(taskSub.getRequestUrl());
            errorRequestAbilityResult(subTask, errorMsg);
        }
    }


    /**
     * 解析算法结果然后拆解
     *
     * @param taskSubMerged
     */
    protected void mergedTaskCallBack(TaskSub taskSubMerged) {
        List<TaskSub> taskSubs = mergedTaskCallBackSplit(taskSubMerged);
        //处理单个任务
        for (TaskSub taskSub : taskSubs) {
            //更新结果
            taskCacheRpcService.updateTaskSubCache(taskSub);
            //回调
            callBackNormal(taskSub);
        }


    }

    /**
     * 拆解结果
     *
     * @param taskSubMerged
     * @return
     */
    protected List<TaskSub> mergedTaskCallBackSplit(TaskSub taskSubMerged) {
        if (log.isDebugEnabled()) {
            log.debug("进入批处理解析流程：{}", JSON.toJSONString(taskSubMerged));
        }
        List<TaskSub> batchMergedTaskSub = taskSubMerged.getBatchMergedTaskSub();
        HashMap<String, String> batchImageIdToVideoId = taskSubMerged.getBatchImageIdToVideoId();
        //合并处理结果
        String requestResult = taskSubMerged.getRequestResult();
        JSONObject requestResultObject = JSON.parseObject(requestResult);

        //处理单个任务
        for (TaskSub taskSub : batchMergedTaskSub) {
            if (SubTaskStatusEnum.FINISHED.getKey().equals(taskSubMerged.getHandleStatus())) {
                //成功调用的，解析数据
                JSONObject subResult = new JSONObject();
                subResult.put("code", requestResultObject.getInteger("code"));
                subResult.put("isSuccess", requestResultObject.getBooleanValue("isSuccess"));
                subResult.put("body", requestResultObject.getJSONObject("body"));
                subResult.put("passthroughParams", requestResultObject.getJSONObject("passthroughParams"));
                //拆解结果
                subResult.put("results", getTaskSubResult(requestResultObject.getJSONObject("results"), taskSub, batchImageIdToVideoId));
                taskSub.setRequestResult(JSON.toJSONString(subResult));
            } else {
                taskSub.setRequestResult(taskSubMerged.getRequestResult());
            }
            taskSub.setHandleDirectByMq(taskSubMerged.getHandleDirectByMq());
            taskSub.setHandleStartTime(taskSubMerged.getHandleStartTime());
            taskSub.setTaskServerRouterId(taskSubMerged.getTaskServerRouterId());
            taskSub.setRequestUrl(taskSubMerged.getRequestUrl());
            taskSub.setHandleFinishTime(taskSubMerged.getHandleFinishTime());
            taskSub.setHandleStatus(taskSubMerged.getHandleStatus());
            taskSub.setHandleTime(taskSubMerged.getHandleTime());
            taskSub.setUpdateTime(taskSubMerged.getUpdateTime());
        }
        if (log.isDebugEnabled()) {
            log.debug("解析后数据：{}", JSON.toJSONString(batchMergedTaskSub));
        }
        return batchMergedTaskSub;
    }

    /**
     * 获取任务结果
     *
     * @param requestResultObject
     * @param taskSub
     * @param batchImageIdToVideoId
     * @return
     */
    protected JSONObject getTaskSubResult(JSONObject requestResultObject, TaskSub taskSub, HashMap<String, String> batchImageIdToVideoId) {
        JSONObject subResult = new JSONObject();
        Long subTaskId = taskSub.getSubTaskId();
        //当前任务结果
        JSONArray jsonArray = requestResultObject.getJSONArray(subTaskId.toString());

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            //获取图片ID
            String imageId = jsonObject.getString("id");
            //获取视频ID
            String videoId = batchImageIdToVideoId.get(imageId);
            if (subResult.get(videoId) == null) {
                JSONArray jsonArrayi = new JSONArray();
                jsonArrayi.add(jsonObject);
                subResult.put(videoId, jsonArrayi);
            } else {
                subResult.getJSONArray(videoId).add(jsonObject);
            }
        }
        return subResult;
    }

    /**
     * 获取能力耗时
     *
     * @param result
     * @return
     */
    protected long getAbilityTook(JSONObject result) {
        try {
            return result.getJSONObject("body").getLong("took");
        } catch (Exception e) {
            return 0;
        }
    }

}
