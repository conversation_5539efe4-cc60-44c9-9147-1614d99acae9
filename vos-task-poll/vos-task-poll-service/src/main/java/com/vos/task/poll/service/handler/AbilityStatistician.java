package com.vos.task.poll.service.handler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.executor.ExecutorFactory;
import com.alibaba.nacos.common.executor.NameThreadFactory;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.enums.AppTaskCountEnum;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.poll.service.entity.AbilityCallMetricData;
import com.vos.task.poll.service.handler.metric.AbilityInstanceMinuteMetricStatistician;
import com.vos.task.poll.service.handler.metric.MetricItemEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR>
 * @date 2024年08月30日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class AbilityStatistician implements AbilityMetricStatistician, AutoCloseable {


    /**
     * 最长多久持久化一次
     */
    private long timeoutMillis = 500;

    /**
     * 最大数量时持久化一次
     */
    private int maxFlushSize = 100;

    /**
     * 最近一次持久化的时间
     */
    private volatile long lastSucTimeMillis = System.currentTimeMillis();

    private volatile long lastFailTimeMillis = System.currentTimeMillis();

    private volatile long lastFastFailTimeMillis = System.currentTimeMillis();

    /**
     * 持久化
     */
    ITaskCacheRpcService taskCacheRpcService;

    /**
     * 第一次算法调用rt持久化
     */
    private Boolean initFps = true;

    /**
     * 能力编号
     */
    private Long abilityId;

    /**
     * 定时线程：时间监控是否持久化
     */
    ScheduledExecutorService scheduledexecutorService;

    /**
     * 存储各事件的计数，比如异常总数、请求总数等
     */
    private LongAdder[] counters;

    private volatile long abilityAvgRt = 0L;
    private volatile AtomicInteger rtNum = new AtomicInteger(0);
    private volatile AtomicInteger rtBiggerNum = new AtomicInteger(0);

    /**
     * 持久化统计锁对象
     */
    private final Object successObject = new Object();
    private final Object exceptionObject = new Object();
    private final Object fastFailObject = new Object();


    /**
     * 算法实例统计容器
     */
    private ConcurrentHashMap<String, AbilityInstanceMinuteMetricStatistician> instanceMetricContainer = new ConcurrentHashMap<>();


    /**
     * 初始化
     */
    public AbilityStatistician(List<AbilityRequestUrlDTO> abilityUrlLimit, ITaskCacheRpcService taskCacheRpcService, String abilityId) {
        this.abilityId = Long.parseLong(abilityId);
        this.taskCacheRpcService = taskCacheRpcService;
        //统计纬度
        MetricItemEnum[] events = MetricItemEnum.values();
        this.counters = new LongAdder[events.length];
        //根据枚举常量初始化counter
        for (MetricItemEnum event : events) {
            counters[event.ordinal()] = new LongAdder();
        }
        //初始化算法实例统计容器
        for (AbilityRequestUrlDTO a : abilityUrlLimit) {
            String instanceId = a.getRouterId() == null ? abilityId : a.getRouterId();
            instanceMetricContainer.put(instanceId, new AbilityInstanceMinuteMetricStatistician());
        }
        //定时扫描是否持久化
        this.scheduledexecutorService = ExecutorFactory.newScheduledExecutorService(2, new NameThreadFactory("call-metric-" + "-"));
        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                tryAddException();
                tryAddRt();
                tryAddFastFail();
            } catch (Exception e) {
                log.warn("定时扫描算法成功失败持久化异常", e);
            }
        }, 0L, 500, TimeUnit.MILLISECONDS);
        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                abilityAvgRt();
            } catch (Exception e) {
                log.warn("定时扫描算法平均执行耗时异常", e);
            }
        }, 0L, 10, TimeUnit.SECONDS);
    }

    /**
     * 添加实例
     *
     * @param instance
     */
    @Override
    public void addAbilityInstanceMinuteMetric(String instance) {
        instanceMetricContainer.put(instance, new AbilityInstanceMinuteMetricStatistician());
    }


    /**
     * 获取对应统计维度的总数
     *
     * @param event
     * @return
     */
    public long get(MetricItemEnum event) {
        return counters[event.ordinal()].sum();
    }

    /**
     * 指定对应统计维度的值
     *
     * @param event
     * @param n
     */
    private void add(MetricItemEnum event, long n) {
        if (event == MetricItemEnum.RT) {
            this.rtNum.incrementAndGet();
        }
        counters[event.ordinal()].add(n);
    }


    /**
     * 重置统计
     */
    public void reset() {
        for (MetricItemEnum event : MetricItemEnum.values()) {
            counters[event.ordinal()].reset();
        }
    }

    /**
     * 重置统计
     */
    public void reset(MetricItemEnum event) {
        counters[event.ordinal()].reset();
    }

    /**
     * 获取异常总数
     *
     * @return
     */
    @Override
    public long exception() {
        return get(MetricItemEnum.EXCEPTION);
    }

    @Override
    public long fastFailNum() {
        return get(MetricItemEnum.FAST_FAIL);
    }

    /**
     * 获取成功总数
     *
     * @return
     */
    @Override
    public long success() {
        return get(MetricItemEnum.SUCCESS);
    }


    /**
     * 获取平均耗时
     *
     * @return
     */
    @Override
    public long rt() {
        return get(MetricItemEnum.RT) / rtNum.get();
    }

    /**
     * 添加异常统计
     */
    @Override
    public void addException(String abilityInstance) {
        tryAddException();
        add(MetricItemEnum.EXCEPTION, 1);
        if (instanceMetricContainer.containsKey(abilityInstance)) {
            instanceMetricContainer.get(abilityInstance).incrException();
        }
    }

    @Override
    public void addFastFail(int fastFailNum) {
        if (fastFailNum > 0) {
            tryAddFastFail();
            add(MetricItemEnum.FAST_FAIL, fastFailNum);
        }
    }

    /**
     * 添加成功统计
     */
    void tryAddFastFail() {
        synchronized (fastFailObject) {
            if (flushCondition(MetricItemEnum.FAST_FAIL) && taskCacheRpcService != null) {
                taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.FAST_FAIL_COUNT.getCode(), fastFailNum());
                lastFastFailTimeMillis = System.currentTimeMillis();
                reset(MetricItemEnum.FAST_FAIL);
            }
        }
    }


    /**
     * 添加成功统计
     */
    void tryAddException() {
        synchronized (exceptionObject) {
            if (flushCondition(MetricItemEnum.EXCEPTION) && taskCacheRpcService != null) {
                taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.FAIL_COUNT.getCode(), exception());
                lastFailTimeMillis = System.currentTimeMillis();
                reset(MetricItemEnum.EXCEPTION);
            }
        }
    }

    /**
     * 算法平均耗时
     */
    private void abilityAvgRt() {
        //未初始化或调用次数少于5次时不同步
        if (this.abilityAvgRt == 0L || rtNum.get() < 80) {
            return;
        }
        long rt = rt();
        if (log.isDebugEnabled()) {
            log.debug("计算算法平均耗时：{}，abilityAvgRt：{}", rt, abilityAvgRt);
        }
        //持久化算法耗时
        if (rt > 0 && Math.abs(rt - this.abilityAvgRt) > 15) {
            this.abilityAvgRt = rt;
            taskCacheRpcService.cacheAbilityRtAvg(abilityId, rt);
        }
        if (rtNum.get() > 2000) {
            rtNum.set(0);
            reset(MetricItemEnum.RT);
        }
//        if (MapUtil.isEmpty(instanceMetricContainer)) {
//            return 0;
//        }
//        long[] avgRts = new long[instanceMetricContainer.size()];
//        int i = 0;
//        for (AbilityInstanceMinuteMetricStatistician instance : instanceMetricContainer.values()) {
//            long l = instance.avgRt();
//            if (l > 0) {
//                avgRts[i] = l;
//                i++;
//            }
//        }
//        if (avgRts.length > 0) {
//            Arrays.sort(avgRts);
//            // 抽样，以bucket的 95% avgRt 作为 95线
//            int index = (int) Math.round((float) i * 0.9) - 1;
//            index = Math.max(index, 0);
//            long avgRt = avgRts[index];
//            long avgRtMin = avgRts[0];
//            if (avgRt > 0 && avgRt < avgRtMin * 10) {
//                //持久化算法耗时
//                taskCacheRpcService.cacheAbilityRtAvg(abilityId, avgRt);
//            }
//            return avgRt;
//        } else {
//            return 0;
//        }
//        this.abilityAvgRt
    }

    /**
     * 添加耗时统计
     *
     * @param rt
     */
    @Override
    public void addRt(String appsourceId, String abilityInstance, int sucNum, long rt, long modelTook) {
        log.info("{},{},{},算法调用rt: {},took:{},abilityAvgRt:{}", abilityId, abilityInstance, appsourceId, rt, modelTook, this.abilityAvgRt);
        if (initFps && this.rtNum.get() > 2) {
            abilityAvgRt = rt;
            initFps = false;
            Double abilityPerFps = taskCacheRpcService.getAbilityPerFps(abilityId.toString());
            if (abilityPerFps == null) {
                //统计初始化且算法耗时未初始化时
                taskCacheRpcService.cacheAbilityRtAvg(abilityId, rt);
            } else {
                abilityAvgRt = abilityPerFps.longValue();
            }
        }
        tryAddRt();
        add(MetricItemEnum.SUCCESS, sucNum == 0 ? 1 : sucNum);
        //保证均匀
        if (abilityAvgRt == 0L || rt < abilityAvgRt * 5) {
            rtBiggerNum.set(0);
            add(MetricItemEnum.RT, rt);
        } else {
            int i = rtBiggerNum.incrementAndGet();
            if (i > 10) {
                abilityAvgRt = rt;
                //统计初始化且算法耗时未初始化时
                taskCacheRpcService.cacheAbilityRtAvg(abilityId, rt);
            }
        }
        if (instanceMetricContainer.containsKey(abilityInstance)) {
            instanceMetricContainer.get(abilityInstance).incrSuccess(rt);
        }
    }

    /**
     * 添加成功统计
     */
    void tryAddRt() {
        synchronized (successObject) {
            if (flushCondition(MetricItemEnum.SUCCESS) && taskCacheRpcService != null) {
                taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.SUCCESS_COUNT.getCode(), success());
                lastSucTimeMillis = System.currentTimeMillis();
                reset(MetricItemEnum.SUCCESS);
            }
        }
    }

    /**
     * 判断是否持久化
     *
     * @return
     */
    private boolean flushCondition(MetricItemEnum event) {
        if (MetricItemEnum.SUCCESS.equals(event)) {
            return success() > 0 && (checkSize(event) || checkTime(event));
        }
        if (MetricItemEnum.EXCEPTION.equals(event)) {
            return exception() > 0 && (checkSize(event) || checkTime(event));
        }
        if (MetricItemEnum.FAST_FAIL.equals(event)) {
            return fastFailNum() > 0 && (checkSize(event) || checkTime(event));
        }
        return false;
    }

    /**
     * 判断容量上是否需要持久化
     *
     * @return
     */
    private boolean checkSize(MetricItemEnum event) {
        if (MetricItemEnum.SUCCESS.equals(event)) {
            return success() >= maxFlushSize;
        }
        if (MetricItemEnum.EXCEPTION.equals(event)) {
            return exception() >= maxFlushSize;
        }
        if (MetricItemEnum.FAST_FAIL.equals(event)) {
            return fastFailNum() >= maxFlushSize;
        }
        return false;
    }


    /**
     * 判断是否时间上需要持久化
     *
     * @return
     */
    private boolean checkTime(MetricItemEnum event) {
        long current = System.currentTimeMillis();
        if (MetricItemEnum.EXCEPTION.equals(event)) {
            return current - lastFailTimeMillis > timeoutMillis;
        }
        if (MetricItemEnum.SUCCESS.equals(event)) {
            return current - lastSucTimeMillis > timeoutMillis;
        }
        if (MetricItemEnum.FAST_FAIL.equals(event)) {
            return current - lastFastFailTimeMillis > timeoutMillis;
        }
        return false;
    }

    @Override
    public void close() {
        log.info("即将关闭算法调用统计");
        if (this.scheduledexecutorService != null) {
            this.scheduledexecutorService.shutdown();
        }
        //持久化数据
        long success = success();
        if (success > 0) {
            taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.SUCCESS_COUNT.getCode(), success);
        }
        long exception = exception();
        if (exception > 0) {
            taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.FAIL_COUNT.getCode(), exception);
        }
        long fastFail = fastFailNum();
        if (fastFail > 0) {
            taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.FAST_FAIL_COUNT.getCode(), fastFail);
        }
        reset();
    }

    /**
     * 获取算法实例统计数据
     *
     * @return
     */
    public List<AbilityCallMetricData> getAbilityCallData() {
        List<AbilityCallMetricData> abilityCallMetricDataList = new ArrayList<>();
        Set<Map.Entry<String, AbilityInstanceMinuteMetricStatistician>> entries = instanceMetricContainer.entrySet();

        for (Map.Entry<String, AbilityInstanceMinuteMetricStatistician> instance : entries) {
            AbilityCallMetricData abilityCallMetricData = new AbilityCallMetricData();
            abilityCallMetricData.setAbilityId(abilityId.toString());
            abilityCallMetricData.setInstanceId(instance.getKey());
            AbilityInstanceMinuteMetricStatistician value = instance.getValue();

            abilityCallMetricData.setSuccessCount(value.totalSuccess());
            abilityCallMetricData.setFailCount(value.totalException());
            abilityCallMetricData.setRt(value.avgRt());
            abilityCallMetricDataList.add(abilityCallMetricData);
        }
        return abilityCallMetricDataList;
    }

    /**
     * 获取算法平均耗时
     *
     * @return
     */
    public long getAbilityAvgRt() {
        return this.abilityAvgRt;
    }


    /**
     * 获取算法平均耗时
     *
     * @return
     */
    public long getAbilityTps() {
        if (MapUtil.isEmpty(instanceMetricContainer)) {
            return 0;
        }
        int total = 0;
        for (AbilityInstanceMinuteMetricStatistician instance : instanceMetricContainer.values()) {
            total += instance.total();
        }
        return total;
    }
}
