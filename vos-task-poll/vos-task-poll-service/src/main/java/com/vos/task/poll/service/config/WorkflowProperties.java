package com.vos.task.poll.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Data
@Component
@ConfigurationProperties(prefix = "workflow")
@RefreshScope
public class WorkflowProperties {
    /**
     * 超时时间
     */
    private int timeoutMs = 1000;

    /**
     * 最大客户端实例数
     */
    private Long maxHttpClientNum = 5L;

    /**
     * 单实例最大连接数
     */
    private int maxRequests = 1024;

    /**
     * 单实例上每个目标主机的最大连接数
     */
    private int maxRequestsPerHost = 32;

    /**
     * 连接池的最大空闲连接数
     */
    private int maxIdleConnections = 30;

    /**
     * 连接保持活动的时间
     */
    private int keepAliveDuration = 360;
}
