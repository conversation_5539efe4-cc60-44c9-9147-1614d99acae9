package com.vos.task.poll.service.handler.ability;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.response.ReRankAbilityResponse;
import com.linker.omos.client.domain.response.ReRankItemResponse;
import com.linker.omos.client.domain.response.ReRankResponse;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.linker.omos.client.domain.workflow.callback.TaskCallExceptionDTO;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.IThroughAbilityCallBackService;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.poll.service.handler.AbilityHttpConcurrentLimit;
import com.vos.task.poll.service.handler.AbilityRequestService;
import com.vos.task.poll.service.handler.AsyncCallbackHandler;
import com.vos.task.poll.service.handler.workflow.IWorkflowTaskResultBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.cluster.specifyaddress.Address;
import org.apache.dubbo.rpc.cluster.specifyaddress.UserSpecifiedAddressUtil;
import org.apache.skywalking.apm.toolkit.trace.ConsumerWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class ThroughAbilityClient extends AbstractAbilityClient {

    @Value("${statistic.schedule.persistSwitch:false}")
    private boolean persistSwitch;

    @Resource
    ITaskCacheRpcService taskCacheRpcService;

    @DubboReference(retries = 1)
    IThroughAbilityCallBackService throughAbilityCallBack;


    @Resource
    AsyncCallbackHandler asyncCallbackHandler;

    @Resource
    IWorkflowTaskResultBackService workflowTaskResultBackService;

    /**
     * 该处理器处理的算法类型
     */
    public ThroughAbilityClient() {
        abilityType = ListUtil.of(RpcCallBackInfoEnum.THROUGH.getKey());
    }

    @Override
    protected void doRequestAbility(TaskSub taskSub, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {
        taskSub.setHandleDirectByMq(true);
        taskSub.setHandleStartTime(new Date());
        String abilityRequestUrl = abilityRequestInfo.getAbilityRequestUrl();
        taskSub.setTaskServerRouterId(abilityRequestInfo.getRouterId());
        taskSub.setRequestUrl(abilityRequestUrl);
        try {
            if (log.isDebugEnabled()) {
                log.debug("{},准备请求through,地址：{},参数：{}", taskSub.getAppSourceId(), abilityRequestUrl, JSON.toJSONString(taskSub));
            }
            JSONObject requestData = JSON.parseObject(taskSub.getRequestJson());
            through(taskSub, requestData, abilityHttpConcurrentLimit, abilityRequestInfo);
        } catch (Exception e) {
            log.error("{},请求through发生异常,地址：{}", taskSub.getAppSourceId(), abilityRequestUrl, e);
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            errorRequestAbilityResult(taskSub, e.getMessage());
        }
    }


    /**
     * 处理cpu 透传算法
     *
     * @param taskSub
     * @param abilityHttpConcurrentLimit
     * @param abilityRequestInfo
     */
    public void through(TaskSub taskSub, JSONObject requestJson, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {

        Date handleStartTime = taskSub.getHandleStartTime();
        String requestUrl = taskSub.getRequestUrl();
        Long subTaskId = taskSub.getSubTaskId();
        String apiKey = StrUtil.isBlank(taskSub.getExtend()) ? "" : JSONObject.parseObject(taskSub.getExtend()).getString("apiKey");
        //获取客户端
        AbilityRequestService requestAbilityClient = asyncCallbackHandler.getRequestAbilityClient(requestUrl, taskSub.getAbilityId(), abilityHttpConcurrentLimit.getConcurrentLimitNum());
        String appSourceId = taskSub.getAppSourceId();
        Mono<String> mono = requestAbilityClient
                .requestAbilityWithAuth(requestUrl, apiKey, requestJson)
                .onErrorResume(res -> {
                    log.error("{},subTaskId={},requestUrl:{},through 发生错误", appSourceId, subTaskId, requestUrl, res);
                    return Mono.just("调用异常" + (StrUtil.isNotBlank(res.getMessage()) ? ":" + res.getMessage() : ""));
                });

        mono.subscribe(ConsumerWrapper.of(result -> {
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            taskSub.setRequestResult(result);
            Date now = new Date();
            int code = StrUtil.isNotBlank(result) && result.contains("调用异常") ? 500 : 200;
            if (log.isDebugEnabled()) {
                log.debug("{},subTaskId={},请求底层能力返回的结果:{}", appSourceId, subTaskId, result);
            }
            if (code == 200) {
                // 更新子任务状态
                taskSub.setHandleStatus(SubTaskStatusEnum.FINISHED.getKey());
                long l = now.getTime() - handleStartTime.getTime();
                //算法成功执行时间
                abilityHttpConcurrentLimit.addRt(appSourceId, abilityRequestInfo.getRouterId(),
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? taskSub.getBatchMergedTaskSub().size() : 1,
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? (l / taskSub.getBatchMergedTaskSub().size()) : l, l);
            } else {
                taskSub.setHandleStatus(SubTaskStatusEnum.EXECUTE_ERROR.getKey());
                abilityHttpConcurrentLimit.addException(abilityRequestInfo.getRouterId());
            }
            taskSub.setHandleFinishTime(now);
            taskSub.setHandleTime((now.getTime() - handleStartTime.getTime()));
            taskSub.setUpdateTime(now);
            //更新结果
            taskCacheRpcService.updateTaskSubCache(taskSub);
            //回调
            callBackNormal(taskSub);
        }));
    }


    @Override
    protected void callBackNormal(TaskSub taskSub) {
        if (BooleanUtil.isTrue(taskSub.getBatchRequest())) {
            mergedTaskCallBack(taskSub);
        } else {
            if (ScheduleConstants.WORKFLOW.equals(taskSub.getCallbackInfo())) {
                String metaInfo = taskSub.getMetaInfo();
                ModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ModelMetaInfo.class);
                WorkflowCallBackContent workflowCallBackContent = buildWorkflowCallBackContent(taskSub, metaInfoBean);
                //编排任务处理
                workflowTaskResultBackService.workflowTaskResultBack(taskSub.getAbilityId(), metaInfoBean.getCallBackUrl(), workflowCallBackContent);
            } else {
                // 开始回调
                TaskCallBackDto taskCallBackDto = buildCallBackDto(taskSub);
                if (log.isDebugEnabled()) {
                    log.debug("through 准备回调：{},回调数据：{}", taskSub.getCallbackInfo(), JSON.toJSONString(taskCallBackDto));
                }
                if (StrUtil.isNotBlank(taskSub.getCallbackInfo())) {
                    //返回到对应的business
                    UserSpecifiedAddressUtil.setAddress(new Address(taskSub.getCallbackInfo(), 21881, true));
                }
                throughAbilityCallBack.throughAbilityCallBack(taskCallBackDto);
            }
            //数据处理
            if (!BooleanUtil.isTrue(taskSub.getHandleDirectByMq())) {
                this.taskDump(taskSub.getTaskId());
            }
        }
    }

    /**
     * 构建workflow回调内容
     *
     * @param taskSub
     * @return
     */
    WorkflowCallBackContent buildWorkflowCallBackContent(TaskSub taskSub, ModelMetaInfo metaInfoBean) {
        WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
        String appSourceId = taskSub.getAppSourceId();
        if (StrUtil.isNotBlank(metaInfoBean.getBizMeta()) && metaInfoBean.getBizMeta().contains("{")) {
            workflowCallBackContent.setTransmissionParams(JSON.parseObject(metaInfoBean.getBizMeta()));
        }
        WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
        workflowCallBackContentItem.setSourceId(appSourceId);
        workflowCallBackContentItem.setStartTime(taskSub.getHandleStartTime().getTime());
        workflowCallBackContentItem.setWorkerId(this.hostName);
        Integer handleStatus = taskSub.getHandleStatus();
        workflowCallBackContentItem.setStatus(SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus) ? WorkflowStatusEnum.COMPLETED : WorkflowStatusEnum.FAILED);
        TaskCallExceptionDTO taskCallException = null;
        JSONObject aiResult = StringUtils.isNotBlank(taskSub.getRequestResult()) && SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus) ? JSONObject.parseObject(taskSub.getRequestResult()) : null;
        if (!SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            taskCallException = new TaskCallExceptionDTO("500", taskSub.getRequestResult());
            workflowCallBackContentItem.setReasonForIncompletion(taskCallException.getMessage());
        }
        //结果输出
        workflowCallBackContentItem.setOutputData(buildWorkflowOutput(appSourceId, metaInfoBean, aiResult, taskCallException));
        workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
        return workflowCallBackContent;
    }

    /**
     * 构建workflow回调内容
     *
     * @param appSourceId
     * @param aiResultResponse
     * @param taskCallException
     * @return
     */
    private Object buildWorkflowOutput(String appSourceId, ModelMetaInfo metaInfoBean, JSONObject aiResultResponse, TaskCallExceptionDTO taskCallException) {

        return taskCallException == null ? aiResultResponse : taskCallException;
    }


    /**
     * 构造最终rpc返回数据
     *
     * @param taskSub
     * @return
     */
    TaskCallBackDto buildCallBackDto(TaskSub taskSub) {
        TaskCallBackDto taskCallBackDto = new TaskCallBackDto();
        taskCallBackDto.setTaskId(taskSub.getTaskId());
        taskCallBackDto.setTaskNum(taskSub.getTaskNum());
        taskCallBackDto.setAppSourceId(taskSub.getAppSourceId());
        taskCallBackDto.setRefluxId(taskSub.getRefluxId());
        taskCallBackDto.setAbilityId(taskSub.getAbilityId());
        taskCallBackDto.setMetaInfo(taskSub.getMetaInfo());
//        JSONObject aiResult = StringUtils.isNotBlank(taskSub.getRequestResult()) ? JSONObject.parseObject(taskSub.getRequestResult()) : null;
        Integer handleStatus = taskSub.getHandleStatus();
        if (SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            taskCallBackDto.setRequestParseResult(taskSub.getRequestResult());
        } else {
            taskCallBackDto.setRequestParseResult(new ArrayList<>());
            taskCallBackDto.setException(new TaskCallExceptionDTO("500", taskSub.getRequestResult()));
        }
        return taskCallBackDto;
    }

    @Override
    public void fastFailHandler(TaskSub taskSub) {
        try {
            TaskSub taskSubCache = StrUtil.isNotBlank(taskSub.getRequestJson()) ? taskSub : taskCacheRpcService.getTaskSubCache(taskSub.getTaskId().toString(), taskSub.getSubTaskId().toString(), taskSub.getAbilityId().toString());
            if (null != taskSubCache) {
                taskSubCache.setRequestJson("");
                errorRequestAbilityResult(taskSubCache, taskSub.getAppSourceId() + "任务超出处理时效，跳过");
            }
        } catch (Exception e) {
            log.error("through fastFailHandler", e);
        }

    }

    /**
     * 任务转存
     *
     * @param taskId
     * @return
     */
    protected Boolean taskDump(Long taskId) {
        if (persistSwitch) {
            taskCacheRpcService.taskResultDumpCache(taskId.toString());
        }
        return taskCacheRpcService.deleteCache(taskId.toString());
    }
}
