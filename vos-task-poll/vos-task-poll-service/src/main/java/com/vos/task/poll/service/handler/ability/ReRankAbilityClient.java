package com.vos.task.poll.service.handler.ability;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.request.ReRankAbilityRequest;
import com.linker.omos.client.domain.request.ReRankAbilityRequestDTO;
import com.linker.omos.client.domain.response.ReRankAbilityResponse;
import com.linker.omos.client.domain.response.ReRankItemResponse;
import com.linker.omos.client.domain.response.ReRankResponse;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.linker.omos.client.domain.workflow.callback.TaskCallExceptionDTO;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.IReRankBusinessCallBackService;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.poll.service.handler.AbilityHttpConcurrentLimit;
import com.vos.task.poll.service.handler.AbilityRequestService;
import com.vos.task.poll.service.handler.AsyncCallbackHandler;
import com.vos.task.poll.service.handler.workflow.IWorkflowTaskResultBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.cluster.specifyaddress.Address;
import org.apache.dubbo.rpc.cluster.specifyaddress.UserSpecifiedAddressUtil;
import org.apache.skywalking.apm.toolkit.trace.ConsumerWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年05月22日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ReRankAbilityClient extends AbstractAbilityClient {

    @Value("${statistic.schedule.persistSwitch:false}")
    private boolean persistSwitch;

    @Resource
    ITaskCacheRpcService taskCacheRpcService;

    @DubboReference(retries = 1)
    IReRankBusinessCallBackService reRankBusinessCallBackService;

    @Resource
    AsyncCallbackHandler asyncCallbackHandler;

    @Resource
    IWorkflowTaskResultBackService workflowTaskResultBackService;

    /**
     * 该处理器处理的算法类型
     */
    public ReRankAbilityClient() {
        abilityType = ListUtil.of(RpcCallBackInfoEnum.RE_RANK.getKey());
    }

    @Override
    protected void doRequestAbility(TaskSub taskSub, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {
        //reRank 请求方式
        taskSub.setHandleDirectByMq(true);
        taskSub.setHandleStartTime(new Date());
        String abilityRequestUrl = abilityRequestInfo.getAbilityRequestUrl();
        taskSub.setTaskServerRouterId(abilityRequestInfo.getRouterId());
        taskSub.setRequestUrl(abilityRequestUrl);
        try {
            ReRankAbilityRequest requestData = buildRankRequestData(taskSub);
            if (log.isDebugEnabled()) {
                log.debug("{},准备请求reRank,地址：{},参数：{}", taskSub.getAppSourceId(), abilityRequestUrl, JSON.toJSONString(requestData));
            }
            reRank(taskSub, requestData, abilityHttpConcurrentLimit, abilityRequestInfo);
        } catch (Exception e) {
            log.error("{},请求reRank发生异常,地址：{}", taskSub.getAppSourceId(), abilityRequestUrl, e);
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            errorRequestAbilityResult(taskSub, e.getMessage());
        }
    }

    /**
     * 构造算法请求参数
     *
     * @param taskSub
     * @return
     */
    protected ReRankAbilityRequest buildRankRequestData(TaskSub taskSub) {
        String requestJson = taskSub.getRequestJson();
        List<List<String>> text = new ArrayList<>();

        ReRankAbilityRequestDTO reRankAbilityRequestDTO = JSON.parseObject(requestJson, ReRankAbilityRequestDTO.class);
        ReRankAbilityRequest reRankAbilityRequest = new ReRankAbilityRequest();
        reRankAbilityRequest.setModel_id(reRankAbilityRequestDTO.getModelId());
        List<String> passage = reRankAbilityRequestDTO.getDocuments();
        for (String s : passage) {
            ArrayList<String> subRequest = new ArrayList<>();
            subRequest.add(reRankAbilityRequestDTO.getQuery());
            subRequest.add(s);
            text.add(subRequest);
        }
        reRankAbilityRequest.setText(text);

        return reRankAbilityRequest;
    }

    @Override
    public void fastFailHandler(TaskSub taskSub) {
        try {
            TaskSub taskSubCache = StrUtil.isNotBlank(taskSub.getRequestJson()) ? taskSub : taskCacheRpcService.getTaskSubCache(taskSub.getTaskId().toString(), taskSub.getSubTaskId().toString(), taskSub.getAbilityId().toString());
            if (null != taskSubCache) {
                taskSubCache.setRequestJson("");
                errorRequestAbilityResult(taskSubCache, taskSub.getAppSourceId() + "任务超出处理时效，跳过");
            }
        } catch (Exception e) {
            log.error("reRank fastFailHandler", e);
        }
    }

    /**
     * 处理ocr算法
     *
     * @param taskSub
     * @param abilityHttpConcurrentLimit
     * @param abilityRequestInfo
     */
    public void reRank(TaskSub taskSub, ReRankAbilityRequest requestJson, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {

        Date handleStartTime = taskSub.getHandleStartTime();
        String requestUrl = taskSub.getRequestUrl();
        Long subTaskId = taskSub.getSubTaskId();
        //获取客户端
        AbilityRequestService requestAbilityClient = asyncCallbackHandler.getRequestAbilityClient(requestUrl, taskSub.getAbilityId(), abilityHttpConcurrentLimit.getConcurrentLimitNum());
        String appSourceId = taskSub.getAppSourceId();
        Mono<ReRankAbilityResponse> mono = requestAbilityClient
                .requestReRankAbility(requestUrl, requestJson)
                .onErrorResume(res -> {
                    log.error("{},subTaskId={},requestUrl:{},reRank发生错误", appSourceId, subTaskId, requestUrl, res);
                    ReRankAbilityResponse response = new ReRankAbilityResponse();
                    response.setCode(500);
                    response.setMsg(res.getMessage());
                    return Mono.just(response);
                });

        mono.subscribe(ConsumerWrapper.of(result -> {
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            String requestResult = JSON.toJSONString(result);
            taskSub.setRequestResult(requestResult);
            int code = result.getCode() == null ? 0 : result.getCode();
            if (log.isDebugEnabled()) {
                log.debug("{},subTaskId={},请求底层能力返回的结果code={},result:{}", appSourceId, subTaskId, code, result);
            }
            Date now = new Date();
            if (code == 200) {
                // 更新子任务状态
                taskSub.setHandleStatus(SubTaskStatusEnum.FINISHED.getKey());
                //算法成功执行时间
                abilityHttpConcurrentLimit.addRt(appSourceId, abilityRequestInfo.getRouterId(),
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? taskSub.getBatchMergedTaskSub().size() : 1,
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? ((now.getTime() - handleStartTime.getTime()) / taskSub.getBatchMergedTaskSub().size()) : (now.getTime() - handleStartTime.getTime()), result.getTook());
            } else {
                taskSub.setHandleStatus(SubTaskStatusEnum.EXECUTE_ERROR.getKey());
                if (code == 50) {
                    taskSub.setHandleStatus(SubTaskStatusEnum.ABILITY_ERROR.getKey());
                }
                abilityHttpConcurrentLimit.addException(abilityRequestInfo.getRouterId());
            }
            taskSub.setHandleFinishTime(now);
            taskSub.setHandleTime((now.getTime() - handleStartTime.getTime()));
            taskSub.setUpdateTime(now);
            //更新结果
            taskCacheRpcService.updateTaskSubCache(taskSub);
            //回调
            callBackNormal(taskSub);
        }));
    }


    /**
     * 回调处理
     *
     * @param taskSub
     */
    protected void callBackNormal(TaskSub taskSub) {

        if (ScheduleConstants.WORKFLOW.equals(taskSub.getCallbackInfo())) {
            String metaInfo = taskSub.getMetaInfo();
            ModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ModelMetaInfo.class);
            WorkflowCallBackContent workflowCallBackContent = buildWorkflowCallBackContent(taskSub, metaInfoBean);
            //编排任务处理
            workflowTaskResultBackService.workflowTaskResultBack(taskSub.getAbilityId(), metaInfoBean.getCallBackUrl(), workflowCallBackContent);
        } else {
            // 开始回调
            TaskCallBackDto taskCallBackDto = buildCallBackDto(taskSub);
            if (log.isDebugEnabled()) {
                log.debug("reRank 准备回调：{},回调数据：{}", taskSub.getCallbackInfo(), JSON.toJSONString(taskCallBackDto));
            }
            if (StrUtil.isNotBlank(taskSub.getCallbackInfo())) {
                //返回到对应的business
                UserSpecifiedAddressUtil.setAddress(new Address(taskSub.getCallbackInfo(), 21881, true));
            }
            reRankBusinessCallBackService.reRankCallBack(taskCallBackDto);
        }
        //数据处理
        if (!BooleanUtil.isTrue(taskSub.getHandleDirectByMq())) {
            this.taskDump(taskSub.getTaskId());
        }
    }

    /**
     * 构建workflow回调内容
     *
     * @param taskSub
     * @return
     */
    WorkflowCallBackContent buildWorkflowCallBackContent(TaskSub taskSub, ModelMetaInfo metaInfoBean) {
        WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
        String appSourceId = taskSub.getAppSourceId();
        if (StrUtil.isNotBlank(metaInfoBean.getBizMeta()) && metaInfoBean.getBizMeta().contains("{")) {
            workflowCallBackContent.setTransmissionParams(JSON.parseObject(metaInfoBean.getBizMeta()));
        }
        WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
        workflowCallBackContentItem.setSourceId(appSourceId);
        workflowCallBackContentItem.setStartTime(taskSub.getHandleStartTime().getTime());
        workflowCallBackContentItem.setWorkerId(this.hostName);
        Integer handleStatus = taskSub.getHandleStatus();
        workflowCallBackContentItem.setStatus(SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus) ? WorkflowStatusEnum.COMPLETED : WorkflowStatusEnum.FAILED);
        TaskCallExceptionDTO taskCallException = null;
        ReRankAbilityResponse response = JSON.parseObject(taskSub.getRequestResult(), ReRankAbilityResponse.class);

        if (!SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            taskCallException = new TaskCallExceptionDTO();
            taskCallException.setCode(response.getCode().toString());
            taskCallException.setMessage(response.getMsg());
            workflowCallBackContentItem.setReasonForIncompletion(taskCallException.getMessage());
        }
        //结果输出
        workflowCallBackContentItem.setOutputData(buildWorkflowOutput(appSourceId, metaInfoBean, response, taskCallException));
        workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
        return workflowCallBackContent;
    }

    /**
     * 构建workflow回调内容
     *
     * @param appSourceId
     * @param aiResultResponse
     * @param taskCallException
     * @return
     */
    private ReRankResponse buildWorkflowOutput(String appSourceId, ModelMetaInfo metaInfoBean, ReRankAbilityResponse aiResultResponse, TaskCallExceptionDTO taskCallException) {

        ReRankResponse reRankResponse = new ReRankResponse();
        reRankResponse.setModel(metaInfoBean.getModel());
        reRankResponse.setTook(new Date().getTime() - metaInfoBean.getEventTime());
        reRankResponse.setSourceId(appSourceId);
        //是否异常
        if (taskCallException != null) {
            reRankResponse.setError(taskCallException.getMessage());
            reRankResponse.setCode(Integer.valueOf(taskCallException.getCode()));
        } else {
            reRankResponse.setCode(200);

            List<BigDecimal> features = aiResultResponse.getFeatures();
            List<ReRankItemResponse> data = new ArrayList<>();
            int i = 0;
            for (BigDecimal feature : features) {
                ReRankItemResponse reRankItemResponse = new ReRankItemResponse();
                reRankItemResponse.setIndex(i);
                reRankItemResponse.setScore(feature);
                data.add(reRankItemResponse);
                i++;
            }
            reRankResponse.setData(data);
        }
        return reRankResponse;
    }

    /**
     * 构造最终rpc返回数据
     *
     * @param taskSub
     * @return
     */
    TaskCallBackDto buildCallBackDto(TaskSub taskSub) {
        TaskCallBackDto taskCallBackDto = new TaskCallBackDto();
        taskCallBackDto.setTaskId(taskSub.getTaskId());
        taskCallBackDto.setTaskNum(taskSub.getTaskNum());
        taskCallBackDto.setAppSourceId(taskSub.getAppSourceId());
        taskCallBackDto.setRefluxId(taskSub.getRefluxId());
        taskCallBackDto.setAbilityId(taskSub.getAbilityId());
        taskCallBackDto.setMetaInfo(taskSub.getMetaInfo());
        ReRankAbilityResponse response = JSON.parseObject(taskSub.getRequestResult(), ReRankAbilityResponse.class);

        Integer handleStatus = taskSub.getHandleStatus();
        if (SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            taskCallBackDto.setRequestParseResult(response);
        } else {
            taskCallBackDto.setRequestParseResult(new ArrayList<>());
            TaskCallExceptionDTO taskCallException = new TaskCallExceptionDTO();
            taskCallException.setCode(response.getCode().toString());
            taskCallException.setMessage(response.getMsg());
            taskCallBackDto.setException(taskCallException);
        }
        return taskCallBackDto;
    }


    /**
     * 任务转存
     *
     * @param taskId
     * @return
     */
    protected Boolean taskDump(Long taskId) {
        if (persistSwitch) {
            taskCacheRpcService.taskResultDumpCache(taskId.toString());
        }
        return taskCacheRpcService.deleteCache(taskId.toString());
    }

}
