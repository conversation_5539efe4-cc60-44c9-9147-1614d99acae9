package com.vos.task.poll.service.handler;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.log.enums.MetricPattern;
import com.linker.log.utils.TraceLogUtil;
import com.linker.omos.client.config.ItemCodeEnum;
import com.vos.kernel.common.common.LifecycleAware;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.threads.ServiceThread;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.poll.service.handler.ability.AbilityClientHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2024年10月31日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class RatioAbilityWorkProcessor extends ServiceThread implements LifecycleAware {

    /**
     * 集群编号
     */
    private String cluster;

    /**
     * 能力编号
     */
    private String abilityId;

    /**
     * 能力编号
     */
    private String abilityCode;


    /**
     * 算法任务
     */
    private LinkedBlockingQueue<AbilityRequestUrlDTO> abilityConcurrent = new LinkedBlockingQueue<>();


    /**
     * 单集群并发限制数
     */
    private Integer concurrentLimitNum;


    /**
     * 消费进度控制
     */
    protected IMessageQueueOffSetService messageQueueOffSetService;


    /**
     * 队列信息
     */
    RatioAbilityQueue ratioAbilityQueue;


    /**
     * 算法执行
     */
    protected AbilityClientHolder abilityClient;

    /**
     * 请求算法线程
     */
    protected ThreadPoolExecutor executorService;

    /**
     * 管理器
     */
    RatioAbilityWorkPoll ratioAbilityWorkPoll;

    /**
     * 并发控制器
     */
    AbilityHttpConcurrentLimit abilityHttpConcurrentLimit;


    /**
     * 构造函数
     *
     * @param abilityId
     * @param abilityCode
     * @param abilityUrlLimit
     * @param ratioAbilityQueue
     * @param abilityMetric
     * @param messageQueueOffSetService
     * @param abilityClient
     */
    public RatioAbilityWorkProcessor(RatioAbilityWorkPoll ratioAbilityWorkPoll, String abilityId, String abilityCode, String cluster, List<AbilityRequestUrlDTO> abilityUrlLimit,
                                     RatioAbilityQueue ratioAbilityQueue, AbilityStatistician abilityMetric,
                                     IMessageQueueOffSetService messageQueueOffSetService, AbilityClientHolder abilityClient) {
        this.abilityId = abilityId;
        this.abilityCode = abilityCode;
        this.cluster = cluster;
        this.concurrentLimitNum = abilityUrlLimit.size();
        this.abilityConcurrent.addAll(abilityUrlLimit);
        this.ratioAbilityWorkPoll = ratioAbilityWorkPoll;
        this.ratioAbilityQueue = ratioAbilityQueue;
        this.messageQueueOffSetService = messageQueueOffSetService;
        this.abilityClient = abilityClient;
        int size = abilityUrlLimit.size();
        //请求线程池
        this.executorService = ExecutorBuilder.create()
                .setCorePoolSize(size)
                .setMaxPoolSize(size + 32)
                .useSynchronousQueue()
                .build();

        this.abilityHttpConcurrentLimit = new AbilityHttpConcurrentLimit(abilityId, abilityConcurrent, abilityUrlLimit, abilityMetric, concurrentLimitNum);
        this.setDaemon(true);
    }

    @Override
    public String getServiceName() {
        return "RatioProcessor-" + cluster;
    }

    @Override
    public void run() {
        log.info("{},启动ratio算法私有调用线程器：{},currentThread:{}", abilityCode, cluster, Thread.currentThread().getId());
        while (!this.stopped) {
            try {
                //判断是否可用
                log.info("cluster:{},能力{},{},实时并发数：{},当前剩余数：{}", cluster, abilityId, abilityCode, concurrentLimitNum, abilityConcurrent.size());
                AbilityRequestUrlDTO abilityRequestUrlDTO = this.abilityHttpConcurrentLimit.acquire();
                Boolean checkAvailable = ratioAbilityWorkPoll.checkAvailable(abilityRequestUrlDTO);
                //判断地址是否可用
                if (!BooleanUtil.isTrue(checkAvailable)) {
                    log.info("{},无效的地址：{},跳过", abilityCode, abilityRequestUrlDTO.getAbilityRequestUrl());
                    continue;
                }
                Message taskSubMq = this.ratioAbilityQueue.poll(cluster);
                if (taskSubMq == null) {
                    log.error("ratioQueue error,abilityCode:{}", abilityCode);
                    this.abilityHttpConcurrentLimit.release(abilityRequestUrlDTO);
                } else {
                    //请求算法
                    enterExecutor(taskSubMq, abilityRequestUrlDTO);
                }
            } catch (Exception e) {
                if (!this.stopped) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        //任务停止运行
        log.info("RatioAbilityWorkProcessor ：{},{}, 停止工作", abilityId, abilityCode);
    }

    @Override
    public void onStart() {
        log.info("RatioProcessor 即将启动start");
        this.start();
    }

    @Override
    public void onShutdown() {
        this.executorService.shutdown();
        super.shutdown(true);
    }


    /**
     * 处理消息：单独领出来为了skywalking自定义插件对此方法进行监控
     */
    public TaskSub handleMessage(Message messageExt) {
        TaskSub taskSub = JSON.parseObject(new String(messageExt.getBody()), TaskSub.class);
        return taskSub;
    }

    /**
     * 独立请求线程池
     *
     * @param messageExt
     */
    private void enterExecutor(Message messageExt, AbilityRequestUrlDTO abilityRequestInfo) {
        try {
            executorService.execute(RunnableWrapper.of(() -> {
                TaskSub taskSub = handleMessage(messageExt);
                log.info("{},taskSub={},的任务进入线程池成功,cluster:{},localSize:{}", taskSub.getAppSourceId(), taskSub.getTaskId(), cluster, this.ratioAbilityQueue.capacityQueue.size());
                if (StrUtil.isNotBlank(taskSub.getExtend())) {
                    int i = RandomUtil.randomInt(1, 10);
                    JSONObject jsonObject = JSON.parseObject(taskSub.getExtend());
                    String extra = jsonObject.getString("deviceId") + "," + jsonObject.getString("bizTaskId") + "," + jsonObject.getString("abilityCode");
                    TraceLogUtil.trace(ItemCodeEnum.SF_9BXKNOYQAO7.getCode(), MetricPattern.CUSTOM, "", i, "0", jsonObject.getLong("frameBizTime"), extra);
                }
                //任务是否超时跳过
                String lineTime = taskSub.getLineTime();
                Integer waiteTime = taskSub.getWaiteTime();
                boolean taskSkip = false;
                if (StrUtil.isNotBlank(lineTime) && waiteTime != null && waiteTime > 0) {
                    //当前时间大于lineTime+waiteTime
                    if (System.currentTimeMillis() > NumberUtil.add(lineTime, waiteTime.toString()).longValue()) {
                        taskSkip = true;
                    }
                }
                //跳过
                if (taskSkip) {
                    this.abilityHttpConcurrentLimit.release(abilityRequestInfo);
                    abilityClient.getClient(taskSub.getCallbackType()).fastFail(taskSub);
                    abilityHttpConcurrentLimit.addFastFailException(1);
                } else {
                    //占用资源
                    abilityClient.getClient(taskSub.getCallbackType()).requestAbility(taskSub, this.abilityHttpConcurrentLimit, abilityRequestInfo);
                }
            }));
        } catch (RejectedExecutionException e) {
            // 线程池已满，拒绝处理
            log.warn("ratio 线程池已满,abilityCode={},的任务被拒绝,开始休眠", abilityCode);
            ThreadUtil.safeSleep(10);
            log.warn("ratio abilityCode={},的任务线程休眠完成,尝试继续进入线程池", abilityCode);
            enterExecutor(messageExt, abilityRequestInfo);
        } catch (Exception e) {
            this.abilityHttpConcurrentLimit.release(abilityRequestInfo);
        }
    }
}
