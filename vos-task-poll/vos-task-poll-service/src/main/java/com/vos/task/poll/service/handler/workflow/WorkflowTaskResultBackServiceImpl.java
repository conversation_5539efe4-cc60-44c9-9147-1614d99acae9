package com.vos.task.poll.service.handler.workflow;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.lianjiatech.retrofit.spring.boot.core.reactive.MonoCallAdapterFactory;
import com.github.lianjiatech.retrofit.spring.boot.log.AggregateLoggingInterceptor;
import com.github.lianjiatech.retrofit.spring.boot.log.GlobalLogProperty;
import com.github.lianjiatech.retrofit.spring.boot.retry.GlobalRetryProperty;
import com.github.lianjiatech.retrofit.spring.boot.retry.RetryInterceptor;
import com.linker.log.enums.MetricPattern;
import com.linker.log.utils.TraceLogUtil;
import com.linker.omos.client.config.ItemCodeEnum;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.task.poll.service.config.WorkflowProperties;
import com.vos.task.poll.service.handler.config.FastjsonConverterFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.apache.skywalking.apm.toolkit.trace.ConsumerWrapper;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class WorkflowTaskResultBackServiceImpl implements IWorkflowTaskResultBackService {

    @Resource
    WorkflowProperties workflowProperties;

    /**
     * 编排客户端
     */
    private final static ConcurrentMap<Long, WorkflowApiService> CLIENT_MAP = new ConcurrentHashMap<>();

    /**
     * 初始化客户端
     */
    @PostConstruct
    private void initBuildRetrofitClient() {
        initOkhttpClient();
    }

    /**
     * 初始化okhttp客户端
     */
    private void initOkhttpClient() {
        for (int i = 0; i < workflowProperties.getMaxHttpClientNum(); i++) {
            GlobalRetryProperty globalRetryProperty = new GlobalRetryProperty();
            globalRetryProperty.setEnable(false);
            GlobalLogProperty globalLogProperty = new GlobalLogProperty();
            OkHttpClient.Builder builder = new OkHttpClient().newBuilder();

            OkHttpClient okHttpClient = builder
                    .readTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                    .connectTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                    .writeTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                    .connectionPool(new ConnectionPool(workflowProperties.getMaxIdleConnections(), workflowProperties.getKeepAliveDuration(), TimeUnit.SECONDS))
                    .retryOnConnectionFailure(true)
                    .addInterceptor(new AggregateLoggingInterceptor(globalLogProperty))
                    .addInterceptor(new RetryInterceptor(globalRetryProperty))
                    .build();
            okHttpClient.dispatcher().setMaxRequests(workflowProperties.getMaxRequests());
            okHttpClient.dispatcher().setMaxRequestsPerHost(workflowProperties.getMaxRequestsPerHost());

            Retrofit retrofit = new Retrofit.Builder()
                    .baseUrl("https://conductor.com/")
                    .client(okHttpClient)
                    .addConverterFactory(FastjsonConverterFactory.create())
                    .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                    .addCallAdapterFactory(MonoCallAdapterFactory.INSTANCE)
                    .build();

            WorkflowApiService workflowApiService = retrofit.create(WorkflowApiService.class);
            CLIENT_MAP.put((long) i, workflowApiService);
        }
    }

    @Override
    public void workflowTaskResultBack(Long abilityId, String url, WorkflowCallBackContent workflowCallBackContent) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        long l = abilityId % workflowProperties.getMaxHttpClientNum();
        WorkflowApiService workflowApiService = CLIENT_MAP.get(l);
        if (null == workflowApiService) {
            throw new RuntimeException("客户端不存在");
        }
        Mono<String> mono = workflowApiService.requestWorkflowEngine(url, ListUtil.of(workflowCallBackContent))
                .doOnError(Exception.class, ConsumerWrapper.of(err -> {
                    log.error("url={},回调编排发生异常", url, err);
                })).onErrorReturn("回调编排异常");

        mono.subscribe(ConsumerWrapper.of(result -> {
            stopWatch.stop();
            log.info("编排回调结果={},耗时：{}", result, stopWatch.getTotalTimeMillis());
        }));


    }
}
