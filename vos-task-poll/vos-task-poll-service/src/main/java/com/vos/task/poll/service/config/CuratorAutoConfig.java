package com.vos.task.poll.service.config;

import com.linker.serving.transport.remote.NettyRpcClient;
import com.vos.task.poll.service.curator.*;
import com.vos.task.poll.service.handler.WaterInfoPollHandler;
import com.vos.task.poll.service.service.ServingRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023年10月07日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({CuratorProperties.class})
@ConditionalOnProperty(name = "curator.enabled", havingValue = "true")
public class CuratorAutoConfig {

    @Resource
    CuratorProperties curatorProperties;

    @Bean(destroyMethod = "close")
    @ConditionalOnMissingBean
    public ZookeeperCuratorClient zookeeperCuratorClient() {
        return new ZookeeperCuratorClient(curatorProperties, "ability");
    }

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean
    public WaterInfoPollHandler waterInfoPollHandler(WaterInfoTaskExecuteEngine waterInfoTaskExecuteEngine, WaterInfoPullInvokeCallBack waterInfoPullInvokeCallBack,
                                                     NettyRpcClient NettyRpcClient, ServingRegisterService servingRegisterService) {
        return new WaterInfoPollHandler(waterInfoTaskExecuteEngine, waterInfoPullInvokeCallBack, NettyRpcClient, servingRegisterService);
    }

    @Bean
    @ConditionalOnMissingBean
    public AbilityCallThreadCheckService abilityCallThreadCheckService(ZookeeperCuratorClient zookeeperCuratorClient) {
        return new AbilityCallThreadCheckService(zookeeperCuratorClient);
    }

}
