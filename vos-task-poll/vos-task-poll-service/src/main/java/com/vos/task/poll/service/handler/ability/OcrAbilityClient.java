package com.vos.task.poll.service.handler.ability;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.response.*;
import com.linker.omos.client.domain.workflow.callback.TaskCallExceptionDTO;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.IOcrBusinessCallBackService;
import com.vos.kernel.common.entity.OcrItemResponse;
import com.vos.kernel.common.entity.OdAndAttrDetItemResponse;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.meta.OdAndAtrrDetModelMetaInfo;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.automated.api.service.rpc.IServingRouterRpcService;
import com.vos.task.poll.service.config.MetricProperties;
import com.vos.task.poll.service.handler.AbilityHttpConcurrentLimit;
import com.vos.task.poll.service.handler.AbilityRequestService;
import com.vos.task.poll.service.handler.AsyncCallbackHandler;
import com.vos.task.poll.service.handler.workflow.IWorkflowTaskResultBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.cluster.specifyaddress.Address;
import org.apache.dubbo.rpc.cluster.specifyaddress.UserSpecifiedAddressUtil;
import org.apache.skywalking.apm.toolkit.trace.ConsumerWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2024年05月22日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class OcrAbilityClient extends AbstractAbilityClient {

    @Value("${statistic.schedule.persistSwitch:false}")
    private boolean persistSwitch;

    @Resource
    ITaskCacheRpcService taskCacheRpcService;

    @DubboReference(retries = 1)
    IOcrBusinessCallBackService ocrBusinessCallBackService;


    @Resource
    AsyncCallbackHandler asyncCallbackHandler;

    @Resource
    IWorkflowTaskResultBackService workflowTaskResultBackService;

    /**
     * 该处理器处理的算法类型
     */
    public OcrAbilityClient() {
        abilityType = ListUtil.of(RpcCallBackInfoEnum.OCR.getKey());
    }

    @Override
    protected void doRequestAbility(TaskSub taskSub, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {
        //ocr 请求方式
        taskSub.setHandleDirectByMq(true);
        taskSub.setHandleStartTime(new Date());
        String abilityRequestUrl = abilityRequestInfo.getAbilityRequestUrl();
        taskSub.setTaskServerRouterId(abilityRequestInfo.getRouterId());
        taskSub.setRequestUrl(abilityRequestUrl);
        try {
            if (log.isDebugEnabled()) {
                log.debug("{},准备请求ocr,地址：{},参数：{}", taskSub.getAppSourceId(), abilityRequestUrl, JSON.toJSONString(taskSub));
            }
            JSONObject requestData = buildRequestData(taskSub, abilityRequestInfo.getAsynType());
            base64ImageMap(requestData, true);
            ocr(taskSub, requestData, abilityHttpConcurrentLimit, abilityRequestInfo);
        } catch (Exception e) {
            log.error("{},请求ocr发生异常,地址：{}", taskSub.getAppSourceId(), abilityRequestUrl, e);
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            errorRequestAbilityResult(taskSub, e.getMessage());
        }
    }

    @Override
    public void fastFailHandler(TaskSub taskSub) {
        try {
            TaskSub taskSubCache = StrUtil.isNotBlank(taskSub.getRequestJson()) ? taskSub : taskCacheRpcService.getTaskSubCache(taskSub.getTaskId().toString(), taskSub.getSubTaskId().toString(), taskSub.getAbilityId().toString());
            if (null != taskSubCache) {
                taskSubCache.setRequestJson("");
                errorRequestAbilityResult(taskSubCache, taskSub.getAppSourceId() + "任务超出处理时效，跳过");
            }
        } catch (Exception e) {
            log.error("ocr fastFailHandler", e);
        } finally {
            //尝试删除redis图片缓存
            taskCacheRpcService.deleteBase64Cache(taskSub.getAppSourceId() + "_" + 0);
        }
    }

    /**
     * 处理ocr算法
     *
     * @param taskSub
     * @param abilityHttpConcurrentLimit
     * @param abilityRequestInfo
     */
    public void ocr(TaskSub taskSub, JSONObject requestJson, AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {

        Date handleStartTime = taskSub.getHandleStartTime();
        String requestUrl = taskSub.getRequestUrl();
        Long subTaskId = taskSub.getSubTaskId();
        //获取客户端
        AbilityRequestService requestAbilityClient = asyncCallbackHandler.getRequestAbilityClient(requestUrl, taskSub.getAbilityId(), abilityHttpConcurrentLimit.getConcurrentLimitNum());
        String appSourceId = taskSub.getAppSourceId();
        Mono<JSONObject> mono = requestAbilityClient
                .requestAbility(requestUrl, requestJson)
                .onErrorResume(res -> {
                    log.error("{},subTaskId={},requestUrl:{},ocr发生错误", appSourceId, subTaskId, requestUrl, res);
                    JSONObject jsonObject = new JSONObject();
                    JSONObject errorJsonObject = new JSONObject();
                    jsonObject.put("code", 500);
                    errorJsonObject.put("message", res.getMessage());
                    jsonObject.put("body", errorJsonObject);
                    jsonObject.put("isSuccess", false);
                    return Mono.just(jsonObject);
                });

        mono.subscribe(ConsumerWrapper.of(result -> {
            //释放资源
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            handleBase64ModelResult(result);
            String requestResult = JSON.toJSONString(result);
            taskSub.setRequestResult(requestResult);
            int code = result.getInteger("code") == null ? 0 : result.getInteger("code");
            boolean success = result.getBoolean("isSuccess") != null && result.getBoolean("isSuccess");
            if (log.isDebugEnabled()) {
                log.debug("{},subTaskId={},请求底层能力返回的结果code={},result:{}", appSourceId, subTaskId, code, result);
            }
            Date now = new Date();
            if (code == 200 && success) {
                // 更新子任务状态
                taskSub.setHandleStatus(SubTaskStatusEnum.FINISHED.getKey());
                //算法成功执行时间
                abilityHttpConcurrentLimit.addRt(appSourceId, abilityRequestInfo.getRouterId(),
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? taskSub.getBatchMergedTaskSub().size() : 1,
                        BooleanUtil.isTrue(taskSub.getBatchRequest()) ? ((now.getTime() - handleStartTime.getTime()) / taskSub.getBatchMergedTaskSub().size()) : (now.getTime() - handleStartTime.getTime()), getAbilityTook(result));
            } else {
                taskSub.setHandleStatus(SubTaskStatusEnum.EXECUTE_ERROR.getKey());
                if (code == 50) {
                    taskSub.setHandleStatus(SubTaskStatusEnum.ABILITY_ERROR.getKey());
                }
                abilityHttpConcurrentLimit.addException(abilityRequestInfo.getRouterId());
            }
            taskSub.setHandleFinishTime(now);
            taskSub.setHandleTime((now.getTime() - handleStartTime.getTime()));
            taskSub.setUpdateTime(now);
            //更新结果
            taskCacheRpcService.updateTaskSubCache(taskSub);
            //回调
            callBackNormal(taskSub);
        }));
    }


    /**
     * 回调处理
     *
     * @param taskSub
     */
    protected void callBackNormal(TaskSub taskSub) {
        if (BooleanUtil.isTrue(taskSub.getBatchRequest())) {
            mergedTaskCallBack(taskSub);
        } else {
            if (ScheduleConstants.WORKFLOW.equals(taskSub.getCallbackInfo())) {
                String metaInfo = taskSub.getMetaInfo();
                ModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ModelMetaInfo.class);
                WorkflowCallBackContent workflowCallBackContent = buildWorkflowCallBackContent(taskSub, metaInfoBean);
                //编排任务处理
                workflowTaskResultBackService.workflowTaskResultBack(taskSub.getAbilityId(), metaInfoBean.getCallBackUrl(), workflowCallBackContent);
            } else {
                // 开始回调
                TaskCallBackDto taskCallBackDto = buildCallBackDto(taskSub);
                if (log.isDebugEnabled()) {
                    log.debug("ocr 准备回调：{},回调数据：{}", taskSub.getCallbackInfo(), JSON.toJSONString(taskCallBackDto));
                }
                if (StrUtil.isNotBlank(taskSub.getCallbackInfo())) {
                    //返回到对应的business
                    UserSpecifiedAddressUtil.setAddress(new Address(taskSub.getCallbackInfo(), 21881, true));
                }
                ocrBusinessCallBackService.ocrCallBack(taskCallBackDto);
            }

            //数据处理
            if (!BooleanUtil.isTrue(taskSub.getHandleDirectByMq())) {
                this.taskDump(taskSub.getTaskId());
            }
        }
    }

    /**
     * 构建workflow回调内容
     *
     * @param taskSub
     * @return
     */
    WorkflowCallBackContent buildWorkflowCallBackContent(TaskSub taskSub, ModelMetaInfo metaInfoBean) {
        WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
        String appSourceId = taskSub.getAppSourceId();
        if (StrUtil.isNotBlank(metaInfoBean.getBizMeta()) && metaInfoBean.getBizMeta().contains("{")) {
            workflowCallBackContent.setTransmissionParams(JSON.parseObject(metaInfoBean.getBizMeta()));
        }
        WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
        workflowCallBackContentItem.setSourceId(appSourceId);
        workflowCallBackContentItem.setStartTime(taskSub.getHandleStartTime().getTime());
        workflowCallBackContentItem.setWorkerId(this.hostName);
        Integer handleStatus = taskSub.getHandleStatus();
        workflowCallBackContentItem.setStatus(SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus) ? WorkflowStatusEnum.COMPLETED : WorkflowStatusEnum.FAILED);
        JSONObject aiResult = getAiResult(taskSub);
        List<OcrItemResponse> aiResultResponse = new ArrayList<>();
        TaskCallExceptionDTO taskCallException = null;
        if (SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            JSONObject results = aiResult.getJSONObject("results");
            Set<String> videoSet = results.keySet();
            for (String videoId : videoSet) {
                JSONArray jsonArray = results.getJSONArray(videoId);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONArray bboxList = jsonObject.getJSONArray("bbox_list");
                    if (bboxList.size() > 0) {
                        List<OcrItemResponse> respList = JSON.parseArray(JSON.toJSONString(bboxList), OcrItemResponse.class);
                        aiResultResponse.addAll(respList);
                    }
                }
            }
        } else {
            taskCallException = buildException(aiResult);
            workflowCallBackContentItem.setReasonForIncompletion(taskCallException.getMessage());
        }
        //结果输出
        workflowCallBackContentItem.setOutputData(buildWorkflowOutput(appSourceId, metaInfoBean, aiResultResponse, taskCallException));
        workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
        return workflowCallBackContent;
    }

    /**
     * 构建workflow回调内容
     *
     * @param appSourceId
     * @param aiResultResponse
     * @param taskCallException
     * @return
     */
    private OcrResponse buildWorkflowOutput(String appSourceId, ModelMetaInfo metaInfoBean, List<OcrItemResponse> aiResultResponse, TaskCallExceptionDTO taskCallException) {

        OcrResponse ocrResponse = new OcrResponse();
        ocrResponse.setTook(new Date().getTime() - metaInfoBean.getEventTime());
        ocrResponse.setModel(metaInfoBean.getModel());
        ocrResponse.setSourceId(appSourceId);
        //是否异常
        if (taskCallException != null) {
            ocrResponse.setCode(Integer.valueOf(taskCallException.getCode()));
            ocrResponse.setError(taskCallException.getMessage());
        } else {
            ocrResponse.setCode(200);
            //设置结果
            List<OcrItemApiResponse> ocrItemApiResponses = new ArrayList<>();
            int i = 0;
            for (OcrItemResponse o : aiResultResponse) {
                OcrItemApiResponse ocrItemApiResponse = new OcrItemApiResponse();
                ocrItemApiResponse.setBbox(o.getBbox());
                ocrItemApiResponse.setConf(CollectionUtil.getFirst(o.getConf()));
                ocrItemApiResponse.setWords(CollectionUtil.getFirst(o.getLabel()));
                ocrItemApiResponse.setIndex(i);
                i++;
                ocrItemApiResponses.add(ocrItemApiResponse);
            }
            ocrResponse.setOcrList(ocrItemApiResponses);
            ocrResponse.setOcrCount(ocrResponse.getOcrList().size());
        }
        return ocrResponse;
    }

    /**
     * 构造最终rpc返回数据
     *
     * @param taskSub
     * @return
     */
    TaskCallBackDto buildCallBackDto(TaskSub taskSub) {
        TaskCallBackDto taskCallBackDto = new TaskCallBackDto();
        taskCallBackDto.setTaskId(taskSub.getTaskId());
        taskCallBackDto.setTaskNum(taskSub.getTaskNum());
        taskCallBackDto.setAppSourceId(taskSub.getAppSourceId());
        taskCallBackDto.setRefluxId(taskSub.getRefluxId());
        taskCallBackDto.setAbilityId(taskSub.getAbilityId());
        taskCallBackDto.setMetaInfo(taskSub.getMetaInfo());
        JSONObject aiResult = getAiResult(taskSub);
        Integer handleStatus = taskSub.getHandleStatus();
        if (SubTaskStatusEnum.FINISHED.getKey().equals(handleStatus)) {
            List<OcrItemResponse> res = new ArrayList<>();
            JSONObject results = aiResult.getJSONObject("results");
            Set<String> videoSet = results.keySet();
            for (String videoId : videoSet) {
                JSONArray jsonArray = results.getJSONArray(videoId);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONArray bboxList = jsonObject.getJSONArray("bbox_list");
                    if (bboxList.size() > 0) {
                        List<OcrItemResponse> respList = JSON.parseArray(JSON.toJSONString(bboxList), OcrItemResponse.class);
                        res.addAll(respList);
                    }
                }
            }
            taskCallBackDto.setRequestParseResult(res);
        } else {
            taskCallBackDto.setRequestParseResult(new ArrayList<>());
            taskCallBackDto.setException(buildException(aiResult));
        }
        return taskCallBackDto;
    }


    /**
     * 任务转存
     *
     * @param taskId
     * @return
     */
    protected Boolean taskDump(Long taskId) {
        if (persistSwitch) {
            taskCacheRpcService.taskResultDumpCache(taskId.toString());
        }
        return taskCacheRpcService.deleteCache(taskId.toString());
    }

}
