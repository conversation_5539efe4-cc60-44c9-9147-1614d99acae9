package com.vos.task.poll.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.linker.basic.exception.BusinessException;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;
import com.vos.task.poll.api.entity.dto.MessageQueueDto;
import com.vos.task.poll.api.entity.model.TaskQueueInfo;
import com.vos.task.poll.api.entity.model.TaskQueueOffset;
import com.vos.task.poll.service.config.AbilityScheduledException;
import com.vos.task.poll.service.curator.AbilityCallThreadCheckService;
import com.vos.task.poll.service.entity.AbilityCallMetricData;
import com.vos.task.poll.service.entity.CallAbilityTimeDTO;
import com.vos.task.poll.service.entity.TaskAbilityEntity;
import com.vos.task.poll.service.rocketmq.RocketMqAdminConfProperties;
import com.vos.task.poll.service.rocketmq.RocketMqManageToolServiceImpl;
import com.vos.task.poll.service.service.IAbilityTopicService;
import com.vos.task.poll.service.service.TaskQueueOffsetService;
import com.vos.task.poll.service.service.batisplus.ITaskAbilityService;
import com.vos.task.poll.service.strategy.AbilityQueueServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.admin.TopicOffset;
import org.apache.rocketmq.common.admin.TopicStatsTable;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 任务池处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/21
 * @description: com.vos.task.poll.service.handler
 */
@Slf4j
@Service
public class AbilityScheduledExecutor implements InitializingBean, DisposableBean, IAbilityScheduledService, ApplicationListener<ContextClosedEvent> {

    @Resource
    IMessageQueueOffSetService messageQueueOffSetService;

    @Resource
    RocketMqAdminConfProperties rocketMqAdminConfProperties;

    @Resource
    RocketMqManageToolServiceImpl rocketMqManageToolService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    TaskQueueOffsetService taskQueueOffsetService;

    @Resource
    IAbilityTopicService abilityTopicService;

    @Resource
    ITaskAbilityService taskAbilityService;

    @Resource
    AbilityCallThreadCheckService abilityCallThreadCheckService;

    @Resource
    AbilityQueueServiceProxy abilityQueueServiceProxy;


    /**
     * 无任务持续多久关闭线程
     */
    @Value("${pollThread.noTaskShutTime:5}")
    private Integer noTaskShutTime;


    /**
     * 同步发送
     */
    @Value("${pollThread.sendSync:true}")
    private Boolean sendSync;

    /**
     * 直接推送处理线程
     */
    @Value("${pollThread.sendToThread:true}")
    private Boolean sendToThread;


    /**
     * 是否跳过任务
     */
    private volatile Boolean skipTask = false;


    /**
     * 处理器集合
     */
    private volatile ConcurrentHashMap<String, AbilityCallThreadHandlerService> HANDLERS = new ConcurrentHashMap<>();


    /**
     * 能力生产者：单生产者官方测试数据50000/s；
     */
    private DefaultMQProducer abilityProducer;

    /**
     * 新起能力处理器
     *
     * @param appIdAndAbilityId
     * @return
     */
    @Override
    @Async("abilityScheduledThreadPool")
    public Boolean startNewAbilitySchedule(String appIdAndAbilityId) {
        if (StrUtil.isBlank(appIdAndAbilityId)) {
            return false;
        }
        List<String> split = StrUtil.split(appIdAndAbilityId, '_');
        //abilityId、appId聚合
        String abilityId = split.get(1);
        String appId = split.get(0);
        String scheduleKey = appId + "_" + abilityId;
        ///能力处理器
        TaskAbilityEntity ability = taskAbilityService.getAbilityById(abilityId);

        if (ability != null) {
            String servingType = ability.getServingType();
            if (ability.getAbilityId() == null) {
                ability.setAbilityId(Long.parseLong(abilityId));
            }
            log.debug("ability getServingType:{}", servingType);
            //全局是否已经启动过;暂时使用redisKey后期换成zookeeper临时节点
//            Boolean checkAbilityThreadHasRun = ServingTypeEnum.JAVA.name().equals(servingType) ?
//                    abilityCallThreadCheckService.checkAbilityThreadHasRun(scheduleKey) :
//                    abilityOccupyService.checkAbilityThreadHasRun(scheduleKey);
            Boolean checkAbilityThreadHasRun = abilityCallThreadCheckService.checkAbilityThreadHasRun(scheduleKey);
            if (BooleanUtil.isTrue(checkAbilityThreadHasRun)) {
                log.debug("poll 能力线程已运行，无需调度，appIdAndAbilityId：{} ", scheduleKey);
                return false;
            }
            try {
                AbilityCallThreadHandlerService abilityCallThreadHandler = buildAbilityCallThreadHandler(servingType, ability, appId);
                //启动
                abilityCallThreadHandler.start();
                HANDLERS.put(scheduleKey, abilityCallThreadHandler);
            } catch (Exception e) {
                log.error("启动线程失败", e);
            }

        } else {
            log.error("能力线程调度失败，获取能力失败:{}", abilityId);
        }
        return true;
    }

    /**
     * 构造能力处理器
     *
     * @param
     * @param appId
     * @return
     */
    private AbilityCallThreadHandlerService buildAbilityCallThreadHandler(String servingType, TaskAbilityEntity ability, String appId) {
        return abilityQueueServiceProxy.getAbilityCallThreadHandlerService(servingType, ability, appId);
    }


    @Override
    public Boolean shutDownAbilitySchedule(String scheduleKey) {
        //能力调度标识
        if (HANDLERS.containsKey(scheduleKey)) {
            AbilityCallThreadHandlerService abilityCallThreadHandler = HANDLERS.get(scheduleKey);
            abilityCallThreadHandler.shutdown(true);
            HANDLERS.remove(scheduleKey);
        } else {
            throw new AbilityScheduledException("未找到对应的能力处理器：{}", scheduleKey);
        }
        return true;
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("准备关闭能力调度器");
    }

    @Override
    public void destroy() throws Exception {
        log.info("关闭能力调度器bean");
        //停止生产者
        this.abilityProducer.shutdown();
        //关闭线程
        if (HANDLERS.size() > 0) {
            HANDLERS.values().forEach(t -> t.shutdown(true));
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DefaultMQProducer producer = new DefaultMQProducer("ability_schedule");
        // 发送失败重试次数
        producer.setRetryTimesWhenSendFailed(3);
        producer.setNamesrvAddr(rocketMqAdminConfProperties.getNamesrvAddr());
        try {
            producer.start();
        } catch (MQClientException e) {
            log.error("[处理能力生产者]加载异常!", e);
            throw new AbilityScheduledException("[处理能力生产者]加载异常!");
        }
        this.abilityProducer = producer;
        log.info("[处理队列任务生产者]--加载完成!");
    }


    /**
     * 查看调度处理线程是否处于饱和状态
     *
     * @param abilityId
     * @return
     */
    private Boolean shouldSendToScheduleThread(Long abilityId, Integer taskOrder, String tag, List<String> taskSubEncodeMessage) {
        if (BooleanUtil.isTrue(sendToThread)) {
            String scheduleKey = "0_" + abilityId;
            if (HANDLERS.containsKey(scheduleKey)) {
                AbilityCallThreadHandlerService abilityCallThreadHandler = HANDLERS.get(scheduleKey);
                return !abilityCallThreadHandler.scheduleThreadFull(taskOrder, tag, taskSubEncodeMessage);
            } else {
                //当前容器内未找到此调度线程则
                return false;
            }
        } else {
            return false;
        }

    }

    @Override
    public Set<String> getUniqueAbilityUrl(String scheduleKey) {
        //能力调度标识
        if (HANDLERS.containsKey(scheduleKey)) {
            AbilityCallThreadHandlerService abilityCallThreadHandler = HANDLERS.get(scheduleKey);
            return abilityCallThreadHandler.getUniqueAbilityUrl();
        } else {
            throw new AbilityScheduledException("未找到对应的能力处理器：{}", scheduleKey);
        }
    }

    @Override
    public List<AbilityCallMetricData> getAbilityCallData(String scheduleKey) {
        //能力调度标识
        if (HANDLERS.containsKey(scheduleKey)) {
            AbilityCallThreadHandlerService abilityCallThreadHandler = HANDLERS.get(scheduleKey);
            return abilityCallThreadHandler.getAbilityCallData();
        } else {
            throw new AbilityScheduledException("未找到对应的能力处理器：{}", scheduleKey);
        }
    }

    @Override
    public void sendMqMessage(String strategy, Long appId, Long abilityId, Integer taskOrder, String tag, List<String> taskSubEncodeMessage) {
        //下发任务处理队列||中间件rocketMq
        Boolean shouldSendToScheduleThread = shouldSendToScheduleThread(abilityId, taskOrder, tag, taskSubEncodeMessage);
        if (!BooleanUtil.isTrue(shouldSendToScheduleThread)) {
            sendToRocketMq(strategy, abilityId, taskOrder, tag, taskSubEncodeMessage);
        }
    }


    /**
     * 推送到mq
     *
     * @param abilityId
     * @param taskOrder
     * @param taskSubEncodeMessage
     */
    private void sendToRocketMq(String strategy, Long abilityId, Integer taskOrder, String tag, List<String> taskSubEncodeMessage) {
        abilityQueueServiceProxy.sendToQueue(strategy, abilityId, taskOrder, tag, taskSubEncodeMessage);
    }

    @Override
    public ConcurrentHashMap<String, AbilityCallThreadHandlerService> getNodeAbilityCallThread() {
        return this.HANDLERS;
    }

    @Override
    public void removeNodeAbilityCallThread() {
        Set<Map.Entry<String, AbilityCallThreadHandlerService>> entries = this.HANDLERS.entrySet();
        LocalDateTime now = LocalDateTime.now();

        for (Map.Entry<String, AbilityCallThreadHandlerService> t : entries) {
            String key = t.getKey();
            AbilityCallThreadHandlerService callThreadHandler = t.getValue();
            CallAbilityTimeDTO lastCallAbilityTime = callThreadHandler.getLastCallAbilityTime();
            log.debug("本节点调度线程，key:{},线程:{},最近执行调度时间：{},剩余并发数：{}", key, callThreadHandler.getServiceName(), lastCallAbilityTime.getLastCallAbilityTime(), lastCallAbilityTime.getLeftOccupy());
            if (lastCallAbilityTime.getLastCallAbilityTime() != null) {
                long between = LocalDateTimeUtil.between(lastCallAbilityTime.getLastCallAbilityTime(), now, ChronoUnit.MINUTES);
                if (Math.abs(between) > noTaskShutTime) {
                    log.info("{},调度线程上次执行调度时间过长，准备关闭", key);
                    callThreadHandler.shutdown(true);
                    this.HANDLERS.remove(key);
                }
            }
        }

    }

    @Override
    public Boolean jumpAbilityQueue(String topic, String queueId) {
        if (StrUtil.isBlank(topic)) {
            throw new BusinessException("跳过topic名称不能为空");
        }
        try {
            TopicStatsTable topicStatsTable = rocketMqManageToolService.examineTopicStats(topic);
            HashMap<MessageQueue, TopicOffset> offsetTable = topicStatsTable.getOffsetTable();
            for (Map.Entry<MessageQueue, TopicOffset> m : offsetTable.entrySet()) {
                MessageQueue key = m.getKey();
                TopicOffset value = m.getValue();
                if (value != null && value.getMaxOffset() > 0) {
                    String queueName = topic + "_" + key.getQueueId() + ":" + key.getBrokerName();
                    if (StrUtil.isNotBlank(queueId)) {
                        if (queueId.equals(String.valueOf(key.getQueueId()))) {
                            jump(queueName, value.getMaxOffset(), key.getBrokerName());
                        }
                    } else {
                        jump(queueName, value.getMaxOffset(), key.getBrokerName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("跳过消费进度信息失败", e);
            return false;
        }

        return true;
    }

    /**
     * 跳过
     *
     * @param queueName
     * @param offset
     * @param brokerName
     */
    public void jump(String queueName, Long offset, String brokerName) {
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
            stringRedisConn.set(queueName, String.valueOf(offset));
            stringRedisConn.hSet(CommonConstant.MESSAGE_OFF_SET, queueName, String.valueOf(offset));
            return null;
        });
        TaskQueueOffset taskQueueOffset = new TaskQueueOffset();
        taskQueueOffset.setQueueName(queueName);
        taskQueueOffset.setOffset(offset);
        taskQueueOffset.setBrokerName(brokerName);
        taskQueueOffsetService.saveOrUpdate(taskQueueOffset);
    }


    @Override
    public List<MessageQueueDto> getMqQueueTrafficInfo(List<TaskQueueInfo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> topics = list.stream().map(TaskQueueInfo::getTopicName).distinct().collect(Collectors.toList());
        List<Long> abilities = list.stream().map(TaskQueueInfo::getAbilityId).distinct().collect(Collectors.toList());
        List<MessageQueueDto> messageQueues = new ArrayList<>();
        for (String topic : topics) {
            List<MessageQueueDto> abilityOffset = getAbilityOffset(topic, abilities);
            messageQueues.addAll(abilityOffset);
        }
        return messageQueues;
    }

    @Override
    public void setTaskSkip(Boolean skip) {
        if (skip) {
            log.info("redis内存达到阀值，触发跳过任务");
        } else {
            log.debug("redis内存正常状态，正常执行任务");
        }

        this.skipTask = skip;
        if (HANDLERS.size() > 0) {
            Set<Map.Entry<String, AbilityCallThreadHandlerService>> entries = this.HANDLERS.entrySet();
            for (Map.Entry<String, AbilityCallThreadHandlerService> t : entries) {
                AbilityCallThreadHandlerService callThreadHandler = t.getValue();
                callThreadHandler.setSkipTask(skip);
            }
        }
    }

    @Override
    public List<LocalMessageQueueDTO> getLocalQueueInfo(Integer isAllFlag) {
        List<LocalMessageQueueDTO> localMessageQueue = new ArrayList<>();
        if (MapUtil.isNotEmpty(HANDLERS)) {
            Set<Map.Entry<String, AbilityCallThreadHandlerService>> entries = this.HANDLERS.entrySet();
            for (Map.Entry<String, AbilityCallThreadHandlerService> t : entries) {
                AbilityCallThreadHandlerService callThreadHandler = t.getValue();
                localMessageQueue.add(callThreadHandler.getLocalQueueInfo());
            }
        }
        return localMessageQueue;

    }

    @Override
    public Boolean clearLocalQueueInfo() {
        log.info("clearLocalQueueInfo");
        if (MapUtil.isNotEmpty(HANDLERS)) {
            Set<Map.Entry<String, AbilityCallThreadHandlerService>> entries = this.HANDLERS.entrySet();
            for (Map.Entry<String, AbilityCallThreadHandlerService> t : entries) {
                AbilityCallThreadHandlerService callThreadHandler = t.getValue();
                callThreadHandler.clearLocalQueueInfo();
            }
        }
        return true;
    }

    /**
     * 获取单个topic中的数据
     *
     * @param topicName
     * @return
     */
    private List<MessageQueueDto> getAbilityOffset(String topicName, List<Long> abilities) {
        List<MessageQueueDto> messageQueues = new ArrayList<>();
        try {
            TopicStatsTable topicStatsTable = rocketMqManageToolService.examineTopicStats(topicName);
            HashMap<MessageQueue, TopicOffset> offsetTable = topicStatsTable.getOffsetTable();
            //队列消费进度
            Map<String, Long> abilityConsumeOffset = messageQueueOffSetService.getAbilityConsumeOffset();
            for (Map.Entry<MessageQueue, TopicOffset> m : offsetTable.entrySet()) {
                MessageQueue key = m.getKey();
                TopicOffset value = m.getValue();
                String abilityId = String.valueOf(key.getQueueId());
                if (topicName.contains("%")) {
                    List<String> split = StrUtil.split(topicName, '%');
                    String last = CollectionUtil.getLast(split);
                    abilityId = abilityTopicService.getAbilityIdFromQueueId(key.getQueueId(), Integer.parseInt(last)) + "";
                }
                if (value.getMaxOffset() >= 0 && abilities.contains(Long.parseLong(abilityId))) {
                    String queueOffsetKey = key.getTopic() + "_" + key.getQueueId() + ":" + key.getBrokerName();
                    Long offSet = abilityConsumeOffset.containsKey(queueOffsetKey) ?
                            abilityConsumeOffset.get(queueOffsetKey)
                            : messageQueueOffSetService.getMessageQueueOffset(queueOffsetKey);
                    MessageQueueDto messageQueueDto = new MessageQueueDto();
                    messageQueueDto.setTopic(key.getTopic());
                    messageQueueDto.setQueueName(queueOffsetKey);
                    messageQueueDto.setAbilityId(abilityId);
                    messageQueueDto.setBrokerName(key.getBrokerName());
                    messageQueueDto.setQueueId(key.getQueueId());
                    messageQueueDto.setMaxOffset(value.getMaxOffset());
                    messageQueueDto.setMinOffset(value.getMinOffset());
                    messageQueueDto.setCurrentOffSet(offSet);
//                    messageQueueDto.setConsumerGroup(t.getConsumerGroup());
                    messageQueueDto.setLineCount((value.getMaxOffset() - offSet));
                    messageQueues.add(messageQueueDto);
                }
            }
        } catch (Exception e) {
            log.error("获取消费进度信息失败", e);
        }
        return messageQueues;
    }
}
