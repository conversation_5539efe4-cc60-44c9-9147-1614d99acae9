package com.vos.task.poll.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.entity.BaseEntity;
import com.vos.task.poll.api.entity.model.TaskQueueOffset;
import com.vos.task.poll.service.service.TaskQueueOffsetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.admin.TopicOffset;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/21
 * @description: com.vos.task.poll.service.handler
 */
@Slf4j
@Service
public class MessageQueueOffSetServiceImpl implements IMessageQueueOffSetService {

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    private TaskQueueOffsetService offsetService;

    @Override
    public long getMessageQueueOffset(String queueOffsetKey) {
        try {
            String offSet = stringRedisTemplate.opsForValue().get(queueOffsetKey);
            if (offSet != null && StrUtil.isNotBlank(offSet)) {
                return Long.parseLong(offSet);
            } else {
                // 从数据库取
                TaskQueueOffset taskQueueOffset = offsetService.lambdaQuery()
                        .eq(TaskQueueOffset::getQueueName, queueOffsetKey)
                        .eq(BaseEntity::getIsDeleted, 0).last("limit 1").one();
                if (taskQueueOffset != null && taskQueueOffset.getOffset() != null) {
                    stringRedisTemplate.opsForValue().set(queueOffsetKey, taskQueueOffset.getOffset().toString(), 1, TimeUnit.HOURS);
                    return taskQueueOffset.getOffset();
                } else {
                    stringRedisTemplate.opsForValue().set(queueOffsetKey, "0", 2, TimeUnit.MINUTES);
                    return 0;
                }
            }
        } catch (Exception e) {
            log.error("从redis中获取当前队列的消费offset失败：", e);
            throw e;
        }
    }

    @Override
    public void setMessageQueueOffset(String queueOffsetKey, Long offSet) {
        //pipeline设置缓存
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
            stringRedisConn.set(queueOffsetKey, String.valueOf(offSet));
            stringRedisConn.hSet(CommonConstant.MESSAGE_OFF_SET, queueOffsetKey, String.valueOf(offSet));
            return null;
        });
        //每十次更新一次数据库减少数据库io
        if (offSet % 30 == 0) {
            // 此处注意通道名称不能重复
            TaskQueueOffset taskQueueOffset = new TaskQueueOffset();
            taskQueueOffset.setQueueName(queueOffsetKey);
            taskQueueOffset.setOffset(offSet);
            offsetService.saveOrUpdate(taskQueueOffset);
        }
    }

    @Override
    public void setRatioAbilityMaxOffset(HashMap<String, String> abilityMaxOffsetMap) {
        // 缓存到redis中
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
            stringRedisConn.hMSet(CommonConstant.RATIO_MAX_OFF_SET, abilityMaxOffsetMap);
            return null;
        });

    }

    /**
     * 获取算法真实消费进度
     *
     * @return
     */
    @Override
    public HashMap<String, Long> getAbilityConsumeOffset() {
        HashMap<String, Long> abilityConsumeOffsetMap = new HashMap<>(32);
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(CommonConstant.MESSAGE_OFF_SET);
        if (MapUtil.isNotEmpty(entries)) {
            for (Map.Entry<Object, Object> m : entries.entrySet()) {
                abilityConsumeOffsetMap.put(m.getKey().toString(), Long.parseLong(m.getValue().toString()));
            }
        }
        return abilityConsumeOffsetMap;
    }

    @Override
    public HashMap<String, Long> getRatioAbilityMaxOffset() {
        HashMap<String, Long> abilityConsumeOffsetMap = new HashMap<>(32);
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(CommonConstant.RATIO_MAX_OFF_SET);
        if (MapUtil.isNotEmpty(entries)) {
            for (Map.Entry<Object, Object> m : entries.entrySet()) {
                abilityConsumeOffsetMap.put(m.getKey().toString(), Long.parseLong(m.getValue().toString()));
            }
        }
        return abilityConsumeOffsetMap;
    }

    @Override
    public void saveCache(String scheduleKey, List<Message> list) {
        if (StrUtil.isNotBlank(scheduleKey) && CollectionUtil.isNotEmpty(list)) {
            stringRedisTemplate.opsForValue().set(scheduleKey, JSON.toJSONString(list), 30, TimeUnit.MINUTES);
        }
    }

    @Override
    public List<Message> loadCache(String scheduleKey) {
        String s = stringRedisTemplate.opsForValue().get(scheduleKey);
        if (StrUtil.isNotBlank(s)) {
            try {
                return JSON.parseArray(s, Message.class);
            } catch (Exception e) {
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    @Override
    public void removeCache(String scheduleKey) {
        stringRedisTemplate.delete(scheduleKey);
    }
}
