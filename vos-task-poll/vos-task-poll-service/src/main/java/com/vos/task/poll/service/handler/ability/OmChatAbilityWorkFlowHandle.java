package com.vos.task.poll.service.handler.ability;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.chat.*;
import com.vos.kernel.common.enums.ChatEventEnum;
import com.vos.kernel.common.meta.ChatModelMetaInfo;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import com.vos.task.poll.service.convert.ResponseEntityConvert;
import com.vos.task.poll.service.handler.ability.ohc.ChatStreamMessage;
import com.vos.task.poll.service.handler.ability.ohc.OhcProtostuffChatStreamMessageSerializer;
import com.vos.task.poll.service.handler.ability.ohc.OhcStringSerializer;
import com.vos.task.poll.service.handler.ability.stream.AgentErrorMessage;
import com.vos.task.poll.service.handler.ability.stream.AgentMessage;
import com.vos.task.poll.service.handler.ability.stream.AgentStreamMessage;
import com.vos.task.poll.service.handler.ability.stream.RedisMessageProducer;
import com.vos.task.poll.service.handler.workflow.IWorkflowTaskResultBackService;
import lombok.extern.slf4j.Slf4j;
import org.caffinitas.ohc.Eviction;
import org.caffinitas.ohc.OHCache;
import org.caffinitas.ohc.OHCacheBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月10日
 * @version: 1.0
 * @description: 更新编排任务状态；流式输出结果到redis队列、业务端监听结果
 */
@Slf4j
@Component
public class OmChatAbilityWorkFlowHandle {

    public static final String STREAM_NAME_KEY_PREFIX = "agent_os:conversation:output:";

    @Resource
    IWorkflowTaskResultBackService workflowTaskResultBackService;

    @Resource
    RedisMessageProducer redisMessageProducer;

    @Resource
    ResponseEntityConvert responseEntityConvert;

    //ohc存储
    OHCache<String, ChatStreamMessage> basicStoreInfoCache = OHCacheBuilder.<String, ChatStreamMessage>newBuilder()
            .keySerializer(new OhcStringSerializer())
            .valueSerializer(new OhcProtostuffChatStreamMessageSerializer())
            .capacity(128 * 1024 * 1024)
            .eviction(Eviction.LRU)
            .timeouts(false)
            .build();


    /**
     * 工作流状态变更回调
     *
     * @param chatCallBackResult
     */
    void workflowCallBackNormal(String metaInfo, ChatCallBackDTO chatCallBackResult, Long handleTime, Boolean shouldOutPut) {

        ChatModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ChatModelMetaInfo.class);
        WorkflowCallBackContent workflowCallBackContent = buildWorkflowCallBackContent(metaInfoBean, chatCallBackResult, handleTime);
        //编排任务处理
        workflowTaskResultBackService.workflowTaskResultBack(chatCallBackResult.getAbilityId(), metaInfoBean.getCallBackUrl(), workflowCallBackContent);

        if (BooleanUtil.isTrue(shouldOutPut) && StrUtil.isNotBlank(metaInfoBean.getConversationId())) {
            String sessionId = STREAM_NAME_KEY_PREFIX + metaInfoBean.getConversationId();
            List<AgentStreamMessage> agentStreamMessage = buildAgentMessageList(metaInfoBean, chatCallBackResult);
            if (CollectionUtil.isNotEmpty(agentStreamMessage)) {
                for (AgentStreamMessage agentStreamMessage1 : agentStreamMessage) {
                    redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage1);
                }
            }
        }
    }

    /**
     * 流式任务状态变更回调
     *
     * @param chatCallBackResult
     */
    void workflowErrorCallBackStream(String metaInfo, ChatCallBackDTO chatCallBackResult, Long handleTime) {
        // 流式输出
        ChatModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ChatModelMetaInfo.class);
        if (StrUtil.isNotBlank(metaInfoBean.getConversationId())) {
            String sessionId = STREAM_NAME_KEY_PREFIX + metaInfoBean.getConversationId();
            AgentStreamMessage agentStreamMessage = buildAgentMessage(metaInfoBean, chatCallBackResult);
            redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage);
            redisMessageProducer.streamMessageExpire(sessionId);
        }
        //回传数据到编排
        workflowCallBackNormal(metaInfo, chatCallBackResult, handleTime, false);
    }


    /**
     * 构建编排任务回调内容
     *
     * @param metaInfoBean
     * @param chatCallBackResult
     * @return
     */
    private List<AgentStreamMessage> buildAgentMessageList(ChatModelMetaInfo metaInfoBean, ChatCallBackDTO chatCallBackResult) {
        ArrayList<AgentStreamMessage> agentStreamMessages = new ArrayList<>();

        ChatCompletionResponse chatCompletionResponse = chatCallBackResult.getChatCompletionResponse();
        AgentMessage agentMessage = new AgentMessage();
        agentMessage.setCreateTime(metaInfoBean.getEventTime());
        agentMessage.setEndTime(new Date().getTime());
        agentMessage.setAgentId(metaInfoBean.getAgentId());
        agentMessage.setChatId(metaInfoBean.getAgentId());
        agentMessage.setSession(metaInfoBean.getAgentId());
        agentMessage.setConversationId(chatCallBackResult.getAppSourceId());
        AgentStreamMessage agentStreamMessage = new AgentStreamMessage();
        if (chatCompletionResponse.getCode() != 200) {
            agentStreamMessage.setEvent(ChatEventEnum.MESSAGE_ERROR.getEvent());
            agentMessage.setStatus("error");
            agentMessage.setError(new AgentErrorMessage(chatCompletionResponse.getCode().toString(), chatCompletionResponse.getError()));
            agentStreamMessage.setData(agentMessage);
            agentStreamMessages.add(agentStreamMessage);
        } else {
            //functionCall 不流式
            ChatCompletionChoice first = CollectionUtil.getFirst(chatCompletionResponse.getChoices());
            if ("tool_calls".equals(first.getFinish_reason())) {
                return agentStreamMessages;
            }
            agentStreamMessage.setEvent(ChatEventEnum.MESSAGE_DELTA.getEvent());
            agentMessage.setStatus("delta");
            agentMessage.setIsFinish(false);
            agentMessage.setContentType("text");
            agentMessage.setContent(CollectionUtil.getFirst(chatCompletionResponse.getChoices()).getMessage().getContent());
            agentMessage.setType("answer");
            agentStreamMessage.setData(agentMessage);
            agentStreamMessages.add(agentStreamMessage);
            //增加完结节点
            AgentStreamMessage agentStreamMessageOver = new AgentStreamMessage();
            agentStreamMessageOver.setEvent(ChatEventEnum.MESSAGE_COMPLETED.getEvent());
            AgentMessage agentMessage1 = responseEntityConvert.agentMessageConverto(agentMessage);
            agentMessage1.setStatus("completed");
            agentMessage.setContent("");
            agentMessage.setIsFinish(true);
            agentStreamMessageOver.setData(agentMessage1);
            agentStreamMessages.add(agentStreamMessageOver);
        }
        return agentStreamMessages;
    }


    /**
     * 构建agentMessage
     *
     * @param metaInfoBean
     * @param chatCallBackResult
     * @return
     */
    private AgentStreamMessage buildAgentMessage(ChatModelMetaInfo metaInfoBean, ChatCallBackDTO chatCallBackResult) {

        AgentStreamMessage agentStreamMessage = new AgentStreamMessage();

        ChatCompletionResponse chatCompletionResponse = chatCallBackResult.getChatCompletionResponse();
        AgentMessage agentMessage = new AgentMessage();
        agentMessage.setAgentId(metaInfoBean.getAgentId());
        agentMessage.setSession(chatCallBackResult.getAppSourceId());
        agentMessage.setChatId(metaInfoBean.getAgentId());
        agentMessage.setConversationId(metaInfoBean.getConversationId());
        agentMessage.setCreateTime(metaInfoBean.getEventTime());
        agentMessage.setEndTime(new Date().getTime());
        if (chatCompletionResponse.getCode() != 200) {
            agentStreamMessage.setEvent(ChatEventEnum.MESSAGE_ERROR.getEvent());
            agentMessage.setStatus("error");
            agentMessage.setError(new AgentErrorMessage(chatCompletionResponse.getCode().toString(), chatCompletionResponse.getError()));
        } else {
            agentStreamMessage.setEvent(BooleanUtil.isTrue(chatCallBackResult.getEnd()) ? ChatEventEnum.MESSAGE_COMPLETED.getEvent() : ChatEventEnum.MESSAGE_DELTA.getEvent());
            agentMessage.setStatus(BooleanUtil.isTrue(chatCallBackResult.getEnd()) ? "completed" : "delta");
            agentMessage.setIsFinish(chatCallBackResult.getEnd());
            agentMessage.setContentType("text");
            ChatCompletionChoice first = CollectionUtil.getFirst(chatCompletionResponse.getChoices());
            agentMessage.setContent(null == first.getDelta().getReasoning_content() ? first.getDelta().getContent() : first.getDelta().getReasoning_content());
            agentMessage.setType(null == first.getDelta().getReasoning_content() ? "answer" : "thinking");
        }
        agentStreamMessage.setData(agentMessage);
        return agentStreamMessage;
    }


    /**
     * 流式输出前token及耗时
     *
     * @param sessionId
     * @param metaInfoBean
     * @param chatCompletionResponse
     * @param handleTime
     */
    private void preEndOfStreamMessage(String appSourceId, String sessionId, ChatModelMetaInfo metaInfoBean, ChatCompletionResponse chatCompletionResponse, Long handleTime) {
        AgentStreamMessage agentStreamMessage = new AgentStreamMessage();

        AgentMessage agentMessage = new AgentMessage();
        agentMessage.setSession(appSourceId);
        agentMessage.setAgentId(metaInfoBean.getAgentId());
        agentMessage.setChatId(metaInfoBean.getAgentId());
        agentMessage.setConversationId(metaInfoBean.getConversationId());
        agentMessage.setCreateTime(metaInfoBean.getEventTime());
        agentMessage.setEndTime(new Date().getTime());

        agentStreamMessage.setEvent(ChatEventEnum.MESSAGE_DELTA.getEvent());
        agentMessage.setStatus("delta");
        agentMessage.setIsFinish(false);
        agentMessage.setContentType("text");
        agentMessage.setContent(CollectionUtil.getFirst(chatCompletionResponse.getChoices()).getDelta().getContent());
        agentMessage.setType("runner_output");
        agentMessage.setCostTime(new Date().getTime() - handleTime);
        if (chatCompletionResponse.getUsage() != null) {
            agentMessage.setTokens(chatCompletionResponse.getUsage().getCompletion_tokens().longValue());
        }
        agentStreamMessage.setData(agentMessage);
        redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage);
    }

    /**
     * 流式任务状态变更回调
     *
     * @param chatCallBackResult
     */
    void workflowCallBackStream(ChatCallBackDTO chatCallBackResult, Long handleTime, Boolean begin) {
        ChatCompletionResponse chatCompletionResponse = chatCallBackResult.getChatCompletionResponse();
        String metaInfo = chatCallBackResult.getMetaInfo();

        if (chatCompletionResponse.getCode() != 200) {
            //异常处理
            workflowErrorCallBackStream(metaInfo, chatCallBackResult, handleTime);
        } else {
            String appSourceId = chatCallBackResult.getAppSourceId();
            // 流式输出
            ChatModelMetaInfo metaInfoBean = JSON.parseObject(metaInfo, ChatModelMetaInfo.class);
            String sessionId = "";
            if (StrUtil.isNotBlank(metaInfoBean.getConversationId())) {
                sessionId = STREAM_NAME_KEY_PREFIX + metaInfoBean.getConversationId();
                if (BooleanUtil.isTrue(chatCallBackResult.getEnd())) {
                    preEndOfStreamMessage(appSourceId, sessionId, metaInfoBean, chatCompletionResponse, handleTime);
                }
                AgentStreamMessage agentStreamMessage = buildAgentMessage(metaInfoBean, chatCallBackResult);
                redisMessageProducer.sendSimpleMessage(sessionId, agentStreamMessage);
            }
            //缓存聊天数据
            ChatStreamMessage chatStreamMessage = basicStoreInfoCache.get(appSourceId);
            if (null == chatStreamMessage) {
                basicStoreInfoCache.put(appSourceId, new ChatStreamMessage(chatCompletionResponse));
            } else {
                ChatCompletionResponse cached = chatStreamMessage.getChatCompletionResponse();
                List<ChatCompletionChoice> choices = cached.getChoices();
                chatCompletionResponse.getChoices().addAll(choices);
                if (BooleanUtil.isTrue(chatCallBackResult.getEnd())) {
                    // 移除缓存
                    basicStoreInfoCache.remove(appSourceId);
                    Collections.reverse(chatCompletionResponse.getChoices());
                } else {
                    basicStoreInfoCache.put(appSourceId, new ChatStreamMessage(chatCompletionResponse));
                }
            }
            if (BooleanUtil.isTrue(chatCallBackResult.getEnd())) {
                if (StrUtil.isNotBlank(sessionId)) {
                    redisMessageProducer.streamMessageExpire(sessionId);
                }
                ChatCompletionResponse chatCompletionResponse1 = new ChatCompletionResponse();
                List<ChatCompletionChoice> choices = chatCompletionResponse.getChoices();
                ChatCompletionChoice chatCompletionChoice = new ChatCompletionChoice();
                if (CollectionUtil.isNotEmpty(choices)) {
                    AssistantMessage assistantMessage = new AssistantMessage();
                    assistantMessage.setRole("assistant");
                    assistantMessage.setContent(choices.stream().map(ChatCompletionChoice::getDelta).map(Delta::getContent).filter(StrUtil::isNotBlank).collect(Collectors.joining()));
                    assistantMessage.setReasoning_content(choices.stream().map(ChatCompletionChoice::getDelta).map(Delta::getReasoning_content).filter(StrUtil::isNotBlank).collect(Collectors.joining()));
                    chatCompletionChoice.setMessage(assistantMessage);
                }
                chatCompletionResponse1.setChoices(Collections.singletonList(chatCompletionChoice));
                chatCompletionResponse1.setUsage(chatCompletionResponse.getUsage());
                chatCompletionResponse1.setId(appSourceId);
                chatCompletionResponse.setCreated(handleTime);
                chatCallBackResult.setChatCompletionResponse(chatCompletionResponse1);

                // 回传数据到编排
                workflowCallBackNormal(metaInfo, chatCallBackResult, handleTime, false);
            }
        }

    }


    /**
     * 构建workflow回调内容
     *
     * @param metaInfo
     * @return
     */
    WorkflowCallBackContent buildWorkflowCallBackContent(ModelMetaInfo metaInfo, ChatCallBackDTO chatCallBack, Long handleTime) {
        WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
        ChatCompletionResponse chatCompletionResponse = chatCallBack.getChatCompletionResponse();
        String appSourceId = chatCallBack.getAppSourceId();
        if (StrUtil.isNotBlank(metaInfo.getBizMeta()) && metaInfo.getBizMeta().contains("{")) {
            workflowCallBackContent.setTransmissionParams(JSONObject.parseObject(metaInfo.getBizMeta()));
        }
        WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
        workflowCallBackContentItem.setSourceId(appSourceId);
        workflowCallBackContentItem.setStartTime(handleTime);
//        workflowCallBackContentItem.setWorkerId(this.hostName);
        Integer handleStatus = chatCompletionResponse.getCode();
        workflowCallBackContentItem.setStatus(200 == handleStatus ? WorkflowStatusEnum.COMPLETED : WorkflowStatusEnum.FAILED);
        //结果输出
        if (200 != handleStatus) {
            workflowCallBackContentItem.setReasonForIncompletion(chatCompletionResponse.getError());
        }
        chatCompletionResponse.setId(appSourceId);
        chatCompletionResponse.setCreated(handleTime);
        workflowCallBackContentItem.setOutputData(chatCompletionResponse);
        workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
        return workflowCallBackContent;
    }
}
