package com.vos.task.poll.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.executor.ExecutorFactory;
import com.alibaba.nacos.common.executor.NameThreadFactory;
import com.linker.basic.exception.BusinessException;
import com.linker.log.aop.LogMetricHandler;
import com.linker.log.enums.MetricPattern;
import com.linker.log.utils.TraceLogUtil;
import com.linker.omos.client.config.ItemCodeEnum;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.automated.api.service.rpc.IServingRouterRpcService;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;
import com.vos.task.poll.api.entity.dto.ReCalculateTopicAndQueueResultDTO;
import com.vos.task.poll.api.service.CommonService;
import com.vos.task.poll.service.config.AbilityScheduledException;
import com.vos.task.poll.service.config.LogMetricConfig;
import com.vos.task.poll.service.curator.AbilityCallThreadCheckService;
import com.vos.task.poll.service.entity.AbilityCallMetricData;
import com.vos.task.poll.service.entity.AbilityDeleteException;
import com.vos.task.poll.service.entity.CallAbilityTimeDTO;
import com.vos.task.poll.service.handler.ability.AbilityClientHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPullConsumer;
import org.apache.rocketmq.client.consumer.PullResult;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * AI能力执行线程
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/20
 * @description: com.vos.task.poll.service.handler
 */
@Slf4j
public class AbilityHttpCallThreadHandlerV3 extends AbilityCallThread implements AbilityCallThreadHandlerService {

    /**
     * queue编号
     */
    private String queueId;


    /**
     * api调用topic
     */
    private String apiTopic;

    /**
     * 任务调用时使用topic
     */
    private String taskTopic;


    /**
     * 快队列缓存消息数据
     */
    private volatile LinkedBlockingDeque<Message> apiMessageQueue;


    /**
     * 慢队列缓存消息数据
     */
    private volatile LinkedBlockingDeque<Message> taskMessageQueue;


    /**
     * MessageQueue
     */
    Set<MessageQueue> apiMessageQueueList;

    /**
     * MessageQueue
     */
    Set<MessageQueue> taskMessageQueueList;

    /**
     * 是否初始化cache
     */
    private volatile Boolean hasLoadCache = false;

    /**
     * 是否跳过任务
     */
    private volatile Boolean skipTask = false;


    /**
     * 空处理次数
     */
    private volatile Long emptyHandleNum = 0L;

    private volatile Long apiEmptyHandleNum = 0L;

    /**
     * 需要跳过的最大offSet
     */
    private volatile Long skipMaxOffset = 0L;


    /**
     * mq堵塞数量
     */
    long realLineCount = 0L;


    /**
     * 获取当前的跳过任务状态
     *
     * @return
     */
    public Boolean getSkipTaskCurrentStatus() {
        return skipTask;
    }

    /**
     * 设置是否跳过任务
     *
     * @param isSkip
     */
    @Override
    public void setSkipTask(Boolean isSkip) {
        this.skipTask = isSkip;
        if (BooleanUtil.isTrue(isSkip)) {
            this.pullMqSizeBatch = 32;
        } else {
            this.pullMqSizeBatch = 4;
        }
    }


    /**
     * 获取上次执行算法调度时间
     *
     * @return
     */
    @Override
    public CallAbilityTimeDTO getLastCallAbilityTime() {
        return getLastCallAbilityTimeIndeed();
    }

    @Override
    public void start() {
        super.start();
    }

    @Override
    public Boolean scheduleThreadFull(Integer taskOrder, String tag, List<String> taskSubEncodeMessage) {
        if (taskOrder == 1) {
            return sendInner(taskOrder, taskSubEncodeMessage);
        } else {
            if (abilityHttpConcurrentLimit != null) {
                return sendInner(taskOrder, taskSubEncodeMessage);
            } else {
                return true;
            }
        }
    }

    /**
     * 加入内存队列
     *
     * @param taskOrder
     * @param taskSubEncodeMessage
     * @return
     */
    private synchronized Boolean sendInner(Integer taskOrder, List<String> taskSubEncodeMessage) {
        String topic = taskOrder == 1 ? apiTopic : taskTopic;
        List<Message> messages = new ArrayList<>();
        if (taskOrder == 1) {
            if (this.apiMessageQueue.size() < abilityCapacity) {
                for (String msgBody : taskSubEncodeMessage) {
                    // 组装RocketMQ消息体
                    Message message = new Message(topic, msgBody.getBytes());
                    addDirectMessageTrace(message);
                    messages.add(message);
                }
                sendToScheduleThread(taskOrder, messages);
                return false;
            } else {
                return true;
            }
        } else {
            if (this.taskMessageQueue.size() < abilityCapacity) {
                for (String msgBody : taskSubEncodeMessage) {
                    // 组装RocketMQ消息体
                    Message message = new Message(topic, msgBody.getBytes());
                    addDirectMessageTrace(message);
                    messages.add(message);
                }
                sendToScheduleThread(taskOrder, messages);
                return false;
            } else {
                return true;
            }
        }
    }

    @Override
    public void sendToScheduleThread(Integer taskOrder, List<Message> taskMessage) {
        log.info("{}下发任务，直接调度到处理线程,apiS:{},taskS:{},cap:{}", abilityId, apiMessageQueue.size(), taskMessageQueue.size(), abilityCapacity);
        try {
            if (taskOrder == 1) {
                this.apiMessageQueue.addAll(taskMessage);
                signalTask();
            } else {
                this.taskMessageQueue.addAll(taskMessage);
                signalTask();
            }
        } catch (Exception e) {
            log.error("发送调度处理线程任务添加失败", e);
        }
    }

    @Override
    public LocalMessageQueueDTO getLocalQueueInfo() {
        LocalMessageQueueDTO localMessageQueueDTO = new LocalMessageQueueDTO();
        localMessageQueueDTO.setAbilityId(abilityId);
        localMessageQueueDTO.setCapacity(abilityCapacity);
        localMessageQueueDTO.setLineCount((long) taskMessageQueue.size() + apiMessageQueue.size());

        if (this.abilityHttpConcurrentLimit != null) {
            localMessageQueueDTO.setTps(this.abilityHttpConcurrentLimit.getTps());
            localMessageQueueDTO.setRt(this.abilityHttpConcurrentLimit.getRt());
        }

        List<Dict> details = new ArrayList<>();
        Dict apiLocal = new Dict();
        apiLocal.put("apiLocal", apiMessageQueue.size());
        details.add(apiLocal);
        Dict taskLocal = new Dict();
        taskLocal.put("taskLocal", taskMessageQueue.size());
        details.add(taskLocal);
        localMessageQueueDTO.setDetails(details);
        return localMessageQueueDTO;
    }

    @Override
    public void clearLocalQueueInfo() {
        this.apiMessageQueue.clear();
        this.taskMessageQueue.clear();
        this.apiMessageQueue = new LinkedBlockingDeque<>(10240);
        this.taskMessageQueue = new LinkedBlockingDeque<>(10240);
        log.info("清除本地队列，abilityId：{}", abilityId);
    }

    @Override
    public Set<String> getUniqueAbilityUrl() {
        HashSet<String> strings = new HashSet<>();
        if (abilityHttpConcurrentLimit != null) {
            return abilityHttpConcurrentLimit.getUniqueAbilityUrl();
        }
        return strings;

    }

    @Override
    public List<AbilityCallMetricData> getAbilityCallData() {
        List<AbilityCallMetricData> abilityCallMetricDataList = new ArrayList<>();
        if (abilityHttpConcurrentLimit != null) {
            return abilityHttpConcurrentLimit.getAbilityCallData();
        }
        return abilityCallMetricDataList;
    }

    /**
     * 构造函数
     *
     * @param abilityId
     * @param appId
     * @param nameServer
     * @param messageQueueOffSetService
     */
    public AbilityHttpCallThreadHandlerV3(IAbilityScheduledService scheduledService, String abilityId, String abilityCode, String appId,
                                          String nameServer, IMessageQueueOffSetService messageQueueOffSetService,
                                          CommonService abilityOccupyService, AbilityCallThreadCheckService abilityCallThreadCheckService, AbilityClientHolder abilityClient,
                                          IServingRouterRpcService servingRouterRpcService, Boolean isSkip, LogMetricConfig logMetricConfig,
                                          ITaskCacheRpcService taskCacheRpcService, Integer abilityEnum, AbilityCallConfig abilityCallConfig) {

        super(scheduledService, abilityId, abilityCode, appId, nameServer, messageQueueOffSetService, abilityOccupyService, abilityCallThreadCheckService,
                abilityClient, servingRouterRpcService, logMetricConfig, taskCacheRpcService, abilityEnum, abilityCallConfig);

        this.queueId = abilityId;
        this.apiTopic = CommonConstant.API_TOPIC + appId;
        this.taskTopic = CommonConstant.TASK_TOPIC + appId;
        this.apiMessageQueue = new LinkedBlockingDeque<>(10240);
        this.taskMessageQueue = new LinkedBlockingDeque<>(10240);
        this.skipTask = isSkip;
        //初始化消息消费者
        initPullConsumer(scheduleKey);
        int limit = concurrentControl();
        if (limit == 0) {
            throw new AbilityDeleteException("并发为0，不启动线程");
        }
        abilityCallThreadCheckService.occupyAbilityThreadRun(scheduleKey, getDubboHostIp());
        //请求线程池
        this.executorService = ExecutorBuilder.create()
                .setCorePoolSize(limit)
                .setMaxPoolSize(limit + 32)
                .useSynchronousQueue()
                .build();
        this.lastCallAbilityTime = LocalDateTime.now();
    }

    /**
     * 重新计算能力对应的topic和queue信息
     *
     * @param result
     */
    @Override
    public void reCalculateAbilityTopicAndQueue(ReCalculateTopicAndQueueResultDTO result) {
        if (null != result) {
            this.apiTopic = result.getApiTopic();
            this.taskTopic = result.getTaskTopic();
            this.queueId = result.getQueueNum();
        }
    }


    private void saveCache() {
        List<Message> taskSubsList = new ArrayList<>();
        if (this.apiMessageQueue.size() > 0) {
            List<Message> taskSubsApi = ListUtil.toList(this.apiMessageQueue);
            taskSubsList.addAll(taskSubsApi);
        }
        if (this.taskMessageQueue.size() > 0) {
            List<Message> taskSubs = ListUtil.toList(this.taskMessageQueue);
            taskSubsList.addAll(taskSubs);
        }
        if (CollectionUtil.isNotEmpty(taskSubsList)) {
            messageQueueOffSetService.saveCache(this.scheduleKey, taskSubsList);
        }
    }


    private void loadCache() {
        String cacheKey = this.scheduleKey;
        List<Message> subs = messageQueueOffSetService.loadCache(cacheKey);
        if (CollectionUtil.isNotEmpty(subs)) {
            this.hasLoadCache = true;
            messageQueueOffSetService.removeCache(cacheKey);
            List<Message> apiTasks = subs.stream().filter(t -> t.getTopic().contains("QUICK")).collect(Collectors.toList());
            List<Message> tasks = subs.stream().filter(t -> !t.getTopic().contains("QUICK")).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(apiTasks)) {
                this.apiMessageQueue.addAll(subs);
                signalTask();
            }
            if (CollectionUtil.isNotEmpty(tasks)) {
                this.taskMessageQueue.addAll(tasks);
                signalTask();
            }

        }
    }

    @Override
    public void shutdown(boolean interrupt) {
        log.info("即将关闭算法处理线程：{},{}", scheduleKey, abilityCode);
        //保存数据
        saveCache();
        if (this.scheduledexecutorService != null) {
            this.scheduledexecutorService.shutdown();
        }
        if (this.executorService != null) {
            this.executorService.shutdown();
        }
        if (this.pullExecutorService != null) {
            this.pullExecutorService.shutdown();
        }
        if (this.abilityConsumer != null) {
            this.abilityConsumer.shutdown();
        }
        abilityCallThreadCheckService.releaseAbilityRunThread(scheduleKey);
        if (this.abilityHttpConcurrentLimit != null) {
            this.abilityHttpConcurrentLimit.close();
        }
        signalTask();
        super.shutdown(interrupt);
    }

    /**
     * 处理消息：单独领出来为了skywalking自定义插件对此方法进行监控
     */
    public TaskSub handleMessage(Message messageExt) {
        TaskSub taskSub = JSON.parseObject(new String(messageExt.getBody()), TaskSub.class);
        return taskSub;
    }

    /**
     * 处理消息：直接发送内存队列数据增加链路追踪
     */
    public Message addDirectMessageTrace(Message messageExt) {
        return messageExt;
    }

    /**
     * 获取mq中的消息
     *
     * @return
     */
    private List<MessageExt> getMqMessage(String topic) {
        try {
            StopWatch sw = new StopWatch("耗时统计");
            sw.start();
            Set<MessageQueue> messageQueueList;
            if (apiTopic.equals(topic)) {
                if (apiMessageQueueList != null) {
                    messageQueueList = apiMessageQueueList;
                } else {
                    messageQueueList = abilityConsumer.fetchSubscribeMessageQueues(topic);
                    messageQueueList = messageQueueList.stream().filter(i -> String.valueOf(i.getQueueId()).equals(queueId)).collect(Collectors.toSet());
                    this.apiMessageQueueList = messageQueueList;
                }
            } else {
                if (taskMessageQueueList != null) {
                    messageQueueList = taskMessageQueueList;
                } else {
                    messageQueueList = abilityConsumer.fetchSubscribeMessageQueues(topic);
                    messageQueueList = messageQueueList.stream().filter(i -> String.valueOf(i.getQueueId()).equals(queueId)).collect(Collectors.toSet());
                    this.taskMessageQueueList = messageQueueList;
                }
            }
            if (CollectionUtils.isEmpty(messageQueueList)) {
                return new ArrayList<>();
            }
            List<CompletableFuture<List<MessageExt>>> completableFutures = messageQueueList.stream().map(messageQueue -> CompletableFuture.supplyAsync(() -> getMessage(topic, messageQueue), pullExecutorService)).collect(Collectors.toList());
            //等待所有并行扩容任务结束，获取所有任务执行状态
            List<MessageExt> msgFoundList = completableFutures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList());
            sw.stop();
            if (msgFoundList.size() > 0) {
                log.debug("{},{},拉取mq消息，耗时：{} ms,size:{}", abilityId, abilityCode, sw.getTotalTimeMillis(), msgFoundList.size());
            }
            return msgFoundList;

        } catch (Exception e) {
            log.error("获取topic：{}中的消息失败", topic, e);
        }
        return Collections.emptyList();
    }

    /**
     * 拉取消息
     *
     * @param topic
     * @param queue
     * @return
     */
    private List<MessageExt> getMessage(String topic, MessageQueue queue) {
        String queueOffsetKey = topic + "_" + queueId + ":" + queue.getBrokerName();
        try {
            long messageQueueOffset = messageQueueOffSetService.getMessageQueueOffset(queueOffsetKey);
            PullResult pullResult = abilityConsumer.pull(queue, "", messageQueueOffset, pullMqSizeBatch == 0 ? 4 : pullMqSizeBatch);
            switch (pullResult.getPullStatus()) {
                case FOUND:
                    //更新消费进度
                    List<MessageExt> msgFoundList = pullResult.getMsgFoundList();
                    messageQueueOffSetService.setMessageQueueOffset(queueOffsetKey, pullResult.getNextBeginOffset());
                    //阻塞数量
                    long maxOffsetMax = pullResult.getMaxOffset();
                    long realLineCount = maxOffsetMax - messageQueueOffset;
                    if (realLineCount > 0) {
                        this.realLineCount = realLineCount;
                    }
                    if (abilityCallConfig.getLineLimit() != 0 && skipMaxOffset == 0 && realLineCount > abilityCallConfig.getLineLimit()) {
                        log.info("阻塞达到阀值，准备跳过");
                        skipMaxOffset = maxOffsetMax;
                        this.pullMqSizeBatch = 32;
                    } else if (realLineCount < abilityCallConfig.getLineLimit()) {
                        skipMaxOffset = 0L;
                        this.pullMqSizeBatch = 4;
                    }
                    return msgFoundList;
                case OFFSET_ILLEGAL:
                    log.debug("topic={},abilityId={},{},队列中Offset不合法", topic, abilityId, abilityCode);
                    PullResult result = abilityConsumer.pull(queue, "", 0, 1);
                    long maxOffset = result.getMaxOffset();
                    //更正消费进度
                    messageQueueOffSetService.setMessageQueueOffset(queueOffsetKey, maxOffset);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("获取queueOffsetKey：{}中的消息失败", queueOffsetKey, e);
        }
        return new ArrayList<>();
    }


    /**
     * 初始化;定时获取mq消息
     */
    private void init() {
        //定时拉取消息
        this.scheduledexecutorService = ExecutorFactory.newScheduledExecutorService(3, new NameThreadFactory("AbilityHttpCallScheduled-" + scheduleKey + "-"));

        //拉取线程池
        this.pullExecutorService = ExecutorBuilder.create()
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("pull-pool-").build())
                .setCorePoolSize(2)
                .setMaxPoolSize(16)
                .useSynchronousQueue()
                .build();
        //定时获取并发控制
        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                int limit = concurrentControl();
                if (this.executorService.getMaximumPoolSize() != (limit + 32)) {
                    try {
                        //更新最大线程池大小
                        this.executorService.setCorePoolSize(limit);
                        this.executorService.setMaximumPoolSize(limit + 32);
                    } catch (Exception e) {
                        log.warn("更新最大线程池大小发生异常", e);
                    }
                }
            } catch (Exception e) {
                log.warn("能力并发控制获取发生异常", e);
            }
        }, 0L, 1, TimeUnit.SECONDS);

        //定时获取数据
        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                boolean pulled = false;
                long apiDelayTime = 5;
                //获取api调用topic消息，放入到缓存双向链表头部
                if (this.apiMessageQueue.size() < abilityCapacity * 2) {
                    List<MessageExt> mqMessage = getMqMessage(apiTopic);
                    if (CollectionUtil.isNotEmpty(mqMessage)) {
                        pulled = true;
                        this.apiMessageQueue.addAll(mqMessage);
                        signalTask();
                    }
                }
                if (pulled) {
                    apiEmptyHandleNum = 0L;
                } else {
                    apiEmptyHandleNum++;
                    if (apiEmptyHandleNum > 300 && apiEmptyHandleNum < 6000) {
                        apiDelayTime = 10;
                    } else if (apiEmptyHandleNum > 6000 && apiEmptyHandleNum < 18000) {
                        apiDelayTime = 50;
                    } else if (apiEmptyHandleNum > 18000) {
                        apiDelayTime = 500;
                    }
                    ThreadUtil.safeSleep(apiDelayTime);
                }
            } catch (Exception e) {
                log.warn("apiTask拉取发生异常", e);
            }

        }, 1L, delayTime, TimeUnit.MILLISECONDS);

        this.scheduledexecutorService.scheduleWithFixedDelay(() -> {
            try {
                //加载缓存
                if (!BooleanUtil.isTrue(hasLoadCache)) {
                    loadCache();
                }
                boolean pulled = false;
                long taskDelayTime = 5;
                //缓存消息小于某个阀值
                if (this.taskMessageQueue.size() < abilityCapacity * 2) {
                    //获取任务调用时使用topic消息，放入到缓存双向链表尾部
                    List<MessageExt> taskMqMessage = getMqMessage(taskTopic);
                    if (CollectionUtil.isNotEmpty(taskMqMessage)) {
                        pulled = true;
                        //判断是否跳过
                        if (skipMaxOffset > 0) {
                            for (MessageExt e : taskMqMessage) {
                                enterExecutor(e, true, null);
                            }
                        } else {
                            this.taskMessageQueue.addAll(taskMqMessage);
                            signalTask();
                        }
                    }
                }

                if (pulled) {
                    emptyHandleNum = 0L;
                } else {
                    emptyHandleNum++;
                    if (emptyHandleNum > 300 && emptyHandleNum < 6000) {
                        taskDelayTime = 10;
                    } else if (emptyHandleNum > 6000 && emptyHandleNum < 18000) {
                        taskDelayTime = 50;
                    } else if (emptyHandleNum > 18000) {
                        taskDelayTime = 500;
                    }
                    ThreadUtil.safeSleep(taskDelayTime);
                }
            } catch (Exception e) {
                log.warn("能力调用发生异常", e);
            }

        }, 1L, delayTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public String getServiceName() {
        return "AbilityCallThreadHandler_" + scheduleKey;
    }

    @Override
    public void run() {
        log.info("{},启动算法私有调用线程器：{},currentThread:{}", abilityCode, scheduleKey, Thread.currentThread().getId());
        //直接运行
        init();
        handleAbility();
        //任务停止运行
        log.info("AbilityCallThreadHandler ：{},{}, 停止工作", abilityId, abilityCode);
    }


    /**
     * 真实并发数与当前运行数比较
     *
     * @return
     */
    private AbilityRequestUrlDTO canRunTaskLocal() {
        if (this.apiMessageQueue.size() > 0 || this.taskMessageQueue.size() > 0) {
            //计算是否可以调用
            try {
                Dict concurrent = abilityHttpConcurrentLimit.getConcurrent();
                log.info("能力{},{},实时并发数：{},当前剩余数：{}", abilityId, abilityCode, concurrent.get("total"), concurrent.getStr("left"));
                return abilityHttpConcurrentLimit.acquire();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }

    /**
     * 能力处理
     */
    private void abilityCall() {
        boolean call = true;
        while (call) {
            //判断是否可用
            AbilityRequestUrlDTO abilityRequestUrlDTO = canRunTaskLocal();
            if (null != abilityRequestUrlDTO) {
                try {
                    this.lastCallAbilityTime = LocalDateTime.now();
                    Boolean checkAvailable = abilityHttpConcurrentLimit.checkAvailable(abilityRequestUrlDTO);
                    //判断地址是否可用
                    if (!BooleanUtil.isTrue(checkAvailable)) {
                        log.info("{},无效的地址：{},跳过", abilityCode, abilityRequestUrlDTO.getAbilityRequestUrl());
                        continue;
                    }
                    //获取缓存中待处理的消息
                    Message taskSubMq = apiMessageQueue.poll();
                    if (taskSubMq != null) {
                        //调多线程用
                        enterExecutor(taskSubMq, false, abilityRequestUrlDTO);
                    } else {
                        if (couldBatchCall()) {
                            //获取批量数据调用
                            List<TaskSub> batchMessage = getBatchMessage();
                            if (CollectionUtil.isNotEmpty(batchMessage)) {
                                log.info("{},批量处理,批次数：{}", abilityCode, batchMessage.size());
                                //批量处理
                                batchRequest(batchMessage, abilityRequestUrlDTO);
                            } else {
                                //跳出循环
                                call = false;
                            }
                        } else {
                            taskSubMq = taskMessageQueue.poll();
                            if (taskSubMq != null) {
                                //调多线程用
                                enterExecutor(taskSubMq, false, abilityRequestUrlDTO);
                            } else {
                                //跳出循环
                                call = false;
                            }
                        }
                    }
                } catch (Exception e) {
                    call = false;
                }
            } else {
                call = false;
            }
        }
    }


    /**
     * 构造批次调用数据结构
     *
     * @param taskSubs
     * @param abilityRequestInfo
     */
    private void batchRequest(List<TaskSub> taskSubs, AbilityRequestUrlDTO abilityRequestInfo) {

        try {
            executorService.execute(RunnableWrapper.of(() -> {
                List<TaskSub> expireTaskSubs = new ArrayList<>();
                List<TaskSub> shouldRequestTaskSubs = new ArrayList<>();
                for (TaskSub taskSub : taskSubs) {
                    //任务是否超时跳过
                    String lineTime = taskSub.getLineTime();
                    Integer waiteTime = taskSub.getWaiteTime();
                    if (!StrUtil.isNotBlank(lineTime) && waiteTime != null && waiteTime > 0) {
                        //当前时间大于lineTime+waiteTime
                        if (System.currentTimeMillis() > NumberUtil.add(lineTime, waiteTime.toString()).longValue()) {
                            expireTaskSubs.add(taskSub);
                        } else {
                            shouldRequestTaskSubs.add(taskSub);
                        }
                    } else {
                        shouldRequestTaskSubs.add(taskSub);
                    }
                }
                //跳过
                if (CollectionUtil.isNotEmpty(expireTaskSubs)) {
//                    log.info("expireTaskSubs:{}", JSON.toJSONString(expireTaskSubs));
                    if (expireTaskSubs.size() == taskSubs.size()) {
                        //释放资源
                        abilityHttpConcurrentLimit.release(abilityRequestInfo);
                    }
                    try {
                        for (TaskSub t : expireTaskSubs) {
                            abilityClient.getClient(t.getCallbackType()).fastFail(t);
                        }
                        abilityHttpConcurrentLimit.addFastFailException(expireTaskSubs.size());
                    } catch (Exception e) {
                        log.error("fastFail异常,expireTaskSubs:{}", expireTaskSubs, e);
                    }
                }
                //发送请求
                if (CollectionUtil.isNotEmpty(shouldRequestTaskSubs)) {
                    TaskSub taskSub;
                    if (shouldRequestTaskSubs.size() == 1) {
                        taskSub = shouldRequestTaskSubs.get(0);
                        log.info("{},taskSub={},的任务进入线程池成功,localSize:{},mqSize:{}", taskSub.getAppSourceId(), taskSub.getTaskId(), taskMessageQueue.size(), this.realLineCount);
                        abilityClient.getClient(taskSub.getCallbackType()).requestAbility(taskSub, abilityHttpConcurrentLimit, abilityRequestInfo);
                    } else {
                        try {
                            taskSub = buildBatchTaskSub(shouldRequestTaskSubs);
                            log.info("{},taskSub={},的任务进入线程池成功,localSize:{},mqSize:{}", taskSub.getAppSourceId(), taskSub.getTaskId(), taskMessageQueue.size(), this.realLineCount);
                            abilityClient.getClient(taskSub.getCallbackType()).requestAbility(taskSub, abilityHttpConcurrentLimit, abilityRequestInfo);
                        } catch (Exception e) {
                            log.error("批量请求异常", e);
                            //释放资源
                            abilityHttpConcurrentLimit.release(abilityRequestInfo);
                        }
                    }
                }
            }));
        } catch (RejectedExecutionException e) {
            // 线程池已满，拒绝处理
            log.warn("线程池已满,scheduleKey={},的任务被拒绝,开始休眠", scheduleKey);
            ThreadUtil.safeSleep(abilityCallConfig.getSpinTime());
            log.warn("scheduleKey={},的任务线程休眠完成,尝试继续进入线程池", scheduleKey);
            batchRequest(taskSubs, abilityRequestInfo);
        } catch (Exception e) {
            log.error("批处理异常", e);
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
        }

    }


    /**
     * 获取批量数据
     *
     * @return
     */
    private List<TaskSub> getBatchMessage() {
        List<TaskSub> messages = new ArrayList<>();
        TaskSub firstTaskSub = null;
        for (int i = 0; i < abilityCallConfig.getBatchSize(this.abilityEnum); i++) {
            Message poll = taskMessageQueue.poll();
            if (poll != null) {
                TaskSub taskSub = JSON.parseObject(new String(poll.getBody()), TaskSub.class);
                if (firstTaskSub == null) {
                    firstTaskSub = taskSub;
                    messages.add(taskSub);
                } else {
                    if (!taskSub.getCallbackType().equals(firstTaskSub.getCallbackType()) || StrUtil.isBlank(taskSub.getRequestJson())) {
                        //不符合合并原则；推出
                        taskMessageQueue.addFirst(poll);
                        break;
                    } else {
                        messages.add(taskSub);
                    }
                }
            }
        }
        return messages;
    }


    /**
     * 是否可以批量调用
     *
     * @return
     */
    private boolean couldBatchCall() {
        return abilityCallConfig.getOpenBatch(this.abilityEnum);
    }


    /**
     * 执行能力调用
     */
    private void handleAbility() {
        while (!this.stopped) {
            try {
                if (BooleanUtil.isTrue(skipTask)) {
                    //跳过后续任务；直接回调业务
                    Message taskSubMq = apiMessageQueue.poll();
                    if (taskSubMq == null) {
                        taskSubMq = taskMessageQueue.poll();
                    }
                    if (taskSubMq != null) {
                        //快速失败，不处理后续业务
                        enterExecutor(taskSubMq, true, null);
                    } else {
                        waitTask();
                    }
                } else {
                    //尝试调用
                    abilityCall();
                    waitTask();
                }
            } catch (Exception e) {
                if (!this.stopped) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        //
    }

    /**
     * 独立请求线程池
     *
     * @param messageExt
     */
    private void enterExecutor(Message messageExt, Boolean isSkipParam, AbilityRequestUrlDTO abilityRequestInfo) {
        try {
            executorService.execute(RunnableWrapper.of(() -> {
                TaskSub taskSub = handleMessage(messageExt);
//                log.debug("处理数据：{}", JSON.toJSONString(taskSub));
                log.info("{},taskSub={},的任务进入线程池成功,localSize:{},mqSize:{}", taskSub.getAppSourceId(), taskSub.getTaskId(), taskMessageQueue.size(), this.realLineCount);
                if (StrUtil.isNotBlank(taskSub.getExtend())) {
                    JSONObject jsonObject = JSON.parseObject(taskSub.getExtend());
                    String extra = jsonObject.getString("deviceId") + "," + jsonObject.getString("bizTaskId") + "," + jsonObject.getString("abilityCode");
                    int i = RandomUtil.randomInt(1, 10);
                    TraceLogUtil.trace(ItemCodeEnum.SF_9BXKNOYQAO7.getCode(), MetricPattern.CUSTOM, "", i, "0", jsonObject.getLong("frameBizTime"), extra);
                }
                //任务是否超时跳过
                String lineTime = taskSub.getLineTime();
                Integer waiteTime = taskSub.getWaiteTime();
                boolean taskSkip = false;
                if (!isSkipParam && StrUtil.isNotBlank(lineTime) && waiteTime != null && waiteTime > 0) {
                    //当前时间大于lineTime+waiteTime
                    if (System.currentTimeMillis() > NumberUtil.add(lineTime, waiteTime.toString()).longValue()) {
                        taskSkip = true;
                    }
                }
                //跳过
                if (BooleanUtil.isTrue(isSkipParam) || taskSkip) {
                    if (taskSkip) {
                        abilityHttpConcurrentLimit.release(abilityRequestInfo);
                    }
                    abilityHttpConcurrentLimit.addFastFailException(1);
                    abilityClient.getClient(taskSub.getCallbackType()).fastFail(taskSub);
                } else {
                    //占用资源
                    abilityClient.getClient(taskSub.getCallbackType()).requestAbility(taskSub, abilityHttpConcurrentLimit, abilityRequestInfo);
                }
            }));
        } catch (RejectedExecutionException e) {
            // 线程池已满，拒绝处理
            log.warn("线程池已满,scheduleKey={},的任务被拒绝,开始休眠", scheduleKey);
            ThreadUtil.safeSleep(abilityCallConfig.getSpinTime());
            log.warn("scheduleKey={},的任务线程休眠完成,尝试继续进入线程池", scheduleKey);
            enterExecutor(messageExt, isSkipParam, abilityRequestInfo);
        } catch (Exception e) {
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
        }
    }


    /**
     * 构建算法pull模式消费者
     *
     * @param scheduleKey
     * @return
     */
    private void initPullConsumer(String scheduleKey) {
        DefaultMQPullConsumer consumer = new DefaultMQPullConsumer(scheduleKey);
        consumer.setNamesrvAddr(nameServer);
        this.abilityConsumer = consumer;
        try {
            consumer.start();
            Set<MessageQueue> messageQueueList = abilityConsumer.fetchSubscribeMessageQueues(apiTopic);
            messageQueueList = messageQueueList.stream().filter(i -> String.valueOf(i.getQueueId()).equals(queueId)).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(messageQueueList)) {
                this.apiMessageQueueList = messageQueueList;
            }

            Set<MessageQueue> messageQueueListTask = abilityConsumer.fetchSubscribeMessageQueues(taskTopic);
            messageQueueListTask = messageQueueListTask.stream().filter(i -> String.valueOf(i.getQueueId()).equals(queueId)).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(messageQueueListTask)) {
                this.taskMessageQueueList = messageQueueListTask;
            }
            log.info("[处理队列任务消费者]--{}加载完成!apiMessageQueueList:{},taskMessageQueueList:{}", scheduleKey, JSON.toJSONString(apiMessageQueueList), JSON.toJSONString(taskMessageQueueList));
        } catch (MQClientException e) {
            log.error("[处理队列任务消费者]--{},加载异常!e={}", scheduleKey, e.toString());
            throw new AbilityScheduledException("[处理队列任务消费者]--{" + scheduleKey + "},加载异常!");
        }


    }

}
