alter table t_task_result_count
    add prompt_tokens_num int default 0 null comment 'omChat prompt token数';
alter table t_task_result_count
    add output_tokens_num int default 0 null comment 'omChat输出token数';
alter table t_task_result_count
    add ability_enum int default 99 null comment '能力类型：比对算法1；视频流算法：2；v3算法：3；v35 4;其他算法：99';

CREATE TABLE `t_task_hub_count`
(
    `id`    bigint(20) NOT NULL AUTO_INCREMENT,
    ymd     date       null comment '事件事件 年月日',
    content text       null comment '统计信息',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

alter table lh_task_server_router
    add gpu_type varchar(64) null comment '选择的卡的类型';