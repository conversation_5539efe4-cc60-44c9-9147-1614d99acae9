<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.data.service.mapper.VideoTaskMatrixMapper">

    <select id="getTaskAbilityInterval" resultType="com.vos.kernel.data.api.model.vo.TaskAbilityIntervalVo">
        SELECT
        b.interval_ability,
        a.`name`,
        a.ability
        FROM
        `t_task_type` a
        LEFT JOIN tb_video_task_matrix b ON a.id = b.ai_type
        AND b.task_code = #{taskCode}
<!--        <if test="muAiTypeId != null and muAiTypeId !=''">-->
<!--            AND mu_ai_type = #{muAiTypeId}-->
<!--        </if>-->
        WHERE
        a.task_type_code = #{taskTypeCode}
    </select>

    <delete id="deleteByVideoIdAndTaskId">
        delete
        from tb_video_task_matrix
        where video_code = #{videoCode}
          and task_code = #{taskCode}
    </delete>
</mapper>
