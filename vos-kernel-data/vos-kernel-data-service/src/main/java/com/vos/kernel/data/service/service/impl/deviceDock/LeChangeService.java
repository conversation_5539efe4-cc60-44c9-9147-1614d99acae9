package com.vos.kernel.data.service.service.impl.deviceDock;

import com.alibaba.fastjson.JSONObject;
import com.vos.kernel.core.api.rpc.TUserAuthenticationRpcService;
import com.vos.kernel.data.api.model.entity.DataInfoVideo;
import com.vos.kernel.data.api.model.enumPackage.CompanyEnum;
import com.vos.kernel.data.api.model.enumPackage.VideoClassifyEnum;
import com.vos.kernel.data.api.model.enumPackage.YinShiDeviceStatusEnum;
import com.vos.kernel.data.api.model.req.SaveLeChange;
import com.vos.kernel.data.api.model.vo.LeChangeVo;
import com.vos.kernel.data.service.mapper.DataInfoVideoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class LeChangeService {

    @DubboReference
    TUserAuthenticationRpcService tUserAuthenticationRpcService;
    @Resource
    DataInfoVideoMapper dataInfoVideoMapper;
    @Value("${leChange.url}")
    private String leChangeUrl;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 拍照
     */
    public JSONObject tokePhoto(LeChangeVo leChangeVo) {
        String url = leChangeUrl + "/api/takePhoto";
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        String params = JSONObject.toJSONString(leChangeVo);
        HttpEntity<String> formEntity = new HttpEntity<String>(params, headers);
        log.info("request  请求接口===>{}  {}  {}", url, params, headers);
        String body = restTemplate.postForEntity(url, formEntity, String.class).getBody();
        log.info("response 请求接口===>{}  {}  {} {}", url, params, headers, body);
        return JSONObject.parseObject(body);
    }

    @Transactional(rollbackFor = Exception.class)
    public DataInfoVideo saveLeChange(String outOss, SaveLeChange saveLeChange) {
        // 是否有默认分组
        Date now = new Date();
        String orgCode = tUserAuthenticationRpcService.getAppkey();
//        Boolean aBoolean = videoDockingService.canSaveVideo(orgCode, 1);
//        if (!aBoolean) {
//            throw new BusinessException("设备接入已达上限，如有疑问请联系超级管理员");
//        }
        DataInfoVideo video = new DataInfoVideo();
        video.setPhoto(outOss);
        video.setIsDel(0);
        video.setSetName(saveLeChange.getSetName());
        video.setClassifyId(saveLeChange.getClassifyId());
        video.setDeviceSerial(saveLeChange.getDeviceSerial());
        video.setChannel(saveLeChange.getChannel());
        video.setAk(saveLeChange.getAppId());
        video.setSk(saveLeChange.getAppSecret());
//        video.setUserId(cas.getUserId());
        video.setUpdateTime(now);
        video.setCreateTime(now);
        video.setType(VideoClassifyEnum.DATASOURCE.getValue());
        video.setOrgId(orgCode);
//        video.setUserName(cas.getUserName());
        video.setFirmType(CompanyEnum.LE_CHANGE.getCode());
        video.setCompany(CompanyEnum.LE_CHANGE.getCompany());
        video.setDeviceStatus(YinShiDeviceStatusEnum.ONLINE.getCode());
        // 借用海康isc字段
        video.setHikIscAk(saveLeChange.getAppId());
        video.setIsDefault(false);
        dataInfoVideoMapper.insert(video);
        log.info("插入乐橙云目录设备{}", video);
        return video;
    }
}
