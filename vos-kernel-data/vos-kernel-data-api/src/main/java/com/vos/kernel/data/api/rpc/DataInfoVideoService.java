package com.vos.kernel.data.api.rpc;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.linker.basic.baseclass.BaseResp;
import com.vos.kernel.data.api.model.dto.DataInfoVideoUpdateDTO;
import com.vos.kernel.data.api.model.dto.DeviceListDTO;
import com.vos.kernel.data.api.model.entity.DataInfoVideo;
import com.vos.kernel.data.api.model.entity.TaskBindVideo;
import com.vos.kernel.data.api.model.entity.VideoLocalDetail;
import com.vos.kernel.data.api.model.enumPackage.CompanyEnum;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 视频流数据集 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
public interface DataInfoVideoService  {

    void addTaskNum(String videoIds);

    @Transactional(rollbackFor = Exception.class)
    DataInfoVideo saveData(DataInfoVideo dataInfoVideo);

    List<DataInfoVideo> videoList(DeviceListDTO deviceListDTO);

    PageInfo<DataInfoVideo> deviceList(DeviceListDTO deviceListParam);

    ArrayList<TaskBindVideo> getVideoTaskTypeNum(Set<Long> videoIds);

    @Transactional(rollbackFor = Exception.class)
    String deleteData(String videoCode);

    void saveFrameCallBack(List<VideoLocalDetail> list);

    DataInfoVideo updateDate(DataInfoVideoUpdateDTO dataInfoVideoUpdateDTO);

    Boolean saveOrUpdateBatch(List<DataInfoVideo> dataInfoVideoList);

    void deleteListByUid(List<String> userIdList);

    Boolean updateList(List<DataInfoVideo> list);

    String changePhoto(String videoCode);

    /**
     * 按照租户获取设备状态与对应回流信息
     * @param pageNum
     * @param pageSize
     * @param deviceStatus
     * @param deviceName
     * @param backStatus
     * @param orgId
     * @param init
     * @return
     */
    IPage<DataInfoVideo> getVideoBackInfoByOrgId(Integer pageNum, Integer pageSize, Integer deviceStatus, String deviceName, Integer backStatus, String orgId, Boolean init);

    /**
     * 清除用户数据
     *
     * @param authenticationId
     */
    void clearByUser(String authenticationId);


    @Async
    void syncAddYinShiDevice(String eachDevice, String snowId, LocalDateTime now, String orgCode, Long defaultClassifyId);

    void saveVideoList(String snowId, String authCode, String deviceSerials);

    void deleteVideoList(String deviceSerials, String authCode);

    DataInfoVideo getBySerialAndComPany(String deviceSerial, Integer inventedDevice);
}
