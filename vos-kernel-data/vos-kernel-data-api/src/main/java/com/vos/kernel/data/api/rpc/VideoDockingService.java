package com.vos.kernel.data.api.rpc;

import com.alibaba.fastjson.JSONObject;
import com.linker.basic.baseclass.BaseResp;
import com.vos.kernel.data.api.model.constants.Response;
import com.vos.kernel.data.api.model.dto.DataInfoVideoBatchAddDTO;
import com.vos.kernel.data.api.model.entity.DataInfoVideo;
import com.vos.kernel.data.api.model.enumPackage.CompanyEnum;
import com.vos.kernel.data.api.model.req.*;
import com.vos.kernel.data.api.model.vo.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface VideoDockingService {
    // 根据不同厂商去抓取视频
    @Async
    @Transactional(propagation = Propagation.SUPPORTS)
    Object cutVideoAll(String taskCode, List<TbVideoTaskResultVo> resultList);

    DataInfoVideo saveHikVision(SaveHikVisionReq hikVisionReq, Boolean isCheck);

    List<DataInfoVideo>  saveHuaYun(SaveHuaYun saveHuaYun);

    DataInfoVideo saveDaHua(SaveHikVisionReq hikVisionReq, Boolean isCheck);

    DataInfoVideo saveStream(StreamUrlReq req, Boolean isCheck);

    DataInfoVideo saveDisk(FtpConfigVo ftpConfigVo, Boolean isCheck);

    @Transactional(rollbackFor = Exception.class)
    DataInfoVideo saveYiShi(SaveYiShi saveYiShi);

    void unzip(String zipPath, String unzipPath);

    @Transactional(rollbackFor = Exception.class)
    List<DataInfoVideo> saveLocalVideoByFileBatch(SaveBatchLocalVideoReq req);

    void sendTask2CutService(String videoCode);

    @Transactional(rollbackFor = Exception.class)
    DataInfoVideo saveQiNiu(SaveQiNiu saveQiNiu, Boolean isCheck);

    JSONObject deviceAdd(SaveYinShi saveYinShi);

    List<DataInfoVideo> addDevices(DeviceVo deviceVo);

    Response<String> saveLeChange(Map<String, Object> map);

    List<DataInfoVideo> saveHivIsc(Map<String, Object> map);

    Boolean canSaveVideo(String orgCode, Integer saveNum);

    DataInfoVideo saveHikVisionAndDaHuaVideo(String outOss, SaveHikVisionReq hikVisionReq, CompanyEnum hikVision);

    String getYinshiUrl(String orgCode);

    String getYingshiUrl(String videoCode);

    List<DataInfoVideo> addDaxunList(SaveDaxun saveDaxun);

    DataInfoVideo addDaxun(SaveDaxun saveDaxun);

    void deviceCallback(String opt, String deviceSerials, String deviceTrustId, String state);

    void authCodeCallback(String auth_code, String state);

    DataInfoVideo saveHivIscCode(String outOss, String orgCode, HivIscExportVo hivIscExportVo);

    List<DataInfoVideo> addGBList(SaveGB saveGB);

    DataInfoVideo addGB(SaveGB saveGB);

    InventedVo fileTaskUpload(SaveInvented saveInvented,String imageUrl);

    Object huaYunCheckConfig(HuaLoginVo huaLoginVo);

    InventedVo fileTaskUploadNoStore(SaveInvented saveInvented);

    List<DataInfoVideo> addYingShiPlatFormList(SaveYinShi saveYinShi);

    List<DataInfoVideo> addYingShiYunList(SaveYinShi saveYinShi);

    DataInfoVideo saveYingShiPlatForm(SaveYinShi saveYinShi);

    DataInfoVideo saveYingShiYun(SaveYinShi saveYinShi);
}
