package com.vos.kernel.data.api.rpc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.vos.kernel.data.api.model.dto.TaskConfigDTO;
import com.vos.kernel.data.api.model.dto.TaskCountHourDTO;
import com.vos.kernel.data.api.model.entity.*;
import com.vos.kernel.data.api.model.dto.TaskListDTO;
import com.vos.kernel.data.api.model.vo.VideoTaskVo;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
public interface TaskService extends IService<Task> {

    List<Task> selectTaskAndIntervalPage(String taskCode);

    PageInfo<Task> taskList(TaskListDTO taskListDTO);

    Task detail(String taskCode);

    Long taskTotal(String taskCode);

    VideoTaskVo saveVideoTask(VideoTaskVo videoTaskVo, Boolean saveFlag);

    List<TaskSub> setSubName(List<TaskSub> subList);

    List<VideoTaskTime> timeChange(List<VideoTaskTime> taskTimeList);

    Task enableTask(String taskCode, Integer enable);

    //    @Async
    void resetRedisTask(String taskCode);

    TaskConfigDTO initTaskConfig(TaskConfigDTO taskConfigDTO);

    List<TaskSub> saveSonTaskListObj(List<TaskSub> subList, Task task, Date now, Boolean saveFlag);

    VideoTaskSetting saveTaskSettingObj(VideoTaskSetting taskSetting, Date now, Task task, Boolean saveFlag);

    List<VideoTaskTime> saveTaskTimeObj(List<VideoTaskTime> taskTimeList, Task task);

    List<VideoTaskMatrix> saveTaskMatrixObj(List<VideoTaskMatrix> matrixList, Task task);

    void initTaskTypeConfigVideo(String videoIds, List<TaskSub> subList, String taskCode, String orgCode);

    void deleteTask(String taskCode);

    void deleteInvented(String orgCode,String taskTypeCode);

    void cutImpl(String deviceCode, LocalDateTime time);

    void cutTaskPush(LocalDateTime now);

    void pushToStudioByConf();

    void checkVideoTaskTimeStart();

    void checkVideoTaskTimeEnd();

    List<TaskHourCount> countGetList(TaskCountHourDTO taskCountHourDTO);

    //棚顶脱落物算法
    void genSample(String videoIds, List<TaskSub> subList, String orgCode);

    void genMsg(List<TaskSub> subList, String orgCode);


    void updateVideoTaskSetting(VideoTaskSetting videoTaskSetting);

    /**
     * 清除用户数据
     *
     * @param authenticationId
     */
    void clearByUser(String authenticationId);

    void getCount();

    VideoTaskVo getTaskInfo(Task tTask);
}
