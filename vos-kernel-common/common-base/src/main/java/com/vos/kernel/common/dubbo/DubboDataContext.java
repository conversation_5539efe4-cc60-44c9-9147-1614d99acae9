package com.vos.kernel.common.dubbo;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;

/**
 * controller端：即服务发起段设置，后续调用端使用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/8
 * @description: com.hzlh.task.common.dubbo
 */
@Slf4j
public class DubboDataContext {

    /**
     * 当前用户 lh_task_app ： app_id
     */
    private static final InheritableThreadLocal<Long> APPID_HOLDER = new TransmittableThreadLocal<>();
    /**
     * 当前用户 t_user_authentication ： app_key
     */
    private static final InheritableThreadLocal<String> APPKEY_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 当前用户 t_user_authentication ： authentication_id
     */
    private static final InheritableThreadLocal<String> AUTHID_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 当前用户 t_user_authentication ： app_key
     */
    private static final InheritableThreadLocal<String> MESSAGE_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 当前用户 t_user_authentication ： app_key
     */
    private static final InheritableThreadLocal<String> USER_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 当前线程 用户code
     *
     * @return
     */
    public static String getUserHolder() {
        return USER_HOLDER.get();
    }

    /**
     * 当前线程 appid
     *
     * @param userCode
     */
    public static void setUserHolder(String userCode) {
        USER_HOLDER.set(userCode);
    }

    /**
     * 当前线程 appid
     *
     * @return
     */
    public static Long getAppIdHolder() {
        return APPID_HOLDER.get();
    }

    /**
     * 当前线程 appid
     *
     * @return
     */
    public static String getMessageHolder() {
        return MESSAGE_HOLDER.get();
    }

    /**
     * 当前线程 appid
     *
     * @param message
     */
    public static void setMessageHolder(String message) {
        MESSAGE_HOLDER.set(message);
    }

    /**
     * 当前线程 appid
     *
     * @param authenticationId
     */
    public static void setAppIdHolder(Long authenticationId) {
        APPID_HOLDER.set(authenticationId);
    }

    /**
     * 当前线程 appKey
     *
     * @return
     */
    public static String getAppKeyHolder() {
        return APPKEY_HOLDER.get();
    }

    /**
     * 设置当前线程 appKey
     *
     * @param appKey
     */
    public static void setAppKeyHolder(String appKey) {
        APPKEY_HOLDER.set(appKey);
    }

    /**
     * 当前线程 authId
     *
     * @return
     */
    public static String getAuthIdHolder() {
        return AUTHID_HOLDER.get();
    }

    /**
     * 设置当前线程 authId
     *
     * @param authId
     */
    public static void setAuthIdHolder(String authId) {
        AUTHID_HOLDER.set(authId);
    }

    /**
     * 清除
     */
    public static void clearOrgHolder() {
        APPID_HOLDER.remove();
        APPKEY_HOLDER.remove();
        AUTHID_HOLDER.remove();
        MESSAGE_HOLDER.remove();
        USER_HOLDER.remove();
    }

}
