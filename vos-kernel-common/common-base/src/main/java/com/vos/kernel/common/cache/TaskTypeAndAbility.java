package com.vos.kernel.common.cache;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年09月18日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class TaskTypeAndAbility implements Serializable {

    /**
     * 应用唯一键
     */
    private String taskTypeCode;

    /**
     * taskTypeId
     */
    private Integer id;

    /**
     * taskTypeId
     */
    private Integer tbAbilityId;


    /**
     * 应用名称
     */
    private String abilityName;
    /**
     * task_ability_id
     */
    private Long operatorId;

    /**
     * model
     */
    private String actionId;

    /**
     * 算法
     */
    private String ability;


    /**
     * tb_ability abilityCode
     */
    private String abilityCode;

    /**
     * 是否比对算法
     */
    private Boolean isMatchType;

    /**
     * 比对库上传地址
     */
    private String matchUploadUrl;


    private Integer abilityEnum;

    private Integer status;

    /**
     * open chat 对应的模型名称
     */
    private String chatModelName;

    /**
     * chat 上下文长度
     */
    private Integer contextLength;

    /**
     * chatApiKey
     */
    private String chatApiKey;

}
