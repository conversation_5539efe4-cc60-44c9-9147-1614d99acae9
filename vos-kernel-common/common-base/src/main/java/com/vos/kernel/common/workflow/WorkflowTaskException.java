package com.vos.kernel.common.workflow;

import com.linker.basic.baseclass.IResponse;
import com.linker.basic.exception.BaseException;

/**
 * <AUTHOR>
 * @date 2024年11月26日
 * @version: 1.0
 * @description: TODO
 */
public class WorkflowTaskException extends BaseException {
    private static final IResponse EXECS = TaskRespCodeEnum.INTERNAL_SERVER_ERROR;

    public WorkflowTaskException() {
        super(EXECS);
    }

    public WorkflowTaskException(Throwable cause) {
        super(EXECS, cause);
    }

    public WorkflowTaskException(String desc) {
        super(EXECS, desc);
    }

    public WorkflowTaskException(IResponse execs) {
        super(execs);
    }

    public WorkflowTaskException(IResponse execs, Throwable cause) {
        super(execs, cause);
    }

    public WorkflowTaskException(IResponse execs, String desc) {
        super(execs, desc);
    }

    public WorkflowTaskException(IResponse execs, String desc, Throwable cause) {
        super(execs, desc, cause);
    }
}
