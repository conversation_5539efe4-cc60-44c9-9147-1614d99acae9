package com.vos.kernel.common.cache;

import com.alibaba.fastjson2.JSONObject;
import com.vos.kernel.common.entity.TaskAndSubInfoDTO;
import com.vos.kernel.common.entity.Task;
import com.vos.kernel.common.entity.TaskSub;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月05日
 * @version: 1.0
 * @description: TODO
 */
public interface ITaskCacheRpcService {

    /**
     * 删除缓存
     *
     * @param taskId
     */
    Boolean deleteCache(String taskId);

    /**
     * 保存缓存
     *
     * @param task
     * @param taskSubList
     */
    void saveCache(Task task, List<TaskSub> taskSubList, boolean persistSwitch);

    /**
     * 获取主任务信息
     *
     * @param taskId
     * @return
     */
    Task getTaskCache(String taskId);

    /**
     * 获取子任务信息
     *
     * @param taskId
     * @param subTaskId
     * @param abilityId
     * @return
     */
    TaskSub getTaskSubCache(String taskId, String subTaskId, String abilityId);

    /**
     * 任务转存
     *
     * @param taskId
     */
    void taskResultDumpCache(String taskId);

    /**
     * 跳过更新状态
     *
     * @param taskId
     */
    void skipTaskUpdateStatus(String taskId);

    /**
     * 获取子任务与主任务信息
     *
     * @param taskId
     * @param subTaskId
     * @param abilityId
     * @return
     */
    TaskAndSubInfoDTO getTaskAndSub(String taskId, String subTaskId, String abilityId);

    /**
     * 更新子任务信息
     *
     * @param taskSub
     */
    void updateTaskSubCache(TaskSub taskSub);

    /**
     * 批量更新
     *
     * @param subList
     */
    void multiUpdateTaskSubCache(List<TaskSub> subList);

    /**
     * 更新主任务信息
     *
     * @param task
     */
    void updateTaskCache(Task task);

    /**
     * 获取所有子任务
     *
     * @param taskId
     * @return
     */

    List<TaskSub> getTaskSubCache(String taskId);

    /**
     * 根据businessIdList获取TaskSub
     *
     * @param taskId
     * @param businessIdList
     * @return
     */
    List<TaskSub> getTaskSubCacheByBusinessId(String taskId, List<String> businessIdList);

    /**
     * 根据subSnowId获取TaskSub
     *
     * @param taskId
     * @param subSnowId
     * @return
     */
    List<TaskSub> getTaskSubCacheBySubSnowId(String taskId, String subSnowId);


    /**
     * 保存redis
     *
     * @param base64Str
     * @param imageId
     * @return
     */
    Boolean base64ToCache(String base64Str, String imageId);


    /**
     * 获取缓存信息
     *
     * @param imageId
     * @return
     */
    String getBase64Cache(String imageId, Boolean clearRightNow);


    /**
     * 删除缓存信息
     *
     * @param imageId
     */
    void deleteBase64Cache(String imageId);

    void saveTempRequestData(JSONObject jsonObject, Long refluxId);

    String getTempRequestData(Long refluxId);


    /**
     * 埋点统计fps
     *
     * @param abilityId
     * @param code
     */
    void addAbilityTaskCount(Long abilityId, Integer code, Long incNum);

    /**
     * 算法任务耗时记录
     *
     * @param abilityId
     * @param taskServerRouterId
     * @param handleTime
     */
    void addAbilityTaskRt(Long abilityId, String taskServerRouterId, Long handleTime);

    /**
     * 缓存算法平均耗时
     *
     * @param abilityId
     */
    void cacheAbilityRtAvg(Long abilityId, Long rtAvg);

    /**
     * 获取近100个请求的平均处理时间
     *
     * @param abilityId
     * @return
     */
    Double getAbilityPerFps(String abilityId);

}
