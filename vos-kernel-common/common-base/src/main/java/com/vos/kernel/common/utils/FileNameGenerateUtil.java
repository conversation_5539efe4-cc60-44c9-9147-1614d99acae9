package com.vos.kernel.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 统一文件路径生成工具
 **/
public class FileNameGenerateUtil {


    //文件路径
    private static final String url = "/AUTHID/OPERATION/TIME/FILE.FORMAT";
    private static final String path = "/AUTHID/OPERATION/TIME";

    /***
     * 根据用户id，操作，时间生成文件路径, 文件尾缀
     * /用户ID/操作步骤/yyyy/mm/dd/唯一ID.xxx
     * **/
    public static String fireNameGenerate(String authenticationId, String operation, String format) {
        if (StringUtils.isEmpty(authenticationId) || StringUtils.isEmpty(operation)) {
            return null;
        }
        String time = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        Long id = SnowFlakeUtil.getId();

        return url.replace("AUTHID", authenticationId)
                .replace("OPERATION", operation)
                .replace("TIME", time)
                .replace("FILE", id.toString())
                .replace("FORMAT", format);
    }

    /***
     * 根据用户id，操作，时间生成文件路径, 文件尾缀
     * /用户ID/操作步骤/yyyy/mm/dd/唯一ID.xxx
     * **/
    public static String fireNameGenerateNew(String authenticationId, String operation) {
        if (StringUtils.isEmpty(authenticationId) || StringUtils.isEmpty(operation)) {
            return null;
        }
        String time = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        return path.replace("AUTHID", authenticationId)
                .replace("OPERATION", operation)
                .replace("TIME", time) + "/";
    }

    public static String firePathGenerate(String authenticationId, String operation) {
        if (StringUtils.isEmpty(authenticationId) || StringUtils.isEmpty(operation)) {
            return null;
        }
        String time = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        Long id = SnowFlakeUtil.getId();

        return path.replace("AUTHID", authenticationId)
                .replace("OPERATION", operation)
                .replace("TIME", time);
    }

    public static String getTimePath(Date date) {
        String time = new SimpleDateFormat("yyyy/MM/dd").format(date);
        return time;
    }

    /**
     * 中文转Unicode
     * 其他英文字母或特殊符号也可进行Unicode编码
     *
     * @param cn
     * @return
     */
    public static Boolean isCn(String cn) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(cn);
        if (m.find()) {
            return true;
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println("http://172.16.36.1:30000/webdav/".contains("http://172.16.36.1:30000/webdav/"));
        System.out.println(
                fireNameGenerate("aa", "cut", "jpg")
        );
    }
}
