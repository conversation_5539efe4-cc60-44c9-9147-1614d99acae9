package com.vos.kernel.common.workflow;

import com.linker.omos.client.config.WorkflowTaskExecLog;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class WorkflowCallBackContentItem implements Serializable {

    /**
     * 任务ID
     */
    private String sourceId;

    /**
     * 任务执行输出
     */
    private Object outputData;

    /**
     * 状态
     */
    private WorkflowStatusEnum status;

    /**
     * 失败原因
     */
    private String reasonForIncompletion;

    /**
     * 执行者
     */
    private String workerId;

    /**
     * 任务开始时间 毫秒的时间戳
     */
    private Long startTime;

    /**
     * task 日志
     */
    private List<WorkflowTaskExecLog> logs;
}
