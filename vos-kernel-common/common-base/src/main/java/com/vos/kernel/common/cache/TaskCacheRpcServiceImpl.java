package com.vos.kernel.common.cache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter;
import com.vos.kernel.common.constant.CacheConstants;
import com.vos.kernel.common.entity.Task;
import com.vos.kernel.common.entity.TaskAndSubInfoDTO;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.kernel.common.enums.AppTaskCountEnum;
import com.vos.kernel.common.enums.SubTaskStatusEnum;
import com.vos.kernel.common.redis.contant.RedisConstant;
import com.vos.kernel.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年08月05日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class TaskCacheRpcServiceImpl implements ITaskCacheRpcService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Value("${task.expired:3}")
    private Integer expired;

    private static final String ABILITY_CALL_COST_ALL = "ability_call_cost_all:";

    /**
     * cache key
     */
    public static final String REDIS_CACHE_KEY = "task:invented:img_";

    public static final String DATA_REFLUX_KEY = "data:reflux:request_";

    @Override
    public Boolean deleteCache(String taskId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        return redisTemplate.delete(taskKey);
    }

    /**
     * 非持久化下精简缓存数据
     *
     * @param excludeFilter
     * @param persistSwitch
     */
    private void shortCacheBody(SimplePropertyPreFilter excludeFilter, boolean persistSwitch) {
        if (!BooleanUtil.isTrue(persistSwitch)) {
            excludeFilter.getExcludes().add("appId");
            excludeFilter.getExcludes().add("appSourceId");
            excludeFilter.getExcludes().add("taskNum");
            excludeFilter.getExcludes().add("taskName");
            excludeFilter.getExcludes().add("callbackType");
            excludeFilter.getExcludes().add("callbackInfo");
            excludeFilter.getExcludes().add("callbackResult");
            excludeFilter.getExcludes().add("callbackTime");
        }
    }


    @Override
    public void saveCache(Task task, List<TaskSub> taskSubList, boolean persistSwitch) {
        Long taskId = task.getTaskId();
        // 将子任务和主任务记录缓存
        HashOperations<String, Object, Object> operations = redisTemplate.opsForHash();
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        String taskFormat = String.format(RedisConstant.HASH_TASK_KEY, taskId);
        HashMap<String, String> taskHashMap = new HashMap<>();
        // Fastjson 动态排除字段
        SimplePropertyPreFilter excludeFilter = new SimplePropertyPreFilter();
        shortCacheBody(excludeFilter, persistSwitch);
        taskHashMap.put(taskFormat, JSON.toJSONString(task, excludeFilter));
        for (TaskSub taskSub : taskSubList) {
            taskHashMap.put(String.format(RedisConstant.HASH_SUB_TASK_KEY, taskSub.getSubTaskId(), taskSub.getAbilityId()), JSON.toJSONString(taskSub));
        }
        operations.putAll(taskKey, taskHashMap);
        // 缓存3小时 根据任务下发时间判断是否过期，是否跳过，保证调度请求返回
        redisTemplate.expire(taskKey, 3, TimeUnit.HOURS);
    }

    @Override
    public Task getTaskCache(String taskId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        Object o = redisTemplate.opsForHash().get(taskKey, String.format(RedisConstant.HASH_TASK_KEY, taskId));
        if (null == o || o == "") {
            return null;
        }
        return JSON.parseObject(o.toString(), Task.class);
    }

    @Override
    public TaskSub getTaskSubCache(String taskId, String subTaskId, String abilityId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        Object o = redisTemplate.opsForHash().get(taskKey, String.format(RedisConstant.HASH_SUB_TASK_KEY, subTaskId, abilityId));
        if (null == o || o == "") {
            return null;
        }
        return JSON.parseObject(o.toString(), TaskSub.class);
    }

    @Override
    public void taskResultDumpCache(String taskId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(taskKey);
        JSONObject taskJo = new JSONObject();
        JSONArray subTaskJo = new JSONArray();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String key = String.valueOf(entry.getKey());
            Object value = entry.getValue();
            if (String.format(RedisConstant.HASH_TASK_KEY, taskId).equals(key)) {
                taskJo.put("task", value);
            } else {
                subTaskJo.add(value);
            }
        }
        taskJo.put("taskSub", subTaskJo);
        redisTemplate.opsForList().leftPush(RedisConstant.DEL_FINISH_TASK, JSON.toJSONString(taskJo));
    }

    @Override
    public void skipTaskUpdateStatus(String taskId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(taskKey);
        HashMap<String, String> stringStringHashMap = new HashMap<>(16);
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String key = String.valueOf(entry.getKey());
            Object value = entry.getValue();
            if (String.format(RedisConstant.HASH_TASK_KEY, taskId).equals(key)) {
                stringStringHashMap.put(key, value.toString());
            } else {
                TaskSub taskSub = JSON.parseObject(value.toString(), TaskSub.class);
                taskSub.setHandleStatus(SubTaskStatusEnum.JUMP_SERIAL.getKey());
                stringStringHashMap.put(key, JSON.toJSONString(taskSub));
            }
        }
        redisTemplate.opsForHash().putAll(taskKey, stringStringHashMap);
    }

    @Override
    public TaskAndSubInfoDTO getTaskAndSub(String taskId, String subTaskId, String abilityId) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        String task = String.format(RedisConstant.HASH_TASK_KEY, taskId);
        String sub = String.format(RedisConstant.HASH_SUB_TASK_KEY, subTaskId, abilityId);
        List<Object> objects = redisTemplate.opsForHash().multiGet(taskKey, Arrays.asList(task, sub));
        if (CollectionUtil.isNotEmpty(objects) && objects.size() == 2 && ObjectUtil.isNotEmpty(CollectionUtil.getFirst(objects)) && ObjectUtil.isNotEmpty(CollectionUtil.getLast(objects))) {
            TaskAndSubInfoDTO taskAndSubInfoDTO = new TaskAndSubInfoDTO();
            taskAndSubInfoDTO.setTask(JSON.parseObject(CollectionUtil.getFirst(objects).toString(), Task.class));
            taskAndSubInfoDTO.setTaskSub(JSON.parseObject(CollectionUtil.getLast(objects).toString(), TaskSub.class));
            return taskAndSubInfoDTO;
        }
        return null;
    }

    @Override
    public void updateTaskSubCache(TaskSub taskSub) {
        if (BooleanUtil.isTrue(taskSub.getHandleDirectByMq()) || BooleanUtil.isTrue(taskSub.getBatchRequest())) {
            return;
        }
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskSub.getTaskId());
        redisTemplate.opsForHash().put(taskKey, String.format(RedisConstant.HASH_SUB_TASK_KEY, taskSub.getSubTaskId(), taskSub.getAbilityId()), JSON.toJSONString(taskSub));
    }


    @Override
    public void multiUpdateTaskSubCache(List<TaskSub> subList) {
        if (CollectionUtil.isNotEmpty(subList)) {
            for (TaskSub t : subList) {
                updateTaskSubCache(t);
            }
        }
    }

    @Override
    public void updateTaskCache(Task task) {
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", task.getTaskId());
        redisTemplate.opsForHash().put(taskKey, String.format(RedisConstant.HASH_TASK_KEY, task.getTaskId()), JSON.toJSONString(task));

    }

    @Override
    public List<TaskSub> getTaskSubCacheByBusinessId(String taskId, List<String> businessIdList) {
        ArrayList<TaskSub> taskSubs = new ArrayList<>();
        if (CollectionUtil.isEmpty(businessIdList)) {
            return taskSubs;
        }
        List<TaskSub> taskSubCache = getTaskSubCache(taskId);
        return taskSubCache.stream().filter(t -> businessIdList.contains(t.getBusinessId())).sorted(Comparator.comparing(TaskSub::getSubTaskId)).collect(Collectors.toList());
    }


    /**
     * 获取所有的子任务
     *
     * @param taskId
     * @return
     */
    @Override
    public List<TaskSub> getTaskSubCache(String taskId) {
        List<TaskSub> taskSubs = new ArrayList<>();
        String taskKey = String.format(RedisConstant.ASSIGN_TASK_KEY, "", "", taskId);
        for (Map.Entry<Object, Object> entry : redisTemplate.opsForHash().entries(taskKey).entrySet()) {
            String key = String.valueOf(entry.getKey());
            Object value = entry.getValue();
            if (String.format(RedisConstant.HASH_TASK_KEY, taskId).equals(key)) {
                continue;
            }
            TaskSub taskSub = JSON.parseObject(value.toString(), TaskSub.class);
            taskSubs.add(taskSub);
        }
        return taskSubs.stream().sorted(Comparator.comparing(TaskSub::getSubTaskId)).collect(Collectors.toList());
    }

    @Override
    public List<TaskSub> getTaskSubCacheBySubSnowId(String taskId, String subSnowId) {
        if (StrUtil.isBlank(subSnowId)) {
            return new ArrayList<>();
        }
        List<TaskSub> taskSubCache = getTaskSubCache(taskId);
        return taskSubCache.stream().filter(t -> subSnowId.equals(t.getSubSnowId())).sorted(Comparator.comparing(TaskSub::getSubTaskId)).collect(Collectors.toList());
    }

    @Override
    public Boolean base64ToCache(String base64Str, String imageId) {
        stringRedisTemplate.opsForValue().set(REDIS_CACHE_KEY + imageId, base64Str, expired, TimeUnit.MINUTES);
        return true;
    }

    /**
     * 获取并删除
     *
     * @param key
     * @return
     */
    private String getAndDelete(String key) {
        RedisScript<String> script = new DefaultRedisScript<>(
                "local value = redis.call('GET', KEYS[1])\n" +
                        "redis.call('DEL', KEYS[1])\n" +
                        "return value",
                String.class);

        return stringRedisTemplate.execute(script, Collections.singletonList(key));
    }

    @Override
    public String getBase64Cache(String imageId, Boolean clearRightNow) {
        String base64 = stringRedisTemplate.opsForValue().get(REDIS_CACHE_KEY + imageId);
        if (base64 == null || base64.length() < 100) {
            log.info("================imageId:{},当前base64有问题=============:{}", imageId, base64);
        }
        if (BooleanUtil.isTrue(clearRightNow)) {
            stringRedisTemplate.delete(REDIS_CACHE_KEY + imageId);
        }
        return base64;
    }

    @Override
    public void deleteBase64Cache(String imageId) {
        if (StrUtil.isBlank(imageId)) {
            return;
        }
        Boolean delete = stringRedisTemplate.delete(REDIS_CACHE_KEY + imageId);

    }

    @Override
    public void saveTempRequestData(JSONObject jsonObject, Long refluxId) {
        stringRedisTemplate.opsForValue().set(DATA_REFLUX_KEY + refluxId, jsonObject.toJSONString(), expired, TimeUnit.MINUTES);
    }

    @Override
    public String getTempRequestData(Long refluxId) {
        String requestData = getAndDelete(DATA_REFLUX_KEY + refluxId);
        if (StrUtil.isBlank(requestData)) {
            return "";
        }
        return requestData;
    }

    @Override
    @Async("metricThreadPool")
    public void addAbilityTaskCount(Long abilityId, Integer code, Long incNum) {
        LocalDateTime now = LocalDateTime.now();
        String keyCount = "MRC:" + now.format(DateTimeFormatter.BASIC_ISO_DATE) + ":A" + abilityId;
        String key = AppTaskCountEnum.getAppTaskCountEnum(code).getKey();
        if (AppTaskCountEnum.CHAT_OUT_COUNT.getCode().equals(code) || AppTaskCountEnum.CHAT_PROMPT_COUNT.getCode().equals(code)) {
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                // 存储当天总调用次数
                connection.hashCommands().hIncrBy(keyCount.getBytes(), key.getBytes(), incNum);
                return null;
            });
        } else {
            byte[] nowMinuteBytes = LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm").getBytes();
            String nowHour = LocalDateTimeUtil.format(now, "yyyy-MM-dd HH");
            String callHourMap = key + ":" + nowHour + ":" + abilityId;
            byte[] callHourMapBytes = callHourMap.getBytes();
            LocalDateTime expireHour = LocalDateTimeUtil.parse(nowHour, "yyyy-MM-dd HH").plusHours(2);
            long expireTimestamp = expireHour.toEpochSecond(ZoneOffset.ofHours(8));
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                // 存储当前小时的调用次数
                connection.hashCommands().hIncrBy(callHourMapBytes, nowMinuteBytes, incNum);
                // 存储当天总调用次数
                connection.hashCommands().hIncrBy(keyCount.getBytes(), key.getBytes(), incNum);
                connection.expireAt(callHourMapBytes, expireTimestamp);
                return null;
            });
        }

    }

    @Override
    public void addAbilityTaskRt(Long abilityId, String taskServerRouterId, Long handleTime) {
        if (abilityId != null && handleTime != null && handleTime > 0) {
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
                stringRedisConn.lPush(ABILITY_CALL_COST_ALL + abilityId, String.valueOf(handleTime));
                stringRedisConn.lTrim(ABILITY_CALL_COST_ALL + abilityId, 0, 100);
                return null;
            });
        }
    }

    @Override
    public void cacheAbilityRtAvg(Long abilityId, Long rtAvg) {
//        stringRedisTemplate.opsForHash().put(CacheConstants.ABILITY_CALL_COST_AVG, abilityId.toString(), rtAvg.toString());
        LocalDateTime now = LocalDateTime.now();
        String nowHour = LocalDateTimeUtil.format(now, "yyyy-MM-dd HH");
        byte[] nowMinuteBytes = LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm").getBytes();
        String callHourMap = "rt:" + nowHour + ":" + abilityId;
        byte[] callHourMapBytes = callHourMap.getBytes();
        LocalDateTime expireHour = LocalDateTimeUtil.parse(nowHour, "yyyy-MM-dd HH").plusHours(2);
        long expireTimestamp = expireHour.toEpochSecond(ZoneOffset.ofHours(8));
        byte[] bytes = rtAvg.toString().getBytes();

        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            //设置总平均值
            connection.hashCommands().hSet(CacheConstants.ABILITY_CALL_COST_AVG.getBytes(), abilityId.toString().getBytes(), bytes);
            // 存储当前小时的每分钟rt
            connection.hashCommands().hSet(callHourMapBytes, nowMinuteBytes, bytes);
            connection.expireAt(callHourMapBytes, expireTimestamp);
            return null;
        });
    }

    @Override
    public Double getAbilityPerFps(String abilityId) {
        Object o = stringRedisTemplate.opsForHash().get(CacheConstants.ABILITY_CALL_COST_AVG, abilityId);
        if (null != o) {
            return Double.parseDouble(o.toString());
        }
        return null;
    }
}
