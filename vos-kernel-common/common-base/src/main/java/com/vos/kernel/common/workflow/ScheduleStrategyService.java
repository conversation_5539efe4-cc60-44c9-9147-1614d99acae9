package com.vos.kernel.common.workflow;

import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;

/**
 * <AUTHOR>
 * @date 2024年11月26日
 * @version: 1.0
 * @description: TODO
 */
public interface ScheduleStrategyService {

    /**
     * 新增任务
     *
     * @param taskAddRequest
     */
    TaskAddResponse addTask(TaskAddRequest taskAddRequest);
}
