package com.vos.kernel.common.install;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.response.cluster.AbilityInstallLogsResponse;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年09月13日
 * @version: 1.0
 * @description: 算法部署工作流数据
 */
@Data
public class OmInstallWorkflow implements Serializable {

    /**
     * 运行状态描述
     */
    private String status;

    /**
     * 流程ID
     */
    private String workflowId;

    /**
     * 流程名称
     */
    private String workflowName;

    /**
     * 流程定义信息
     */
    private OmInstallWorkflowDef workflowDefinition;


    /**
     * 开始时间
     */
    private Long createTime;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 关闭时间
     */
    private Long endTime;

    /**
     * 关闭时间
     */
    private Long updateTime;


    /**
     * 流程关键输入
     */
    private Map<String, Object> input = new HashMap<>();

    /**
     * 流程关键输出
     */
    private Map<String, Object> output = new HashMap<>();


    /**
     * 当前任务序号
     */
    private AtomicInteger currentSeq = new AtomicInteger(0);


    /**
     * 流程任务
     */
    private List<OmInstallWorkflowTask> tasks = new LinkedList<>();

    /**
     * 失败的任务名称
     */
    private List<String> failedTaskNames = new ArrayList<>();

    /**
     * 失败的任务名称
     */
    private List<String> failedReferenceTaskNames = new ArrayList<>();


    /**
     * 获取当前任务序号
     *
     * @return
     */
    public int addCurrentSeq() {
        return currentSeq.addAndGet(1);
    }

    /**
     * 构造om安装工作流数据
     *
     * @param workflowTypeEnum
     * @param omApp
     * @param input
     * @return
     */
    public static OmInstallWorkflow buildOmInstallWorkflow(WorkflowTypeEnum workflowTypeEnum, String omApp, Map<String, Object> input) {
        LocalDateTime now = LocalDateTime.now();
        OmInstallWorkflow omInstallWorkflow = new OmInstallWorkflow();
        omInstallWorkflow.setStartTime(LocalDateTime.now(ZoneOffset.UTC).toInstant(ZoneOffset.UTC).getEpochSecond());
        omInstallWorkflow.setCreateTime(omInstallWorkflow.getStartTime());
        omInstallWorkflow.setWorkflowId(omApp + "-" + workflowTypeEnum.getCode() + "-" + LocalDateTimeUtil.formatNormal(now));
        //流程定义数据
        omInstallWorkflow.setWorkflowName(omApp + "-" + workflowTypeEnum.getCode());
        OmInstallWorkflowDef workflowDefinition = new OmInstallWorkflowDef();
        workflowDefinition.setName(omInstallWorkflow.getWorkflowName());
        workflowDefinition.setCreateTime(omInstallWorkflow.getStartTime());
        workflowDefinition.setUpdateTime(omInstallWorkflow.getStartTime());
        omInstallWorkflow.setWorkflowDefinition(workflowDefinition);
        omInstallWorkflow.setInput(input);

        return omInstallWorkflow;
    }


    /**
     * 构造简单任务数据
     *
     * @param taskName
     * @param inputData
     * @return
     */
    public OmInstallWorkflowTask buildSimpleTask(String taskNameAlias, String taskName, Map<String, Object> inputData) {
        OmInstallWorkflowTask omInstallWorkflowTask = buildBaseTask(taskName, taskName, this.addCurrentSeq(), inputData);
        WorkflowTaskDef workflowTaskDef = buildSimpleTaskDef(taskNameAlias, taskName, WorkflowTaskEnum.SIMPLE);
//        omInstallWorkflowTask.setWorkflowTask(workflowTaskDef);
        this.tasks.add(omInstallWorkflowTask);
        this.workflowDefinition.getTasks().add(workflowTaskDef);
        return omInstallWorkflowTask;
    }

    /**
     * 构造Fork任务数据
     *
     * @param taskName
     * @param inputData
     * @return
     */
    public List<OmInstallWorkflowTask> buildForkTask(String taskNameAlias, String taskName, Map<String, Object> inputData, List<ForkTaskData> forkTasks) {

        //构造上层任务
        OmInstallWorkflowTask omInstallWorkflowTask = buildBaseTask(taskName, WorkflowTaskEnum.FORK.name(), this.addCurrentSeq(), inputData);
        List<List<WorkflowTaskDef>> forkTaskRefs = new LinkedList<>();

        //下层任务
        List<OmInstallWorkflowTask> omInstallWorkflowSubTasks = new ArrayList<>();
        for (ForkTaskData forkTask : forkTasks) {
            OmInstallWorkflowTask forkOmInstallWorkflowTask = buildBaseTask(forkTask.getName(), forkTask.getName(), this.addCurrentSeq(), forkTask.getInputData());
            WorkflowTaskDef workflowTaskDef = buildSimpleTaskDef(forkTask.getTaskNameAlias(), forkTask.getName(), WorkflowTaskEnum.SIMPLE);
//            forkOmInstallWorkflowTask.setWorkflowTask(workflowTaskDef);
            forkOmInstallWorkflowTask.setForkTask(true);
            forkTaskRefs.add(ListUtil.toList(workflowTaskDef));
            omInstallWorkflowSubTasks.add(forkOmInstallWorkflowTask);
        }
        //构造上层任务定义
        WorkflowTaskDef workflowTaskDef = buildSimpleTaskDef(taskNameAlias, taskName, WorkflowTaskEnum.FORK_JOIN);
        workflowTaskDef.setForkTasks(forkTaskRefs);
//        omInstallWorkflowTask.setWorkflowTask(workflowTaskDef);
        omInstallWorkflowTask.setSuccessful(true);
        omInstallWorkflowTask.setStatus("COMPLETED");
        omInstallWorkflowTask.setOutput(MapUtil.of("subTasks", forkTasks.stream().map(ForkTaskData::getName).collect(Collectors.toList())));

        this.tasks.add(omInstallWorkflowTask);
        this.tasks.addAll(omInstallWorkflowSubTasks);

        this.workflowDefinition.getTasks().add(workflowTaskDef);
        return omInstallWorkflowSubTasks;
    }

//    public static void main(String[] args) {
//        OmInstallWorkflow omInstallWorkflow = new OmInstallWorkflow();
//        ArrayList<ForkTaskData> forkTaskData = new ArrayList<>();
//
//        forkTaskData.add(new ForkTaskData("f1", MapUtil.of("f1", "f1")));
//        forkTaskData.add(new ForkTaskData("f2", MapUtil.of("f2", "f2")));
//        List<OmInstallWorkflowTask> omInstallWorkflowTasks = omInstallWorkflow.buildForkTask("ss", MapUtil.of("ss", "ss"), forkTaskData);
//        CollectionUtil.getFirst(omInstallWorkflowTasks).setSuccessful(true);
//        CollectionUtil.getLast(omInstallWorkflowTasks).setSuccessful(true);
//
//        System.out.println(omInstallWorkflow);
//
//
//    }

    /**
     * 获取Fork子任务
     *
     * @param subTaskName
     * @return
     */
    public OmInstallWorkflowTask getForkSubTask(String subTaskName) {
        return this.tasks.stream().filter(omInstallWorkflowTask -> omInstallWorkflowTask.getTaskDefName().equals(subTaskName)).findFirst().orElse(null);
    }


    /**
     * 构造Join任务数据
     *
     * @param taskName
     * @param inputData
     * @return
     */
    public OmInstallWorkflowTask buildJoinTask(String taskNameAlias, String taskName, Map<String, Object> inputData, List<String> joinOn) {
        OmInstallWorkflowTask omInstallWorkflowTask = buildBaseTask(taskName, WorkflowTaskEnum.JOIN.name(), this.addCurrentSeq(), inputData);

        WorkflowTaskDef workflowTaskDef = buildSimpleTaskDef(taskNameAlias, taskName, WorkflowTaskEnum.JOIN);
        workflowTaskDef.setJoinOn(joinOn);
//        omInstallWorkflowTask.setWorkflowTask(workflowTaskDef);
        this.tasks.add(omInstallWorkflowTask);
        this.workflowDefinition.getTasks().add(workflowTaskDef);
        return omInstallWorkflowTask;
    }


    /**
     * 构造基础任务数据
     *
     * @param taskName
     * @param seq
     * @param inputData
     * @return
     */
    private OmInstallWorkflowTask buildBaseTask(String taskName, String taskType, int seq, Map<String, Object> inputData) {
        OmInstallWorkflowTask omInstallWorkflowTask = new OmInstallWorkflowTask();
        omInstallWorkflowTask.setStartTime(LocalDateTime.now(ZoneOffset.UTC).toInstant(ZoneOffset.UTC).getEpochSecond());
        omInstallWorkflowTask.setTaskDefName(taskType);
        omInstallWorkflowTask.setTaskType(taskType);
        omInstallWorkflowTask.setReferenceTaskName(taskName);
        if (MapUtil.isNotEmpty(inputData)) {
            omInstallWorkflowTask.setInputData(inputData);
        }
        omInstallWorkflowTask.setSeq(seq);
        return omInstallWorkflowTask;
    }

    /**
     * 构造简单任务定义
     *
     * @param taskName
     * @param taskEnum
     * @return
     */
    private WorkflowTaskDef buildSimpleTaskDef(String taskNameAlias, String taskName, WorkflowTaskEnum taskEnum) {
        WorkflowTaskDef workflowTaskDef = new WorkflowTaskDef();
        workflowTaskDef.setName(taskNameAlias);
        workflowTaskDef.setTaskReferenceName(taskName);
        workflowTaskDef.setType(taskEnum.name());
        return workflowTaskDef;
    }

    /**
     * 解析简单的数据结构
     *
     * @return
     */
    public List<AbilityInstallLogsResponse> parseSimpleInstallLogs() {
        List<AbilityInstallLogsResponse> abilityInstallLogsResponses = new ArrayList<>();
        List<WorkflowTaskDef> tasks = this.getWorkflowDefinition().getTasks();
        Map<String, String> nameMap = tasks.stream().collect(Collectors.toMap(WorkflowTaskDef::getTaskReferenceName, WorkflowTaskDef::getName));

        List<OmInstallWorkflowTask> tasksDetail = this.getTasks();
        for (OmInstallWorkflowTask task : tasksDetail) {
            if (task.isForkTask()) {
                continue;
            }
            AbilityInstallLogsResponse abilityInstallLogsResponse = new AbilityInstallLogsResponse();
            abilityInstallLogsResponse.setMessage(nameMap.containsKey(task.getReferenceTaskName()) ? nameMap.get(task.getReferenceTaskName()) : task.getReferenceTaskName());
            abilityInstallLogsResponse.setCreateTime(task.getStartTime());
            Dict dict = new Dict();
            dict.put("input", task.getInputData());
            dict.put("output", task.getOutput());
            abilityInstallLogsResponse.setDetailMessage(JSON.toJSONString(dict));
            abilityInstallLogsResponses.add(abilityInstallLogsResponse);
        }
        return abilityInstallLogsResponses;
    }
}
