package com.vos.kernel.common.utils;

import cn.hutool.core.codec.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class EncryptUtil {

    /**
     * 加密KEY： xxxxxxxx,
     * 加密方式： DES/CBC/PKCS5PADDING
     * */
    public static String encryptStr(String originalPwd) {
        byte[] result = null;
        try {
            String content = "nhdmegun";
            byte[] tt = StringToByte(content);
            IvParameterSpec ivinfo = new IvParameterSpec(tt);
            SecretKeySpec key = new SecretKeySpec(tt, "DES");
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5PADDING");
            byte[] byteContent = originalPwd.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, key, ivinfo);
            result = cipher.doFinal(byteContent);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.encode(result);
    }

    /**
     * 字符串转byte数组
     * */
    private static byte[] StringToByte(String str) {
        int len = str.length();
        byte[] bytes = new byte[len];

        for (int i = 0; i < len; i++) {
            bytes[i] = (byte) (str.charAt(i));
        }
        return bytes;
    }

    public static void main(String[] args) {
        String s = EncryptUtil.encryptStr("http://**********:8895/test?tenantId=6&appId=1592428234317266945&omAppId=M100115000");
        System.out.println(s);

    }
}
