package com.vos.kernel.common.meta;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年05月22日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmbeddingMetaInfo extends WorkflowMeta implements Serializable {
    /**
     * 回调地址
     */
    String callBackUrl;

    /**
     * 调用租户
     */
    String orgCode;

    /**
     * 模型名称
     */
    String model;

    /**
     * 下发时间
     */
    long eventTime;
}
