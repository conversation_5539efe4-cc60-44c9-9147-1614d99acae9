package com.linker.omos.client.domain.request.v2workflow;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class TaskAddRequest implements Serializable {

    @NotBlank(message = "任务类型不能为空;common|custom|algorithm")
    private String taskType;

    @NotBlank(message = "模型类型｜workerName不能为空")
    private String taskName;

    /**
     * 模型ID
     */
    private String model;

    /**
     * 任务下发后多长时间有效:单位毫秒
     */
    private Integer waitTime;

    /**
     * 任务完成后的回调地址
     */
    private String callbackUrl;

    /**
     * 任务执行所需的具体参数 json 格式
     */
    @NotBlank(message = "任务执行所需的具体参数不能为空")
    private String parameters;

    /**
     * 业务元数据,透传返回
     */
    private String bizMeta;

    /**
     * 自定义worker 域名;区分来源
     */
    private String domain;

    /**
     * 自定义worker 任务调用策略 mq|redis
     */
    private String strategy = "redis";
}
