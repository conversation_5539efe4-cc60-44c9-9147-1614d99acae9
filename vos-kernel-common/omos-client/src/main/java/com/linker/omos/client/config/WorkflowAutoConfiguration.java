package com.linker.omos.client.config;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.component.WorkflowTaskClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.scope.ScopedProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(WorkflowTaskProperties.class)
@ConditionalOnProperty(prefix = "workflow.client", name = "enabled", havingValue = "true")
public class WorkflowAutoConfiguration implements ApplicationContextAware, SmartInitializingSingleton {

    @Resource
    private Environment environment;

    /**
     * 任务配置
     */
    private final WorkflowTaskProperties workflowTaskProperties;

    /**
     * 代理的计数器
     */
    private final AtomicLong counter = new AtomicLong(0);

    /**
     * spring 上下文
     */
    private ConfigurableApplicationContext applicationContext;


    @Bean
    public WorkflowTaskClient workflowTaskClient(WorkflowTaskProperties workflowTaskProperties) {
        return new WorkflowTaskClient(workflowTaskProperties);
    }


    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext) applicationContext;
    }


    @Override
    public void afterSingletonsInstantiated() {
        String identity = getIdentity();

        Map<String, Object> beans = this.applicationContext.getBeansWithAnnotation(WorkflowTaskMessageListener.class)
                .entrySet().stream().filter(entry -> !ScopedProxyUtils.isScopedTarget(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        log.info("注册 WorkflowTaskMessageListener size:{}", beans.size());
        WorkflowTaskClient taskClient = this.applicationContext.getBean("workflowTaskClient", WorkflowTaskClient.class);
        Map<String, WorkflowTaskProperties.TaskProperties> collect = CollectionUtil.isEmpty(this.workflowTaskProperties.getTasks()) ? MapUtil.empty()
                : this.workflowTaskProperties.getTasks().stream().collect(Collectors.toMap(WorkflowTaskProperties.TaskProperties::getTaskDefName, container -> container));

        for (Map.Entry<String, Object> entry : beans.entrySet()) {
            DefaultWorkflowTaskListenerContainer container = registerContainer(entry.getKey(), entry.getValue());
            WorkflowTaskProperties.TaskProperties taskProperties = collect.getOrDefault(container.getStreamQueue(), null);
            //初始化
            container.init(identity, taskClient, taskProperties, workflowTaskProperties.getShutdownGracePeriodSeconds(), workflowTaskProperties.getIsProd(), workflowTaskProperties.getIsPublic());
            //设置domain
            if (!BooleanUtil.isTrue(workflowTaskProperties.getIsProd())) {
                String domain = getDomain(container.getDomain());
                container.setDomain(domain);
            }
            if (!BooleanUtil.isTrue(container.getClosed())) {
                //启动
                container.start();
            }else{
                log.info("WorkflowTaskMessageListener 设置关闭状态，不启动：{}", container.getStreamQueue());
            }
        }
    }


    /**
     * 注册容器
     *
     * @param beanName
     * @param bean
     */
    private DefaultWorkflowTaskListenerContainer registerContainer(String beanName, Object bean) {
        Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);
        if (!WorkflowTaskListener.class.isAssignableFrom(bean.getClass())) {
            throw new IllegalStateException(clazz + " 需要实现" + WorkflowTaskListener.class.getName());
        }
        WorkflowTaskMessageListener annotation = clazz.getAnnotation(WorkflowTaskMessageListener.class);

        String streamQueue = annotation.taskDefName();
        if (StrUtil.isBlank(streamQueue)) {
            throw new IllegalStateException("WorkflowTaskMessageListener注解属性taskDefName不能为空");
        }

        String containerBeanName = String.format("%s_%s", DefaultWorkflowTaskListenerContainer.class.getName(), counter.incrementAndGet());
        GenericApplicationContext genericApplicationContext = (GenericApplicationContext) applicationContext;

        genericApplicationContext.registerBean(containerBeanName, DefaultWorkflowTaskListenerContainer.class, () -> createListenerContainer(containerBeanName, bean, annotation));

        log.info("注册 workflow任务处理节点, listenerBeanName:{}, containerBeanName:{}", beanName, containerBeanName);
        return genericApplicationContext.getBean(containerBeanName, DefaultWorkflowTaskListenerContainer.class);
    }

    /**
     * 创建代理
     *
     * @param containerBeanName
     * @param bean
     * @param annotation
     * @return
     */
    private DefaultWorkflowTaskListenerContainer createListenerContainer(String containerBeanName, Object bean, WorkflowTaskMessageListener annotation) {
        DefaultWorkflowTaskListenerContainer container = new DefaultWorkflowTaskListenerContainer();
        container.setMessageListenerProperties(annotation);
        if (WorkflowTaskListener.class.isAssignableFrom(bean.getClass())) {
            container.setWorkflowTaskListener((WorkflowTaskListener) bean);
        } else {
            throw new IllegalStateException("WorkflowTaskListener 类型不正确");
        }
        container.setName(containerBeanName);
        return container;
    }


    /**
     * 当前节点ID
     */
    private String getIdentity() {
        String serverId;
        try {
            serverId = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            serverId = System.getenv("HOSTNAME");
        }
        if (serverId == null) {
            serverId = UUID.randomUUID().toString();
        }
        log.info("WorkflowTask Setting worker id to {}", serverId);
        return serverId;
    }


    /**
     * 获取域名
     *
     * @return
     */
    private String getDomain(String containerDomain) {
        if (!BooleanUtil.isTrue(workflowTaskProperties.getIsProd())) {
            if (StrUtil.isNotBlank(containerDomain) && containerDomain.startsWith("dev")) {
                return containerDomain;
            } else {
                String token = System.getenv("TOKEN");
                if (StrUtil.isBlank(token)) {
                    token = environment.getProperty("TOKEN");
                    if (StrUtil.isBlank(token)) {
                        throw new IllegalStateException("自定义节点开发模式下需要设置TOKEN环境变量");
                    }
                }
                log.info("WorkflowTask Setting domain to {}", token);
                return token;
            }
        }
        return "";
    }
}
