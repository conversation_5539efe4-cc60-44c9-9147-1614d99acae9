package com.linker.omos.client.domain.request.v2workflow;

import com.linker.core.utils.IdWorker;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年12月11日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskMessage implements Serializable {

    private String id = String.valueOf(IdWorker.nextId());

    /**
     * 任务下发后多长时间有效:单位毫秒
     */
    private Integer waitTime;


    /**
     * 任务执行所需的具体参数 json 格式
     */
    private String payload;

    /**
     * 业务元数据,透传返回
     */
    private String bizMeta;


    /**
     * 回调地址
     */
    private String callbackUrl;

}
