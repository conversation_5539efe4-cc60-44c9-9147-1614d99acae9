package com.linker.omos.client.domain.response.cluster;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年11月10日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class AbilityInstallLogsResponse implements Serializable {

    /**
     * 发生时间
     */
    private long createTime;

    /**
     * 日志阶段
     */
    private String message;


    /**
     * 详细日志
     */
    private String detailMessage;
}
