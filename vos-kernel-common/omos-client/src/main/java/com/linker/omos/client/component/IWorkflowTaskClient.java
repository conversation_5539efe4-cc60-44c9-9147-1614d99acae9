package com.linker.omos.client.component;

import com.linker.omos.client.domain.Response;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月19日
 * @version: 1.0
 * @description: TODO
 */
public interface IWorkflowTaskClient {

    /**
     * 请求api 拉取消息
     *
     * @param taskPollRequest
     * @return
     */
    Response<List<TaskMessage>> batchPoll(TaskPollRequest taskPollRequest);


    /**
     * 请求api 更新消息状态
     *
     * @param taskUpdateRequest
     * @return
     */
    Response<Boolean> updateTaskStatus(TaskUpdateRequest taskUpdateRequest);

    /**
     * 关闭连接
     */
    void shutdown() throws InterruptedException;
}
