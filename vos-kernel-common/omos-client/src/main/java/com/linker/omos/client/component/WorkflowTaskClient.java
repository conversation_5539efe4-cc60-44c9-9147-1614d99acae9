package com.linker.omos.client.component;

import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.config.WorkflowTaskProperties;
import com.linker.omos.client.domain.Response;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024年12月12日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class WorkflowTaskClient {

    TaskApiComponent taskApiService;

    WorkflowTaskProperties workflowTaskProperties;

    /**
     * 构造
     *
     * @param workflowTaskProperties
     */
    public WorkflowTaskClient(WorkflowTaskProperties workflowTaskProperties) {
        this.workflowTaskProperties = workflowTaskProperties;
        this.init(workflowTaskProperties.getEndpoint(), workflowTaskProperties.getTimeoutInMs());
    }


    /**
     * 初始化
     *
     * @param endpoint
     * @param pollTimeoutInMs
     */
    private void init(String endpoint, Long pollTimeoutInMs) {
        if (StrUtil.isEmpty(endpoint)) {
            throw new RuntimeException("endpoint is empty");
        }
        if (StrUtil.isEmpty(workflowTaskProperties.getAppSecret())) {
            throw new RuntimeException("appSecret is empty");
        }
        OkHttpClient.Builder builder = new OkHttpClient().newBuilder();
        OkHttpClient okHttpClient = builder
                .readTimeout(pollTimeoutInMs, TimeUnit.MILLISECONDS)
                .connectTimeout(pollTimeoutInMs, TimeUnit.MILLISECONDS)
                .writeTimeout(pollTimeoutInMs, TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(30, 360, TimeUnit.SECONDS))
                .retryOnConnectionFailure(true)
                .addInterceptor(new AuthenticationInterceptor(workflowTaskProperties.getAppSecret(), workflowTaskProperties.getIsPublic()))
                .build();
        okHttpClient.dispatcher().setMaxRequests(1024);
        okHttpClient.dispatcher().setMaxRequestsPerHost(512);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(endpoint)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .addCallAdapterFactory(BodyCallAdapterFactory.INSTANCE)
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())

                .build();

        this.taskApiService = retrofit.create(TaskApiComponent.class);
        log.info("初始化 workflow客户端 完成,endpoint:{}", endpoint);
    }


    /**
     * 请求api 拉取消息
     *
     * @param taskPollRequest
     * @return
     */
    public Response<List<TaskMessage>> batchPoll(TaskPollRequest taskPollRequest) {
        //动态更改 超时时间
        return taskApiService.batchPoll(taskPollRequest);
    }


    /**
     * 请求api 更新消息状态
     *
     * @param taskUpdateRequest
     * @return
     */
    public Response<Boolean> updateTaskStatus(TaskUpdateRequest taskUpdateRequest) {
        //动态更改 超时时间
        return taskApiService.updateTaskStatus(taskUpdateRequest);
    }

}
