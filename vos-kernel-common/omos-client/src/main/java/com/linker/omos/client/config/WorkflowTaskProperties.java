package com.linker.omos.client.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Data
@ConfigurationProperties("workflow.client")
public class WorkflowTaskProperties {

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 请求地址
     */
    private String endpoint;

    /**
     * 应用appSecret
     */
    private String appSecret;

    /**
     * 连接超时时间
     */
    private long timeoutInMs = 3000;


    /**
     * 是否生产环境
     */
    private Boolean isProd;

    /***
     * 是否官方节点
     */
    private Boolean isPublic;
    /**
     * 任务详细配置
     */
    private List<TaskProperties> tasks;


    /**
     * 优雅关闭
     */
    private int shutdownGracePeriodSeconds = 10;


    /**
     * 任务详细配置
     */
    @Data
    public static class TaskProperties {

        /**
         * taskName
         */
        private String taskDefName;

        /**
         * 是否关闭
         */
        private Boolean closed;

        /**
         * 平台id
         */
        private String taskAasId;

        /**
         * 处理线程数
         */
        private Integer threadNum;

        /**
         * 线程前缀
         */
        private String threadNamePrefix = "workflow-worker-%d";

        /**
         * 访问域名
         */
        private String domain;

        /**
         * 拉取批次大小
         */
        private Integer pollBatchSize;

        /**
         * 拉取间隔
         */
        private Long pollingInterval;


        /**
         * 拉取超时时间
         */
        private Long pollTimeoutInMs;

        /***
         * 是否官方节点
         */
        private Boolean isPublic;
    }


}
