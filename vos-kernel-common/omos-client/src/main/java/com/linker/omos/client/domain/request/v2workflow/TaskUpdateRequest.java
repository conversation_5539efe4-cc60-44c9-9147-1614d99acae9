package com.linker.omos.client.domain.request.v2workflow;

import com.linker.omos.client.config.WorkflowTaskExecLog;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class TaskUpdateRequest implements Serializable {

    @NotBlank(message = "任务唯一标识不能为空")
    private String taskId;

    /**
     * 任务执行输出
     */
    private Object outputData;

    /**
     * 状态
     */
    private WorkflowStatusEnum status;

    /**
     * 失败原因
     */
    private String reasonForIncompletion;

    /**
     * 执行者
     */
    private String workerId;

    /**
     * 业务元数据,透传返回
     */
    private String bizMeta;

    /**
     * 回调地址
     */
    private String callbackUrl;


    /**
     * task 日志
     */
    private List<WorkflowTaskExecLog> logs;
}
