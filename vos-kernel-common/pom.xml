<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.vos.kernel</groupId>
        <artifactId>vos-kernel</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>vos-kernel-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>vos-kernel-common</name>
    <packaging>pom</packaging>
    <description>vos-kernel-common</description>

    <modules>
        <module>common-base</module>
        <module>common-storage</module>
        <module>xxl-job-starter</module>
        <module>dtp-adapter-dubbo-starter</module>
        <module>redis-multi-starter</module>
        <module>redis-dynmic-datasouce-starter</module>
        <module>omos-client</module>
    </modules>
</project>
