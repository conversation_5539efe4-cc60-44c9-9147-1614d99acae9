package com.vos.kernel.common.aspect;

import com.vos.kernel.common.platform.FileStorage;
import lombok.Data;

import java.util.Iterator;

/**
 * 删除的切面调用链
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/10
 * @description: com.vos.kernel.common.aspect
 */
@Data
public class DeleteAspectChain {
    private DeleteAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public DeleteAspectChain(Iterable<FileStorageAspect> aspects, DeleteAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public boolean next(String relativePath, FileStorage fileStorage) {
        if (aspectIterator.hasNext()) {
            return aspectIterator.next().deleteAround(this, relativePath, fileStorage);
        } else {
            return callback.run(relativePath, fileStorage);
        }
    }

}
