package com.vos.kernel.dtp.adapter.dubbo;

import com.vos.kernel.dtp.adapter.dubbo.condition.ConditionOnApacheDubboApp;
import org.dromara.dynamictp.core.spring.DtpBaseBeanConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/15
 * @description: com.vos.kernel.dtp.adapter.dubbo
 */
@Configuration
@ConditionOnApacheDubboApp
@AutoConfigureAfter({DtpBaseBeanConfiguration.class})
@ConditionalOnBean({DtpBaseBeanConfiguration.class})
@SuppressWarnings("all")
public class ApacheDubboTpAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ApacheDubboDtpAdapter apacheDubboDtpHandler() {
        return new ApacheDubboDtpAdapter();
    }
}
