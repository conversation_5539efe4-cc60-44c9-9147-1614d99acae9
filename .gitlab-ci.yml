include:
  - remote: 'http://*************/ci-yml/template_default-pipeline.yml'

variables:

  ## 作业控制
  RUN_PIPELINE_BUILD:     "yes"     #是否运行构建 yes/no 
  RUN_CODE_ANALYSIS:      "no"      #是否代码扫描 yes/no
  RUN_BUILD_HBT_IMAGE:    "yes"     #是否生成hbt镜像 yes/no
  RUN_BUILD_LINKER_IMAGE: "no"      #是否生成linkercc镜像 yes/no

  ## 依赖容器镜像
  BUILD_IMAGE: "hbt.linker.cc/citools/maven:self-3.5.3-3-fix"
  
  ## 构建测试参数
  #MAVEN_OPTS: "-Dmaven.repo.local=/home/<USER>/ci-build-cache/maven "  
  BUILD_SHELL: 'mvn clean package  -DskipTests '   #构建命令
  #GRADLE_OPTS: ""               #gradle构建参数

  ## 单元测试参数
  #TEST_SHELL : 'mvn test  --settings=./settings.xml   '       #测试命令
  #JUNIT_REPORT_PATH: 'target/surefire-reports/TEST-*.xml'   #单元测试报告

  ## 代码扫描
  SONAR_SOURCE_DIR : "ilink-manage-service/src"                                          #项目源码目录
  SONAR_SERVER_LOGIN: '${SONAR_TOKEN}'    #Sonar Token最好在项目中定义。
  SONAR_SCAN_ARGS: "-Dsonar.sources=${SONAR_SOURCE_DIR} 
                   -Dsonar.java.binaries=ilink-manage-service/target/classes 
                   -Dsonar.java.test.binaries=ilink-manage-service/target/test-classes 
                   -Dsonar.java.surefire.report=ilink-manage-service/target/surefire-reports "                                     #项目扫描参数

  ## 构建hbt镜像
  #CI_REGISTRY: 'hbt.linker.cc'               #镜像仓库地址
  #CI_REGISTRY_USER: '${HBT_REGISTRY_USER}'                               #仓库用户信息
  #CI_REGISTRY_PASSWD: '${HBT_REGISTRY_PASSWD}'                              #仓库用户密码
  #IMAGE_NAME: "${CI_REGISTRY}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"     #镜像名称

  ### 构建linkercc镜像
  #CI_LINKER_REGISTRY: 'registry.linker.cc'               #镜像仓库地址              
  #CI_LINKER_REGISTRY_USER: 'dong_jing'                               #仓库用户信息
  #CI_LINKER_REGISTRY_PASSWD: 'xxxxxxxx.'                              #仓库用户密码
  #IMAGE_NAME: "${CI_LINKER_REGISTRY}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"     #镜像名称
  #DOCKER_FILE_PATH: "./Dockerfile"                              #Dockerfile位置
