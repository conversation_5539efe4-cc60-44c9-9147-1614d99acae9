package com.vos.task.automated.api.model.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28
 * @description: com.vos.task.automated.api.model.dto
 */
@Data
public class AbilityAddWithOutOmDTO implements Serializable {

    /**
     * 能力ID
     */
    private String abilityId;

    /**
     * 适用类型 视频 VIDEO(1),音频MUSIC(2),视频流STREAM(3)
     */
    private String suitTypes;


    /**
     * 运行方式 算子串行 OPERATOR(1),能力*ABILITY(2)
     */
    private Integer type;

    /**
     * 所属分类
     */
    private Long classifyId;

    /**
     * 最大算力
     */
    private Integer maxOperator;

    /**
     * 能力名称
     */
    private String abilityName;

    /**
     * 能力编码
     */
    private String abilityCode;

    /**
     * chatModelName
     */
    private String chatModelName;


    /**
     * chatApiKey
     */
    private String chatApiKey;

    /**
     * 服务器地址名称
     */
    private String serverUrl;

    /**
     * 是否比对算法
     */
    private Boolean isMatchType;

    /**
     * 比对库上传地址
     */
    private String matchUploadUrl;

    /**
     * 串行能力必传 新汇智能力公式
     */
    private String formula;

    /**
     * 新汇智能力id operator_id
     */
    private Long operatorId;

    /**
     * 能力备注
     */
    private String abilityRemark;

    /**
     * 串行能力必传 前端数据
     */
    private String htmlJson;

    /**
     * 应用回流地址
     */
    private String reflowUrl;

    /**
     * 应用版本号
     */
    private String versionId;

    /**
     * appKey
     */
    private String appKey;

    /**
     * 能力类型：识别算法 0；比对算法1；视频流算法：2；v3算法：3；其他算法：99
     */
    private Integer abilityEnum;

    private String authenticationId;

    /**
     * 高级配置参数
     */
    private String applicationJson;

    /**
     * 算法并发限制
     */
    private int abilityConcurrent = 2;

    public String getAbilityId() {
        return StrUtil.trim(abilityId);
    }

    public String getServerUrl() {
        return StrUtil.trim(serverUrl);
    }

    public String getMatchUploadUrl() {
        return StrUtil.trim(matchUploadUrl);
    }
}
