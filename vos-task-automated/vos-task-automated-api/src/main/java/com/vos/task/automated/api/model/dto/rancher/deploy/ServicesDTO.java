package com.vos.task.automated.api.model.dto.rancher.deploy;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * k8s service 创建
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/1
 * @description: com.vos.task.automated.api.model.dto.rancher.deploy
 */
@Data
public class ServicesDTO implements Serializable {

    /**
     * 类型：service
     */
    private String type = "service";

    /**
     * 命名空间
     */
    private String namespaceId;

    /**
     * DNS类型
     */
    private String kind = "ClusterIP";

    /**
     * 名称
     */
    private String name;

    /**
     * 映射端口信息
     */
    private List<ServicePortDTO> ports;

    /**
     * 关联pod
     */
    private List<String> targetWorkloadIds;

}
