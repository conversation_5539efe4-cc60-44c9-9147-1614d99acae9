package com.vos.task.automated.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.entity.R;
import com.vos.kernel.common.enums.BizResponseEnum;
import com.vos.kernel.common.install.OmInstallWorkflow;
import com.vos.kernel.common.install.OmInstallWorkflowTask;
import com.vos.kernel.common.install.WorkflowTypeEnum;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.*;
import com.vos.task.automated.api.model.dto.gpu.GpuOperateItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.dto.om.ServingConfigVo;
import com.vos.task.automated.api.model.entity.*;
import com.vos.task.automated.api.model.enums.AbilityNodeStatusEnum;
import com.vos.task.automated.api.model.enums.DeployPatternEnum;
import com.vos.task.automated.api.model.enums.ScalingModeEnum;
import com.vos.task.automated.api.model.enums.ScalingStatusEnum;
import com.vos.task.automated.api.service.rpc.IServingRouterRpcService;
import com.vos.task.automated.service.config.DeployConfigProperties;
import com.vos.task.automated.service.convert.AbilityConvertMapper;
import com.vos.task.automated.service.entity.K8sScalePodResult;
import com.vos.task.automated.service.entity.ScaleAbilityConfig;
import com.vos.task.automated.service.entity.ScaleBatchExpandResult;
import com.vos.task.automated.service.entity.StageExpandErrorMessage;
import com.vos.task.automated.service.service.IScaleHistoryService;
import com.vos.task.automated.service.service.IScaleService;
import com.vos.task.automated.service.service.batisplus.IAbilityNodeInfoService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.automated.service.service.impl.ai.OmWorkflowDataBuilderService;
import com.vos.task.automated.service.service.impl.ai.gpu.MultiClusterAtomGpuApplyResult;
import com.vos.task.automated.service.service.impl.ai.install.*;
import com.vos.task.automated.service.service.impl.ai.master.MasterScheduleConfig;
import com.vos.task.automated.service.service.rpc.AbilityAutomatedRpcServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alibaba.nacos.common.utils.ExceptionUtil.getStackTrace;
import static com.vos.task.automated.service.schedule.NewScaleScheduleHandle.checkIsHydra;

/**
 * <AUTHOR>
 * @date 2024年06月19日
 * @version: 1.0
 * @description: 并行扩容
 */
@Slf4j
@Component
public class ConcurrentScaleService extends ScaleServiceImpl {

    @Resource
    IScaleHistoryService scaleHistoryService;

    @Resource
    DeployConfigProperties deployConfigProperties;

    @Resource
    DtpExecutor omDeleteThreadPool;

    @Resource
    IAbilityNodeInfoService abilityNodeInfoService;

    @Resource
    GpuPreTokenOperate gpuPreTokenOperate;

    @Resource
    MultiClusterGpuPreTokenOperate multiClusterGpuPreTokenOperate;

    @Resource
    VirtualGpuDeployOperate virtualGpuDeployOperate;

    @Resource
    VirtualGpuTakenOperate virtualGpuTakenOperate;

    @Resource
    ScaleDataBaseOperate scaleDataBaseOperate;

    @Resource
    MasterScheduleConfig masterScheduleConfig;

    @Resource
    AbilityConvertMapper abilityConvertMapper;

    @Resource
    ExceptionHandler exceptionHandler;


    @Autowired
    private IServingRouterRpcService servingRouterRpcService;

    @Value("${deploy.abilityTrafficNum:10}")
    private Integer abilityTrafficNum;

    /**
     * 获取最近一次自动扩缩容时间
     *
     * @param abilityId
     * @return
     */
    private ScaleHistoryEntity getLastAutoExpandAndShrinkTime(Long abilityId) {

        QueryWrapper<ScaleHistoryEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.lambda().select(ScaleHistoryEntity::getUpdateTime).eq(ScaleHistoryEntity::getAbilityId, abilityId)
                .eq(ScaleHistoryEntity::getScalingMode, ScalingModeEnum.AUTO_SCALE.getMode()).orderByDesc(ScaleHistoryEntity::getUpdateTime).last(" limit 1");
        return scaleHistoryService.getOne(entityQueryWrapper);
    }

    /**
     * 并行手动扩缩容九头蛇算子算法
     *
     * @param taskServerRouterEntity
     * @param abilityNodeInfoEntity
     * @param nodeCountMap
     */
    @Async(value = "abilityScaleThreadPool")
    public void concurrentManualScaleHydra(TaskServerRouterEntity taskServerRouterEntity, AbilityNodeInfoEntity abilityNodeInfoEntity, Map<Long, Integer> nodeCountMap) {
        if (taskServerRouterEntity == null) {
            log.warn("routerEntity 为空，直接退出");
            abilityNodeInfoEntity.setStatus(2);
            abilityNodeInfoService.updateById(abilityNodeInfoEntity);
            return;
        }
        Integer copySize = nodeCountMap.getOrDefault(abilityNodeInfoEntity.getId(), 0);
        Integer scaleNum = abilityNodeInfoEntity.getExceptDuplicateNum();
        Long abilityId = taskServerRouterEntity.getAbilityId();
        log.info("{},扩容数量计算：期望节点数:{},当前节点数:{}", abilityId, scaleNum, copySize);
        if (Objects.equals(copySize, scaleNum)) {
            return;
        }
        // 判断当前安装队列是否为空；不为空则放弃本次扩容；等待下次
        Integer waitNum = abilityOperateQueueService.getWaitNum();
        if (null != waitNum && waitNum > 0) {
            log.info("部署队列存在排队，数量：{},放弃本次扩容；等待下次", waitNum);
            return;
        }
        //真实部署状态中
        abilityNodeInfoEntity.setDeployProcess(AbilityNodeStatusEnum.DOING.getStatus());
        abilityNodeInfoService.updateById(abilityNodeInfoEntity);

        int needNum = scaleNum - copySize;
        //构造本地扩缩容记录
        ScaleHistoryEntity scaleHistoryEntity = buildScaleHistory(abilityId, ScalingModeEnum.MANUAL_SCALE, copySize, scaleNum);
        if (needNum > 0) {
            //指定卡配置
            GpuScaleChooseConfig gpuScaleChooseConfig = new GpuScaleChooseConfig();
            gpuScaleChooseConfig.setPhysicalGpuId(abilityNodeInfoEntity.getGpuId());
            gpuScaleChooseConfig.setHost(abilityNodeInfoEntity.getHost());
            gpuScaleChooseConfig.setMachineGroup(abilityNodeInfoEntity.getMachineGroup());
            gpuScaleChooseConfig.setAbilityNodeInfoEntity(abilityNodeInfoEntity);
            //扩容
            concurrentExpandAbilityPod(taskServerRouterEntity, scaleHistoryEntity, gpuScaleChooseConfig, Math.abs(needNum));
        } else {
            //缩容
            concurrentShrinkAbilityPod("", taskServerRouterEntity, abilityNodeInfoEntity, false, scaleHistoryEntity, Math.abs(needNum));
        }
    }


    /**
     * 并行手动扩缩容普通算法
     *
     * @param ability
     * @param queueTrafficInfo
     * @param abilityNodeInfoEntity
     * @param nodeCountMap
     */
    @Async(value = "abilityScaleThreadPool")
    public void concurrentManualAbility(TaskServerRouterEntity ability, Integer queueTrafficInfo, AbilityNodeInfoEntity abilityNodeInfoEntity, Map<Long, Integer> nodeCountMap) {
        if (ability == null) {
            log.warn("routerEntity 为空，直接退出");
            abilityNodeInfoEntity.setStatus(2);
            abilityNodeInfoService.updateById(abilityNodeInfoEntity);
            return;
        }
        Integer scaleThreshold = Optional.ofNullable(ability.getScaleThreshold()).orElse(0);
        if (queueTrafficInfo < scaleThreshold && scaleThreshold > 0) {
            abilityNodeInfoEntity.setStatus(AbilityNodeStatusEnum.FAIL.getStatus());
            abilityNodeInfoEntity.setRemark("扩容异常： 扩缩容 阈值");
            abilityNodeInfoService.updateById(abilityNodeInfoEntity);
            return;
        }
        String atomId = abilityNodeInfoEntity.getAtomId();
        Integer scaleNum = abilityNodeInfoEntity.getExceptDuplicateNum();
        Integer copySize = nodeCountMap.getOrDefault(abilityNodeInfoEntity.getId(), 0);
        log.info("ability{},atom:{}扩容数量计算：期望节点数:{},当前节点数:{}", ability.getAbilityId(), atomId, scaleNum, copySize);
        if (Objects.equals(copySize, scaleNum)) {
            abilityNodeInfoEntity.setStatus(AbilityNodeStatusEnum.SUCCESS.getStatus());
            abilityNodeInfoService.updateById(abilityNodeInfoEntity);
            return;
        }
        // 判断当前安装队列是否为空；不为空则放弃本次扩容；等待下次
        Integer waitNum = abilityOperateQueueService.getWaitNum();
        if (null != waitNum && waitNum > 0) {
            log.info("部署队列存在排队，数量：{},放弃本次扩容；等待下次", waitNum);
            return;
        }
        //真实部署状态中
        abilityNodeInfoEntity.setDeployProcess(AbilityNodeStatusEnum.DOING.getStatus());
        abilityNodeInfoService.updateById(abilityNodeInfoEntity);

        int needNum = scaleNum - copySize;
        //构造本地扩缩容记录
        ScaleHistoryEntity scaleHistoryEntity = buildScaleHistory(ability.getAbilityId(), ScalingModeEnum.MANUAL_SCALE, copySize, scaleNum);
        if (needNum > 0) {
            //指定卡配置
            GpuScaleChooseConfig gpuScaleChooseConfig = new GpuScaleChooseConfig();
            gpuScaleChooseConfig.setAbilityNodeInfoEntity(abilityNodeInfoEntity);
            gpuScaleChooseConfig.setPhysicalGpuId(abilityNodeInfoEntity.getGpuId());
            gpuScaleChooseConfig.setHost(abilityNodeInfoEntity.getHost());
            gpuScaleChooseConfig.setMachineGroup(abilityNodeInfoEntity.getMachineGroup());
            //扩容
            concurrentExpandAbilityPod(ability, scaleHistoryEntity, gpuScaleChooseConfig, Math.abs(needNum));
        } else {
            //缩容
            concurrentShrinkAbilityPod("", ability, abilityNodeInfoEntity, false, scaleHistoryEntity, Math.abs(needNum));
        }

    }


    /**
     * 基于下发量与处理量并发扩缩容
     *
     * @param ability
     * @param requestPerTime
     * @param canCallPerTime
     */
    @Async(value = "abilityScaleThreadPool")
    public void concurrentAutoExpandAndShrink(TaskServerRouterEntity ability, Integer requestPerTime, Integer canCallPerTime) {
        Long abilityId = ability.getAbilityId();
        //是否需要扩容
        Integer needScale = ability.getNeedScale();
        if (needScale != null && needScale != 1) {
            log.info("自动扩缩容应用配置无需扩缩容:{}", abilityId);
            return;
        }
        //是否在扩缩容的冷却期内
        ScaleHistoryEntity lastAutoExpandAndShrink = getLastAutoExpandAndShrinkTime(abilityId);
        if (lastAutoExpandAndShrink != null) {
            String status = lastAutoExpandAndShrink.getStatus();
            if (ScalingStatusEnum.IN_PROGRESS.getStatus().equals(status)) {
                log.warn("自动扩缩容正在进行中:{}", abilityId);
                return;
            }
            LocalDateTime lastAutoExpandAndShrinkTime = LocalDateTimeUtil.parse(lastAutoExpandAndShrink.getUpdateTime(), "yyyy-MM-dd HH:mm:ss");
            long between = LocalDateTimeUtil.between(lastAutoExpandAndShrinkTime, LocalDateTime.now(), ChronoUnit.SECONDS);
            //上次扩缩容距离当前大于冷却期
            if (Math.abs(between) < scaleCoolingOffPeriod) {
                log.info("{},自动扩缩容的冷却期内,上次执行：{}", abilityId, lastAutoExpandAndShrinkTime);
                return;
            }
        }

        //单节点1分可处理数校验
        if (canCallPerTime == null || canCallPerTime == 0) {
            log.warn("{},自动扩缩容canCallPerTime为空", ability.getAbilityId());
            return;
        }
        //扩缩容逻辑判断
        int expandAndShrinkNum = 0;
        BigDecimal sub = NumberUtil.sub(requestPerTime, canCallPerTime);
        if (sub.compareTo(BigDecimal.ZERO) < 0) {
            //如果当前请求量与可处理数的占比
            if (NumberUtil.div(requestPerTime, canCallPerTime).compareTo(new BigDecimal("0.75")) > 0) {
                expandAndShrinkNum = 1;
            }
        } else {
            // sub大于等于0的逻辑处理
            BigDecimal div = NumberUtil.div(sub, canCallPerTime, BigDecimal.ROUND_HALF_UP);
            expandAndShrinkNum = Math.max(NumberUtil.add(div, 1).intValue(), 0);
        }
        //当前算法副本列表
        List<TaskServerRouterEntity> copyRouters = getCopyRouters(ability.getAbilityId());
        int copySize = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
        Integer scaleMax = getScaleMax(ability);
        log.info("{},自动扩缩容数量计算,当前算法副本数量:{},计算最终需要副本数量:{},最大扩容数量:{}", abilityId, copySize, expandAndShrinkNum, scaleMax);
        //开始
        int needNum = expandAndShrinkNum - copySize;
        if (needNum > 0) {
            //配置了扩容上限
            if (scaleMax != -1) {
                needNum = Math.min((scaleMax - copySize), needNum);
            }
            if (needNum < 1) {
                return;
            }
        }
        if (expandAndShrinkNum != copySize) {
            //构造本地扩缩容记录
            ScaleHistoryEntity scaleHistoryEntity = buildScaleHistory(abilityId, ScalingModeEnum.AUTO_SCALE, copySize, expandAndShrinkNum);
            if (needNum > 0) {
                //扩容
                concurrentExpandAbilityPod(ability, scaleHistoryEntity, new GpuScaleChooseConfig(), Math.abs(needNum));
            } else {
                //缩容
                concurrentShrinkAbilityPod("", ability, null, true, scaleHistoryEntity, Math.abs(needNum));
            }
        }
    }

    public void expandAndShrink(TaskServerRouterEntity ability, int expandAndShrinkNum, ScaleHistoryEntity scaleHistoryEntity) {
        if (expandAndShrinkNum > 0) {
            //扩容
            concurrentExpandAbilityPod(ability, scaleHistoryEntity, new GpuScaleChooseConfig(), Math.abs(expandAndShrinkNum));
        } else {
            //缩容
            concurrentShrinkAbilityPod("", ability, null, true, scaleHistoryEntity, Math.abs(expandAndShrinkNum));
        }
    }

    /**
     * 构造
     *
     * @return
     */
    private ScaleHistoryEntity buildScaleHistory(Long abilityId, ScalingModeEnum scalingModeEnum, Integer currentPodNum, Integer expectPodNum) {
        ScaleHistoryEntity scaleHistoryEntity = new ScaleHistoryEntity();
        scaleHistoryEntity.setScalingMode(scalingModeEnum.getMode());
        scaleHistoryEntity.setAbilityId(abilityId.toString());
        scaleHistoryEntity.setCurrentNodeCount(currentPodNum);
        scaleHistoryEntity.setExpectedNodeCount(expectPodNum);
        scaleHistoryEntity.setUpdateTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        scaleHistoryEntity.setStatus(ScalingStatusEnum.IN_PROGRESS.getStatus());
        scaleHistoryService.save(scaleHistoryEntity);

        return scaleHistoryEntity;
    }

    /**
     * 并发扩容
     *
     * @param taskServerRouterEntity 算法部署信息
     * @param scaleHistoryEntity     扩缩容记录
     * @param gpuChooseConfig        部署卡限制
     * @param needNum
     */
    public void concurrentExpandAbilityPod(TaskServerRouterEntity taskServerRouterEntity, ScaleHistoryEntity scaleHistoryEntity, GpuScaleChooseConfig gpuChooseConfig, Integer needNum) {

        Integer scaleBatchSize = deployConfigProperties.getScaleBatchSize();
        int batchSize = NumberUtil.ceilDiv(needNum, scaleBatchSize);
        //扩容流转数据
        OmInstallWorkflow omInstallWorkflow = OmInstallWorkflow.buildOmInstallWorkflow(WorkflowTypeEnum.SCALE, taskServerRouterEntity.getAbilityId().toString(),
                OmWorkflowDataBuilderService.buildBatchScaleInputData(scaleBatchSize, needNum, batchSize));
        log.info("{},目标数量：{},批次：{}", taskServerRouterEntity.getAbilityId(), needNum, batchSize);

        if (batchSize == 1) {
            //批次扩容
            ScaleBatchExpandResult scaleBatchExpandResult = batchExpandAbilityPod(taskServerRouterEntity, gpuChooseConfig, needNum, omInstallWorkflow);
            scaleHistoryEntity.setUpdateTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
            int successNum = scaleBatchExpandResult.getSuccessNum();
            scaleHistoryEntity.setActualNodeCount(scaleBatchExpandResult.getSuccessNum());
            scaleHistoryEntity.setDetail(JSON.toJSONString(scaleBatchExpandResult.getErrorMessage()));
            if (successNum == needNum) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
            } else if (successNum > 0) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.PART_SUCCESSFUL.getStatus());
            } else {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            }
        } else {
            List<ScaleBatchExpandResult> scaleBatchExpandResults = new ArrayList<>();
            for (int i = 0; i < batchSize; i++) {
                //需要部署的数量
                int deployNum = scaleBatchSize;
                if (i == batchSize - 1) {
                    //最后一个需要部署的数量
                    deployNum = needNum - scaleBatchSize * i;
                }
                //批次扩容
                ScaleBatchExpandResult scaleBatchExpandResult = batchExpandAbilityPod(taskServerRouterEntity, gpuChooseConfig, deployNum, omInstallWorkflow);
                scaleBatchExpandResults.add(scaleBatchExpandResult);
                if (!BooleanUtil.isTrue(scaleBatchExpandResult.getExpandResult())) {
                    log.warn("{},批次：{}扩容异常，跳出下轮批次，总批次：{}", taskServerRouterEntity.getAbilityId(), i, batchSize);
                    break;
                }
            }
            //此次扩容记录详情
            int sum = scaleBatchExpandResults.stream().mapToInt(ScaleBatchExpandResult::getSuccessNum).sum();
            scaleHistoryEntity.setUpdateTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
            scaleHistoryEntity.setActualNodeCount(sum);
            if (sum == needNum) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
            } else if (sum > 0) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.PART_SUCCESSFUL.getStatus());
            } else {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            }
            scaleHistoryEntity.setDetail(JSON.toJSONString(scaleBatchExpandResults.stream().map(ScaleBatchExpandResult::getErrorMessage).flatMap(Collection::stream).distinct().collect(Collectors.toList())));
        }
        //此次扩容阶段结束处理scaleHistoryEntity
        scaleHistoryEntity.setScaleWorkflow(JSON.toJSONString(omInstallWorkflow, SerializerFeature.DisableCircularReferenceDetect));
        scaleHistoryService.updateById(scaleHistoryEntity);
        //更新abilityNodeInfoService
        if (gpuChooseConfig != null && gpuChooseConfig.getAbilityNodeInfoEntity() != null) {
            AbilityNodeInfoEntity abilityNodeInfoEntity = gpuChooseConfig.getAbilityNodeInfoEntity();
            abilityNodeInfoEntity.setStatus(scaleHistoryEntity.getActualNodeCount() > 0 ? AbilityNodeStatusEnum.SUCCESS.getStatus() : AbilityNodeStatusEnum.FAIL.getStatus());
            abilityNodeInfoEntity.setDeployProcess(AbilityNodeStatusEnum.SUCCESS.getStatus());
            abilityNodeInfoService.updateById(abilityNodeInfoEntity);
        }

        //清除缓存
        clearCache(taskServerRouterEntity);
    }

    /**
     * 批次扩容
     *
     * @param taskServerRouterEntity
     * @param expandNum
     */
    private ScaleBatchExpandResult batchExpandAbilityPod(TaskServerRouterEntity taskServerRouterEntity, GpuScaleChooseConfig gpuChooseConfig, Integer expandNum, OmInstallWorkflow omInstallWorkflow) {

        ScaleBatchExpandResult scaleBatchExpandResult = new ScaleBatchExpandResult();
        List<ScaleAbilityConfig> scaleAbilityConfigs = new ArrayList<>();
        List<StageExpandErrorMessage> errorMessage = new ArrayList<>();
        //占用gpu资源
        for (int i = 0; i < expandNum; i++) {
            try {
                ScaleAbilityConfig scaleAbilityConfig = buildAbilityDeployConfigAndOccupyGpu(taskServerRouterEntity, gpuChooseConfig, omInstallWorkflow);
                scaleAbilityConfigs.add(scaleAbilityConfig);
            } catch (Exception e) {
                log.error("批次扩容发生异常", e);
                StageExpandErrorMessage stageExpandErrorMessage = new StageExpandErrorMessage();
                stageExpandErrorMessage.setStageName("构造扩容配置&预占用显存");
                stageExpandErrorMessage.setErrorMessage(e.getMessage());
                errorMessage.add(stageExpandErrorMessage);
                //跳出循环
                break;
            }
        }
        //需要调度到k8s
        if (CollectionUtil.isNotEmpty(scaleAbilityConfigs)) {
            List<OmInstallWorkflowTask> omInstallWorkflowTasks = omInstallWorkflow.buildForkTask("并行扩容Fork", "batchScale", MapUtil.of("scaleAbilitySize", scaleAbilityConfigs.size()),
                    OmWorkflowDataBuilderService.buildScaleForkInputData(scaleAbilityConfigs, gpuChooseConfig));
            //并行扩容
            List<CompletableFuture<K8sScalePodResult>> deploys = scaleAbilityConfigs.stream().map(abilityConfig -> CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                OmInstallWorkflowTask workflowSubTask = omInstallWorkflowTasks.stream()
                        .filter(t -> t.getTaskDefName().equals(abilityConfig.getAbilityDeploy().getServingDeployInfo().getServingDeploy().getK8sName())).findFirst().get();
                try {
                    if (StrUtil.isNotBlank(abilityConfig.getSlaveAddress())) {
                        //跨机房扩容
                        K8sScalePodResult k8sScalePodResult = multiClusterScale(abilityConfig, taskServerRouterEntity, gpuChooseConfig);
                        workflowSubTask.success();
                        return k8sScalePodResult;
                    } else {
                        //本集群扩容
                        K8sScalePodResult k8sScalePodResult = localClusterScale(abilityConfig, taskServerRouterEntity, gpuChooseConfig);
                        workflowSubTask.success();
                        return k8sScalePodResult;
                    }

                } catch (Exception e) {
                    log.error("batchExpandAbilityPod发生异常", e);
                    K8sScalePodResult k8sScalePodResult = new K8sScalePodResult();
                    k8sScalePodResult.setScaleErrorMessage(e.getMessage());
                    workflowSubTask.error(e.getMessage());
                    return k8sScalePodResult;
                }
            }), omDeleteThreadPool)).collect(Collectors.toList());
            //等待所有并行扩容任务结束，获取所有任务执行状态
            List<K8sScalePodResult> k8sScalePodResults = deploys.stream().map(CompletableFuture::join).collect(Collectors.toList());
            long count = k8sScalePodResults.stream().filter(K8sScalePodResult::isSuccess).count();
            List<String> collect = k8sScalePodResults.stream().filter(t -> !t.isSuccess()).map(K8sScalePodResult::getScaleErrorMessage).collect(Collectors.toList());
            OmInstallWorkflowTask workflowAtomHealthTask = omInstallWorkflow.buildJoinTask("批量扩容Join", "multiScaleRes", MapUtil.empty(),
                    omInstallWorkflowTasks.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));

            if (CollectionUtil.isNotEmpty(collect)) {
                StageExpandErrorMessage stageExpandErrorMessage = new StageExpandErrorMessage();
                stageExpandErrorMessage.setStageName("k8s部署阶段");
                String jsonString = JSON.toJSONString(collect);
                stageExpandErrorMessage.setErrorMessage(jsonString);
                workflowAtomHealthTask.error(jsonString);
                errorMessage.add(stageExpandErrorMessage);
            } else {
                workflowAtomHealthTask.success();
            }

            //设置本批次执行状态
            scaleBatchExpandResult.setSuccessNum((int) count);
            scaleBatchExpandResult.setExpandResult(count > 0);
            scaleBatchExpandResult.setErrorMessage(errorMessage);
            return scaleBatchExpandResult;
        } else {
            log.warn("当前批次扩容前置资源占用阶段失败");
            scaleBatchExpandResult.setExpandResult(false);
            scaleBatchExpandResult.setErrorMessage(errorMessage);
            return scaleBatchExpandResult;
        }

    }

    /**
     * 本地集群扩容
     *
     * @param scaleAbilityConfig
     */
    private K8sScalePodResult localClusterScale(ScaleAbilityConfig scaleAbilityConfig, TaskServerRouterEntity taskServerRouterEntity, GpuScaleChooseConfig gpuChooseConfig) {
        K8sScalePodResult k8sScalePodResult = new K8sScalePodResult();
        InstallPipelineContext installPipelineContext = buildScaleContext(scaleAbilityConfig);

        OmInstallWorkflow omInstallWorkflow = OmInstallWorkflow.buildOmInstallWorkflow(WorkflowTypeEnum.INSTALL, "localClusterScale", MapUtil.empty());
        installPipelineContext.setOmInstallWorkflow(omInstallWorkflow);
        //部署
        List<ContextHandler> list = Arrays.asList(virtualGpuDeployOperate, virtualGpuTakenOperate);
        boolean handle = true;
        for (ContextHandler contextHandler : list) {
            handle = contextHandler.handle(installPipelineContext);
            if (!handle) {
                log.warn("部署异常，准备清理数据");
                break;
            }
        }
        if (!handle) {
            k8sScalePodResult.setScaleErrorMessage(installPipelineContext.getExceptionMessage());
            exceptionHandler.cleanK8sResources(installPipelineContext);
        } else {
            try {
                boolean scaleDataBaseRes = scaleDataBaseOperate.handle(installPipelineContext, taskServerRouterEntity, gpuChooseConfig, scaleAbilityConfig.getAbilityDeploy().getScalePods());
                k8sScalePodResult.setSuccess(scaleDataBaseRes);
                if (!scaleDataBaseRes) {
                    k8sScalePodResult.setScaleErrorMessage(installPipelineContext.getExceptionMessage());
                }
            } catch (Exception e) {
                exceptionHandler.cleanK8sResources(installPipelineContext);
                log.error("localClusterScale数据库操作异常", e);
                k8sScalePodResult.setScaleErrorMessage("localClusterScale 数据库操作异常" + e.getMessage());
            }

        }

        return k8sScalePodResult;
    }

    /**
     * 构建扩容上下文
     *
     * @param scaleAbilityConfig
     * @return
     */
    private InstallPipelineContext buildScaleContext(ScaleAbilityConfig scaleAbilityConfig) {
        AbilityDeployDTO abilityDeploy = scaleAbilityConfig.getAbilityDeploy();

        InstallPipelineContext installPipelineContext = new InstallPipelineContext();
        installPipelineContext.setAbilityDeployInfo(abilityDeploy);
        installPipelineContext.setChooseGpuType(abilityDeploy.getOmChooseGpuType());
        installPipelineContext.setOmGpuInfo(abilityDeploy.getOmGpuInfo());
        installPipelineContext.setServingType(abilityDeploy.getServingType());
        installPipelineContext.setIsScale(true);
        installPipelineContext.setAtomOnly(scaleAbilityConfig.getAbilityDeploy().getAtomOnly());

        installPipelineContext.setDeployPattern(abilityDeploy.getDeployPattern());
        installPipelineContext.setAtomDeployOccupy(scaleAbilityConfig.getAtomDeployOccupyNeed());
        //设置
        PhysicalGpuDTO physicalGpuDTO = abilityDeploy.getPhysicalGpuDTO();
        if (physicalGpuDTO == null) {
            if (CollectionUtil.isNotEmpty(scaleAbilityConfig.getAtomDeployOccupyNeed())) {
                physicalGpuDTO = CollectionUtil.getFirst(scaleAbilityConfig.getAtomDeployOccupyNeed()).getPhysicalGpu();
            }
        }
        installPipelineContext.setPhysicalGpuDTO(physicalGpuDTO);
        return installPipelineContext;
    }

    /**
     * 跨机房扩容
     *
     * @param scaleAbilityConfig
     */
    private K8sScalePodResult multiClusterScale(ScaleAbilityConfig scaleAbilityConfig, TaskServerRouterEntity taskServerRouterEntity, GpuScaleChooseConfig gpuChooseConfig) {

        K8sScalePodResult k8sScalePodResult = new K8sScalePodResult();
        String slaveAddress = scaleAbilityConfig.getSlaveAddress();
        AbilityDeployDTO abilityDeploy = scaleAbilityConfig.getAbilityDeploy();

        //主从部署
        R<MasterDeployResult> deployMaster = masterScheduleConfig.getSlaveClient(slaveAddress, "").deploy(abilityDeploy);
        InstallPipelineContext installPipelineContext = buildScaleContext(scaleAbilityConfig);
        if (!BizResponseEnum.OK.getBizCode().equals(deployMaster.getCode())) {
            log.warn("远程部署异常：{}", deployMaster.getMessage());
            k8sScalePodResult.setScaleErrorMessage(deployMaster.getMessage());
            exceptionHandler.cleanK8sResources(installPipelineContext);
        } else {
            //数据库操作
            try {
                installPipelineContext.setDeploySlaveAddress(slaveAddress);
                MasterDeployResult masterDeployResult = deployMaster.getData();
                TaskServerRouterEntity taskServerRouterEntity1 = new TaskServerRouterEntity();
                taskServerRouterEntity1.setTaskServerUrl(masterDeployResult.getIngressUrl());
                installPipelineContext.setTaskServerRouter(taskServerRouterEntity1);
                PhysicalGpuDTO physicalGpuDTO = masterDeployResult.getPhysicalGpuDTO();
                physicalGpuDTO.setMachineGroup(slaveAddress);
                installPipelineContext.setPhysicalGpuDTO(physicalGpuDTO);
                List<String> scalePods = scaleAbilityConfig.getAbilityDeploy().getScalePods();
                installPipelineContext.setPodDeployNameInstalled(scalePods);
                if (StrUtil.isNotBlank(masterDeployResult.getServingConfigMap())) {
                    ArrayList<String> maps = new ArrayList<>();
                    maps.add(masterDeployResult.getServingConfigMap());
                    installPipelineContext.setConfigMapInstalled(maps);
                }
                installPipelineContext.setServingInfo(abilityDeploy.getServingDeployInfo().getServingDeploy());

                log.info("multiClusterScale构建数据库操作上下文：{}", JSON.toJSONString(installPipelineContext));
                TaskServerRouterEntity taskServerRouterEntityClone = abilityConvertMapper.cloneTaskServerRouter(taskServerRouterEntity);
                taskServerRouterEntityClone.setGpuInfo(masterDeployResult.getGpus());
                taskServerRouterEntityClone.setSlaveAddress(slaveAddress);
                List<GpuOperateItemDTO> collect = masterDeployResult.getGpuOperateItems().stream().filter(t -> scalePods.contains(t.getAbilityDeployName())).collect(Collectors.toList());
                taskServerRouterEntityClone.setGpuInfoDetail(JSON.toJSONString(collect));
                taskServerRouterEntityClone.setServingConfigMap(masterDeployResult.getServingConfigMap());

                boolean scaleDataBaseRes = scaleDataBaseOperate.handle(installPipelineContext, taskServerRouterEntityClone, gpuChooseConfig, scalePods);
                k8sScalePodResult.setSuccess(scaleDataBaseRes);
                if (!scaleDataBaseRes) {
                    k8sScalePodResult.setScaleErrorMessage(installPipelineContext.getExceptionMessage());
                }
            } catch (Exception e) {
                exceptionHandler.cleanK8sResources(installPipelineContext);
                log.error("multiClusterScale数据库操作异常", e);
                k8sScalePodResult.setScaleErrorMessage("multiClusterScale 数据库操作异常" + e.getMessage());

            }
        }
        return k8sScalePodResult;

    }

    /**
     * 构建算法部署配置&占用显存
     *
     * @param taskServerRouter
     * @param gpuChooseConfig
     * @return
     */
    private ScaleAbilityConfig buildAbilityDeployConfigAndOccupyGpu(TaskServerRouterEntity taskServerRouter, GpuScaleChooseConfig gpuChooseConfig, OmInstallWorkflow omInstallWorkflow) {

        ScaleAbilityConfig scaleAbilityConfig = new ScaleAbilityConfig();

        //构建算法部署配置
        AbilityDeployDTO abilityDeployConfig = buildAbilityDeployConfig(taskServerRouter);
        scaleAbilityConfig.setAbilityDeploy(abilityDeployConfig);
        //调度扩容节点并占用gpu资源
        MultiClusterAtomGpuApplyResult atomGpuApply = occupyScalePodGpu(abilityDeployConfig, gpuChooseConfig, omInstallWorkflow);
        scaleAbilityConfig.setAtomDeployOccupyNeed(atomGpuApply.getAtomDeployOccupyResult());
        if (StrUtil.isNotBlank(atomGpuApply.getSlaveAddress())) {
            scaleAbilityConfig.setSlaveAddress(atomGpuApply.getSlaveAddress());
        }
        return scaleAbilityConfig;
    }

    /**
     * 调度扩容节点并占用gpu资源
     *
     * @param abilityDeployConfig
     * @param gpuChooseConfig
     */
    private MultiClusterAtomGpuApplyResult occupyScalePodGpu(AbilityDeployDTO abilityDeployConfig, GpuScaleChooseConfig gpuChooseConfig, OmInstallWorkflow omInstallWorkflow) {
        abilityDeployConfig.setIsScale(true);
        gpuChooseConfig.setChooseGpuType(abilityDeployConfig.getOmChooseGpuType());

        if (StrUtil.isNotBlank(abilityDeployConfig.getSlaveIp())) {
            //跨机房
            //todo
            MultiClusterAtomGpuApplyResult multiClusterAtomGpuApplyResult = multiClusterGpuPreTokenOperate.handleGpuPreToken(abilityDeployConfig, gpuChooseConfig, omInstallWorkflow);
            if (StrUtil.isBlank(multiClusterAtomGpuApplyResult.getSlaveAddress())) {
                throw new AiAutomatedException("跨机房扩容占用资源失败");
            }
            return multiClusterAtomGpuApplyResult;
        } else {
            //单集群
            MultiClusterAtomGpuApplyResult multiClusterAtomGpuApplyResult = new MultiClusterAtomGpuApplyResult();
            //todo
            multiClusterAtomGpuApplyResult.setAtomDeployOccupyResult(gpuPreTokenOperate.occupyGpu(abilityDeployConfig.getAtomDeployInfo(), gpuChooseConfig, omInstallWorkflow));
            return multiClusterAtomGpuApplyResult;
        }
    }


    /**
     * 构建算法部署配置
     *
     * @param taskServerRouter
     * @return
     */
    private AbilityDeployDTO buildAbilityDeployConfig(TaskServerRouterEntity taskServerRouter) {
        String servingTime = DateUtil.format(LocalDateTime.now(), CommonConstant.DEPLOY_DATA);
        AbilityDeployDTO abilityDeploy = JSON.parseObject(taskServerRouter.getDeployInfo(), AbilityDeployDTO.class);
        List<AtomDeployDTO> atomDeployInfo = abilityDeploy.getAtomDeployInfo();
        List<String> list = JSONArray.parseArray(taskServerRouter.getSlavePods(), String.class);

        List<String> podsShouldDeploy = new ArrayList<>();
        //atom处理
        if (CollectionUtil.isNotEmpty(atomDeployInfo)) {
            int j = 0;
            for (AtomDeployDTO a : atomDeployInfo) {
                //九头蛇
                if (!list.contains(a.getAtomInfo().getK8sName())) {
                    a.setNonRequiredInstall(true);
                    a.getAtomInfo().setPhysicalGpuDTO(null);
                    continue;
                }
                a.setChooseGpuType(abilityDeploy.getOmChooseGpuType());
                AtomInfo atomInfo = a.getAtomInfo();
                String k8sName = atomInfo.getK8sName();
                //部署名称重置
                String newName = StrUtil.subBefore(k8sName, "-", true) + "-" + servingTime + j;
                atomInfo.setK8sName(newName);
                atomInfo.setDeployName(newName);
                if (taskServerRouter.getModelId() == null || !taskServerRouter.getDeployPattern().equals(DeployPatternEnum.SINGLE_ATOM.getPattern())) {
                    //非九头蛇
                    atomInfo.setContainerName(null);
                }
                //重置configMap
                if (StrUtil.isNotBlank(a.getConfigMapName()) && StrUtil.isNotBlank(a.getConfigMapValue())) {
                    a.setConfigMapName(UUID.randomUUID().toString().replaceAll("-", ""));
                }
                podsShouldDeploy.add(newName);
                j++;
            }
        }
        //serving处理
        ServingDeployDTO servingDeployInfo = abilityDeploy.getServingDeployInfo();
        ServingInfo servingInfo = servingDeployInfo.getServingDeploy();
        //只有atom无Serving
        boolean atomOnly = false;
        ServingConfigVo servingConfig = servingDeployInfo.getServingConfig();
        atomOnly = servingConfig.getServing_atom() != null && !servingConfig.getServing_atom();
        if (list.contains(servingInfo.getK8sName()) && !atomOnly) {
            //serving处理
            String k8sName = StrUtil.subBefore(servingInfo.getK8sName(), "-", true) + "-" + servingTime;
            servingInfo.setK8sName(k8sName);
            podsShouldDeploy.add(k8sName);
        } else {
            log.info("不包含:{}直接跳过", servingInfo.getK8sName());
        }
        //重置configMap
        String configMapName = UUID.randomUUID().toString().replaceAll("-", "");
        servingInfo.setServingConfigMap(configMapName);
        servingDeployInfo.setConfigMapName(configMapName);
        abilityDeploy.setDeployPattern(taskServerRouter.getDeployPattern());
        abilityDeploy.setScalePods(podsShouldDeploy);
        abilityDeploy.setAtomOnly(atomOnly);
        abilityDeploy.setIsRelocate(false);
        abilityDeploy.setRelocateNoHeader(false);
        return abilityDeploy;
    }


    /**
     * 并发缩容
     *
     * @param taskServerRouterEntity
     * @param needNum
     */
    public void concurrentShrinkAbilityPod(String slaveAddress, TaskServerRouterEntity taskServerRouterEntity, AbilityNodeInfoEntity abilityNodeInfoEntity, Boolean checkTraffic, ScaleHistoryEntity scaleHistoryEntity, Integer needNum) {
        Long abilityId = taskServerRouterEntity.getAbilityId();

        //是否检验算法是否堵塞
        if (BooleanUtil.isTrue(checkTraffic)) {
            //判断是否阻塞
            Integer queueTrafficInfo = taskQueueInfoPrcService.getQueueTrafficInfo(abilityId);
            if (queueTrafficInfo < abilityTrafficNum) {
                // 需要缩容
                Integer shrinkNum = shrinkScale(abilityId, slaveAddress, Math.abs(needNum));
                log.info("{},实际缩容成功：{}", slaveAddress, shrinkNum);
                scaleHistoryEntity.setActualNodeCount(shrinkNum);
            } else {
                log.info("实际缩容暂停，因为任务处于阻塞状态：{},算法:{}", queueTrafficInfo, abilityId);
                scaleHistoryEntity.setDetail("实际缩容暂停，因为任务处于阻塞状态");
                scaleHistoryEntity.setActualNodeCount(0);
            }
        } else {
            // 需要缩容
            Integer shrinkNum = 0;
            //更新状态
            if (abilityNodeInfoEntity != null) {
                shrinkNum = shrinkScaleNode(taskServerRouterEntity, Math.abs(needNum), abilityNodeInfoEntity);
                abilityNodeInfoEntity.setStatus(AbilityNodeStatusEnum.SUCCESS.getStatus());
                abilityNodeInfoService.updateById(abilityNodeInfoEntity);
            } else {
                shrinkNum = shrinkScale(abilityId, slaveAddress, Math.abs(needNum));
            }
            log.info("{},实际缩容成功：{}", slaveAddress, shrinkNum);
            scaleHistoryEntity.setActualNodeCount(shrinkNum);
        }
        scaleHistoryEntity.setUpdateTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
        //此次扩容阶段结束处理scaleHistoryEntity
        scaleHistoryService.updateById(scaleHistoryEntity);
//        //清除缓存
//        clearCache(taskServerRouterEntity);
    }

    public void scaleToTargetNodeCount(String abilityId, Integer needNodeCount, Long historyId) {
        TaskServerRouterEntity taskServerRouterEntity = taskServerRouterService.getInstallRouteByAbilityId(abilityId);
        if (Objects.isNull(taskServerRouterEntity)) {
            log.info("abilityId:{} taskServerRouterEntity is null", abilityId);
            //说明不是手动扩容的
            return;
        }
        String key = IScaleService.abilityScaleKey + abilityId;
        RLock abilityLock = redissonClient.getLock(key);
        try {
            if (abilityLock.tryLock(10, 300, TimeUnit.SECONDS)) {
                log.debug("{}, start expandAndShrink", key);
                ScaleHistoryEntity scaleHistoryEntity = scaleHistoryService.getById(historyId);
                this.expandAndShrink(taskServerRouterEntity, needNodeCount, scaleHistoryEntity);
            } else {
                log.warn("{},正在扩容中,直接退出", key);
            }
        } catch (Exception e) {
            log.error("扩容出现异常");
        } finally {
            if (abilityLock.isLocked() && abilityLock.isHeldByCurrentThread()) {
                abilityLock.unlock();
            }
        }
    }

    @Async
    public CompletableFuture<Void> scaleToTargetNodeCountAsync(String abilityId, int needNocdCount, Long historyId) {
        QueryWrapper<ScaleHistoryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScaleHistoryEntity::getId, historyId);
        ScaleHistoryEntity scaleHistoryEntity = scaleHistoryService.getById(historyId);
        scaleHistoryEntity.setStatus(ScalingStatusEnum.IN_PROGRESS.getStatus());
        scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
        scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        try {
            scaleToTargetNodeCount(abilityId, needNocdCount, historyId);
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            List<TaskServerRouterEntity> copyRouters = this.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            if (scaleHistoryEntity.getActualNodeCount() < scaleHistoryEntity.getExpectedNodeCount()) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            } else {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
            }
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        } catch (Exception e) {
            log.error("abilityId: {}, needNocdCount: {}, historyId: {} scaleToTargetNodeCount error", abilityId, needNocdCount, historyId, e);
            scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            scaleHistoryEntity.setDetail(getStackTrace(e));
            List<TaskServerRouterEntity> copyRouters = this.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        }
        return CompletableFuture.completedFuture(null);
    }

    public void scaleBasedOnQPS(String omAppId, Integer requestQps, Long historyId) {
        TaskServerRouterEntity taskServerRouterEntity = taskServerRouterService.getInstallRouteByAbilityId(omAppId);
        Long abilityId = taskServerRouterEntity.getAbilityId();
        if (checkIsHydra(taskServerRouterEntity)) {
            //九头蛇模式不支持自动扩所容
            log.debug("abilityId:{} 九头蛇模式不支持自动扩容，跳过", abilityId);
            servingRouterRpcService.flushAbilityTrafficMetric(abilityId);
            return;
        }
        //获取每分钟能力可处理请求书(单个node)
        Integer canCallPerMinute = servingRouterRpcService.getSingleAbilityConcurrent(null, abilityId);
        Double abilityPerFps = servingRouterRpcService.getAbilityPerFps(abilityId.toString());
        Double fps = abilityPerFps == null ? taskServerRouterEntity.getFps() : abilityPerFps;
        canCallPerMinute = NumberUtil.mul(canCallPerMinute, 60, NumberUtil.div(1000D, fps.doubleValue())).intValue();
        log.info("{},单个节点canCallPerTime {},算法平均处理时间：{}", abilityId, canCallPerMinute, fps);
        String key = IScaleService.abilityScaleKey + abilityId;
        RLock abilityLock = redissonClient.getLock(key);
        Integer requestPerTime = requestQps * 60;
        try {
            if (abilityLock.tryLock(10, 300, TimeUnit.SECONDS)) {
                log.debug("{},开始进入扩容", key);
                //扩缩容逻辑判断
                int targetNum = AbilityAutomatedRpcServiceImpl.calculateNodeCount(canCallPerMinute, requestPerTime);
                ScaleHistoryEntity scaleHistoryEntity = scaleHistoryService.getById(historyId);
                List<TaskServerRouterEntity> copyRouters = this.getCopyRouters(Long.valueOf(abilityId));
                int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
                currentNodeCount = currentNodeCount + 1;
                int expandAndShrinkNum = targetNum - currentNodeCount;
                this.expandAndShrink(taskServerRouterEntity, expandAndShrinkNum, scaleHistoryEntity);
            } else {
                log.warn("{},正在扩容中,请稍后再试", key);
            }
        } catch (Exception e) {
            log.error("{} scheduledExpandAndShrink error", key, e);
        } finally {
            if (abilityLock.isLocked() && abilityLock.isHeldByCurrentThread()) {
                abilityLock.unlock();
            }
        }
        //最近一分钟的能力请求数重置
        servingRouterRpcService.flushAbilityTrafficMetric(abilityId);
    }


}
