package com.vos.task.automated.service.service.impl.ai.install;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.enums.InstallActionEnum;
import com.vos.kernel.common.enums.TaskResponseEnum;
import com.vos.kernel.common.exception.LhException;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.om.AtomConfigModelVo;
import com.vos.task.automated.api.model.dto.om.AtomConfigVo;
import com.vos.task.automated.api.model.dto.om.ImageInfo;
import com.vos.task.automated.api.model.entity.AtomInfo;
import com.vos.task.automated.api.model.entity.ModelInfo;
import com.vos.task.automated.service.common.ServingTypeEnum;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.service.IAbilityOperateService;
import com.vos.task.automated.service.service.ModelService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.manage.api.model.entity.TaskApp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/24
 * @description: com.hzlh.task.automated.service.impl.ai.install
 */
@Slf4j
@Service
public class BaseDeployOperate {

    @Resource
    NacosConfigManager nacosConfigManager;

    @Resource
    KubernetesConfigProperties kubernetesConfigProperties;

    @Resource
    ModelService modelService;

    /**
     * 健康检查时长
     */
    @Value("${deploy.deployHealthDuration:60}")
    private Long deployHealthDuration;
    @Resource
    private IAbilityOperateService abilityOperateService;

    /**
     * 构造基础模型文件信息
     *
     * @param taskApp
     * @param modelGroupId
     * @param modelId
     * @param atomConfigModelVo
     * @return
     */
    public ModelInfo buildModelInfo(TaskApp taskApp, String modelGroupId, String modelId, AtomConfigModelVo atomConfigModelVo) {
        ModelInfo modelInfo = new ModelInfo();
        modelInfo.setTenantId(taskApp.getTenantId());
        modelInfo.setAppId(taskApp.getAppId());
        modelInfo.setGroupId(modelGroupId);
        modelInfo.setModelName(modelId);
        modelInfo.setMd5(atomConfigModelVo.getMd5());
        modelInfo.setCudaAddon(atomConfigModelVo.getCuda_addon());
        modelInfo.setCudaInit(atomConfigModelVo.getCuda_init());
        modelInfo.setAction(InstallActionEnum.INSTALL.getValue());
        modelInfo.setMessage(JSON.toJSONString(atomConfigModelVo));
        return modelInfo;
    }


    /**
     * 获取nacos中额外配置
     *
     * @return
     */
    public JSONObject getNacosConfig(ConfigService configService) {
        String dataId = "task-automated-extra-config.yml";
        String group = "DEFAULT_GROUP";
        try {
            String config = configService.getConfig(dataId, group, 3000);
            return JSONObject.parseObject(config);
        } catch (NacosException e) {
            return null;
        }
    }

    /**
     * 构造基础atom信息
     *
     * @param taskApp
     * @param atomKey
     * @param atomConfigVo
     * @return
     */
    public AtomInfo buildSingleAtom(TaskApp taskApp, String atomKey, AtomConfigVo atomConfigVo) {

        String atomImage = atomConfigVo.getImage();
        //解析镜像信息
        ImageInfo atomImageInfo = parseImageStr(atomImage);
        String atomImageName = atomImageInfo.getImageName();
        String atomImageVersion = atomImageInfo.getImageVersion();
        String atomTime = DateUtil.format(LocalDateTime.now(), CommonConstant.DEPLOY_DATA);

        //构造atom信息
        AtomInfo atomInfo = new AtomInfo();
        atomInfo.setAtomKey(atomKey);
        atomInfo.setTenantId(taskApp.getTenantId());
        atomInfo.setAppId(taskApp.getAppId());
        atomInfo.setAtomName(atomKey + "-" + atomImageName + "-" + atomImageVersion);
        String deployName = atomImageName + "-" + atomTime;
        atomInfo.setDeployName(deployName);
        atomInfo.setK8sName(deployName);
        atomInfo.setContainerName(deployName);
        atomInfo.setAction(InstallActionEnum.INSTALL.getValue());
        atomInfo.setDuplicateNum(0);
        atomInfo.setFps(atomConfigVo.getSpeed().toString());
        atomInfo.setChangeComPower(0);
        atomInfo.setVersion(atomImageVersion);
        atomInfo.setMessage(JSON.toJSONString(atomConfigVo));
        atomInfo.setImage(atomImage);
        return atomInfo;
    }


    /**
     * 根据镜像地址获取镜像名称及版本名称
     */
    public ImageInfo parseImageStr(String image) {
        try {
            String hostname = image.substring(0, image.indexOf("/"));
            String name = image.substring((image.indexOf("/") + 1), image.lastIndexOf(":"));
            name = name.replaceAll("/", "-").replaceAll("_", "-");
            String version = image.substring(image.lastIndexOf(":") + 1);

            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setHostName(hostname);
            imageInfo.setImageName(name);
            imageInfo.setImageVersion(version);
            log.info("获取镜像信息成功,image={},解析参数={}", image, JSON.toJSONString(imageInfo));
            return imageInfo;
        } catch (Exception e) {
            log.error("获取镜像信息失败,image={},异常信息={}", image, e.getMessage());
            throw new LhException(TaskResponseEnum.APP_PARAM_ERROR);
        }
    }

    /**
     * 截取获取部署名称：不能大于63字符
     *
     * @param name
     * @return
     */
    public String getShortName(String name) {
        if (StrUtil.length(name) > 50) {
            name = StrUtil.subWithLength(name, StrUtil.length(name) - 50, 50);
        }
        if (name.startsWith("-")) {
            name = StrUtil.sub(name, 1, StrUtil.length(name));
        }
        char firstChar = name.charAt(0);
        if (Character.isDigit(firstChar)) {
            name = "c" + name;
        }
        return name;
    }

    /**
     * serving 环境变量解析
     * 1、nacos中获取 2、拼装atom请求地址
     *
     * @param context
     * @param atomKey
     * @param modelId
     */
    public void buildServingEnv(InstallPipelineContext context, String atomKey, String modelId) {
        List<String> envs = context.getOmInstallInfo().getEnvs();
        //serving调用atom地址环境变量设置
        List<String> envsBak = new ArrayList<>();
        ConfigService configService = nacosConfigManager.getConfigService();
        JSONObject nacosConfig = getNacosConfig(configService);
        for (Iterator<String> iterator = envs.iterator(); iterator.hasNext(); ) {
            String env = iterator.next();
            if (!env.contains("--") && null != nacosConfig) {
                //直接到配置中心中获取数据
                String configString = nacosConfig.getString(env);
                if (StrUtil.isNotBlank(configString)) {
                    envsBak.add(env + "=" + configString);
                }
            }
            String[] split = env.split("--");
            if (atomKey.equals(split[0]) && modelId.equals(split[1])) {
                envsBak.add(env + "=" + atomKey);
                iterator.remove();
            }
        }
        List<String> envsBakInfo = context.getOmInstallInfo().getEnvsBak();
        if (CollectionUtil.isEmpty(envsBakInfo)) {
            envsBakInfo = envsBak;
        } else {
            envsBakInfo.addAll(envsBak);
            envsBakInfo = envsBakInfo.stream().distinct().collect(Collectors.toList());
        }
        context.getOmInstallInfo().setEnvsBak(envsBakInfo);
    }

    /**
     * 处理serving环境变量
     *
     * @param context
     */
    public void handleServingEnv(InstallPipelineContext context) {
        List<String> envs = context.getOmInstallInfo().getEnvs();
        //serving调用atom地址环境变量设置
        List<String> envsBak = new ArrayList<>();
        ConfigService configService = nacosConfigManager.getConfigService();
        JSONObject nacosConfig = getNacosConfig(configService);
        for (Iterator<String> iterator = envs.iterator(); iterator.hasNext(); ) {
            String env = iterator.next();
            if (!env.contains("--") && null != nacosConfig) {
                //直接到配置中心中获取数据
                String configString = nacosConfig.getString(env);
                if (StrUtil.isNotBlank(configString)) {
                    envsBak.add(env + "=" + configString);
                }
            }
        }
        context.getOmInstallInfo().setEnvsBak(envsBak);

    }

    /**
     * 获取已经在用的模型及对应的md5值
     *
     * @param modelNames
     * @return
     */
    private HashMap<String, String> getModelMd5(Set<String> modelNames) {
        HashMap<String, String> modelMd5 = new HashMap<>();
        if (kubernetesConfigProperties.isAtomModelMd5Check() && CollectionUtil.isNotEmpty(modelNames)) {
            //获取已经在用的模型及对应的md5值
            try {
                QueryWrapper<ModelInfo> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().select(ModelInfo::getModelName, ModelInfo::getMd5).eq(ModelInfo::getIsDeleted, 0).isNotNull(ModelInfo::getMd5).in(ModelInfo::getModelName, modelNames);
                List<ModelInfo> list = modelService.list(queryWrapper);
                if (CollectionUtil.isNotEmpty(list)) {
                    for (ModelInfo m : list) {
                        modelMd5.put(m.getModelName(), m.getMd5());
                    }
                }

            } catch (Exception e) {
                log.warn("获取模型对应的md5值失败", e);
            }
        }
        return modelMd5;
    }

    /**
     * 计算atom启动所需资源；及serving访问atom地址环境变量设置
     *
     * @param atomKey
     * @param atomConfigVo
     * @param atomInfo
     * @param context
     * @return
     */
    public Integer atomResourceCalculate(String atomKey, AtomConfigVo atomConfigVo, AtomInfo atomInfo, InstallPipelineContext context) {

        TaskApp taskApp = context.getTaskApp();
        JSONObject model = atomConfigVo.getModel();
        // 新增消耗的算力
        Integer initNum = 0;
        String modelGroupId = UUID.randomUUID().toString().replaceAll("-", "");
        List<ModelInfo> modelInfoList = new ArrayList<>();
        //是否为九头蛇模式
        boolean isHydra = ServingTypeEnum.HYDRA.name().equals(context.getServingType());
        HashMap<String, String> modelMd5 = getModelMd5(model.keySet());
        int numGpus = 0;
        //一个atom可配置多个model
        for (String modelId : model.keySet()) {
            AtomConfigModelVo atomConfigModelVo = model.getObject(modelId, AtomConfigModelVo.class);
            if (kubernetesConfigProperties.isAtomModelMd5Check() && MapUtil.isNotEmpty(modelMd5) && StrUtil.isNotBlank(atomConfigModelVo.getMd5()) && modelMd5.containsKey(modelId)) {
                String old = modelMd5.get(modelId);
                if (!atomConfigModelVo.getMd5().equals(old)) {
                    throw new AiAutomatedException("模型md5值不匹配；原值：" + old + ",现值：" + atomConfigModelVo.getMd5());
                }
            }
            //模型信息
            ModelInfo modelInfo = buildModelInfo(taskApp, modelGroupId, modelId, atomConfigModelVo);
            if (initNum == 0) {
                //atom首次加载模型所需资源
                Integer cudaInit = atomConfigModelVo.getCuda_init();
                //cuda为0，需要增加上base占用的gpu
                if (cudaInit == 0 && atomConfigModelVo.getBase_cuda_init() != null) {
                    cudaInit = atomConfigModelVo.getBase_cuda_init();
                }
                initNum += cudaInit;
            } else {
                //atom非首次加载模型所需资源
                initNum += atomConfigModelVo.getCuda_addon();
            }
            modelInfo.setAtomName(atomInfo.getAtomName());
            modelInfo.setRequestUrl(atomKey);
            modelInfoList.add(modelInfo);
            // 构造serving环境变量
            buildServingEnv(context, atomKey, modelId);
            //独占显卡显示
            if (atomConfigModelVo.getNum_gpus() != null && atomConfigModelVo.getNum_gpus() > 0) {
                numGpus = numGpus + atomConfigModelVo.getNum_gpus();
            }
        }
        //atom 所需资源
        atomInfo.setResourceSituation(String.valueOf(initNum));
        atomInfo.setNumGpus(numGpus);

        context.getModelInfoEntityList().addAll(modelInfoList);
        return initNum;
    }

    /**
     * atom健康检查
     *
     * @param podName
     */
    public void healthPodCheck(String podName, LocalDateTime deployTime) {

        LocalDateTime now = LocalDateTime.now();
        long between = LocalDateTimeUtil.between(deployTime, now, ChronoUnit.SECONDS);
        if (between > deployHealthDuration) {
            //获取具体的部署错误
            String deployMessage = abilityOperateService.podDeployMessage(podName);
            throw new AiAutomatedException(StrUtil.isBlank(deployMessage) ? podName + "部署失败，时长：" + deployHealthDuration : deployMessage);
        }
        Boolean podActive = abilityOperateService.podActive(podName);
        if (BooleanUtil.isFalse(podActive)) {
            ThreadUtil.safeSleep(1000);
            healthPodCheck(podName, deployTime);
        }
    }
}
