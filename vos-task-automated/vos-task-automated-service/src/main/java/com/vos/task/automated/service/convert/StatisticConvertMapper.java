package com.vos.task.automated.service.convert;

import com.vos.task.automated.api.model.dto.AbilityPodGpuInfoDTO;
import com.vos.task.automated.api.model.dto.AbilityResourceItemDTO;
import com.vos.task.automated.api.model.dto.AlarmMessageDTO;
import com.vos.task.automated.api.model.dto.RouterStatisticDTO;
import com.vos.task.automated.api.model.dto.k8s.PodInfoDTO;
import com.vos.task.automated.api.model.entity.AlarmsEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/18
 * @description: com.vos.task.automated.service.convert
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface StatisticConvertMapper {

    /**
     * pod信息转换
     *
     * @param pod
     * @return
     */
    AbilityPodGpuInfoDTO podConvert(PodInfoDTO pod);

    /**
     * 数组
     *
     * @param pods
     * @return
     */
    List<AbilityPodGpuInfoDTO> podConvert(List<PodInfoDTO> pods);


    /**
     * 类型转换
     *
     * @param dto
     * @return
     */
    @Mapping(target = "scaleNum", source = "count")
    @Mapping(target = "returnTime", source = "fps")
    AbilityResourceItemDTO routerStatisticConvert(RouterStatisticDTO dto);

    @Mapping(target = "alarmTime", source = "updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    AlarmMessageDTO alarmConvert(AlarmsEntity alarmsEntity);

    List<AlarmMessageDTO> alarmConvert(List<AlarmsEntity> alarmsEntity);
}
