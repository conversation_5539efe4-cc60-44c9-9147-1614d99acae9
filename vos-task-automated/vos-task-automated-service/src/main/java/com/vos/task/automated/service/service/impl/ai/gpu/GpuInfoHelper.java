package com.vos.task.automated.service.service.impl.ai.gpu;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.entity.R;
import com.vos.kernel.common.entity.TenantGpuDTO;
import com.vos.task.automated.api.MasterGpuDTO;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.AbilityDeployDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuHostItemDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.dto.k8s.PodInfoDTO;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.GpuEnvEnums;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.mapper.TaskServerRouterMapper;
import com.vos.task.automated.service.service.IAbilityOperateService;
import com.vos.task.automated.service.service.impl.ai.master.IEmbedService;
import com.vos.task.automated.service.service.impl.ai.master.MasterScheduleConfig;
import com.vos.task.automated.service.service.impl.ai.master.MasterVirtualGpuDeployOperate;
import com.vos.task.automated.service.service.impl.ai.master.RoleProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.vos.task.automated.service.common.Constants.HUAWEI_GPU_TYPE;

/**
 * 该类的定义说明、使用范围
 *
 * <AUTHOR>
 * @version 对应产品版本号
 * @since 2023/7/21 17:11
 */
@Component
@Slf4j
public class GpuInfoHelper implements CommandLineRunner {
    @Resource
    IAbilityOperateService abilityOperateService;
    @Resource
    KubernetesConfigProperties kubernetesConfigProperties;
    /**
     * 节点下显存剩余最大占比
     */
    @Value("${deploy.residual-video-memory:0.8}")
    private Double residualVideoMemory;

    /**
     * 华为占用是否以数据库记录为准
     */
    @Value("${deploy.useToken:true}")
    private Boolean useToken;

    @Resource
    RoleProperties roleProperties;

    @Resource
    MasterVirtualGpuDeployOperate masterVirtualGpuDeployOperate;

    @Resource
    TaskServerRouterMapper taskServerRouterMapper;

    @Resource
    MasterScheduleConfig masterScheduleConfig;

    /**
     * 显卡数量缓存
     */
    private Integer gpuNums = 0;

    public Integer getGpuNums() {
        return gpuNums;
    }

    public TenantGpuDTO getGpuDetailInfo() {
        TenantGpuDTO tenantGpuDTO = new TenantGpuDTO();
        try {
            List<GpuHostItemDTO> gpuInfo;
            if (BooleanUtil.isTrue(roleProperties.getIsMaster())) {
                gpuInfo = masterVirtualGpuDeployOperate.getGpuInfo(false);
            } else {
                gpuInfo = abilityOperateService.getPhysicalGpuInfo(false);
            }
            if (CollectionUtil.isEmpty(gpuInfo)) {
                throw new AiAutomatedException("gpu 获取失败");
            }
            this.gpuNums = gpuInfo.stream().map(s -> s.getDetail().size()).mapToInt(t -> t).sum();
            tenantGpuDTO.setGpuNum(gpuNums);
            tenantGpuDTO.setGpuTotal(gpuInfo.stream().map(GpuHostItemDTO::getDetail).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).map(t -> String.valueOf(NumberUtil.add(t.getUsedMem(), t.getFreeMem()).intValue())).mapToInt(Integer::parseInt).sum());
            tenantGpuDTO.setGpuUsed(gpuInfo.stream().map(GpuHostItemDTO::getDetail).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).map(GpuItemDTO::getUsedMem).mapToInt(Integer::parseInt).sum());
        } catch (Exception e) {
        }

        return tenantGpuDTO;
    }

    /**
     * 是否为华为卡部署
     *
     * @param chooseGpuType
     * @return
     */
    private Boolean isHuawei(String chooseGpuType) {
        if (StrUtil.isBlank(chooseGpuType)) {
            return false;
        } else {
            return chooseGpuType.contains(HUAWEI_GPU_TYPE);
        }
    }

    /**
     * 选择物理gpu资源
     *
     * @param chooseGpuType
     * @param isScale
     * @return
     */
    public PhysicalGpuDTO choosePhysicalGpu(String chooseGpuType, Boolean isScale, String applyResource) {
        PhysicalGpuDTO physicalGpuDTO = null;
        //是否华为卡部署
        Boolean huawei = isHuawei(chooseGpuType);

        List<PhysicalGpuDTO> list = getPhysicalGpuList(huawei, null, null);
        if (StringUtils.isNotBlank(chooseGpuType)) {
            Map<String, String> finalGpuMap = getFinalGupType(chooseGpuType);
            log.info("查询gpu,isScale:{},om包解析的gpu是:{},所拥有的gpu列表：{}，映射关系：{}", isScale, chooseGpuType,
                    JSON.toJSONString(list.stream().map(PhysicalGpuDTO::getGpuType).collect(Collectors.toList())), JSON.toJSONString(finalGpuMap));

            if (list.stream().noneMatch(c -> StringUtils.isNotBlank(finalGpuMap.get(c.getGpuType())))) {
                throw new AiAutomatedException(chooseGpuType + "显卡资源未找到");
            }
            list = list.stream().filter(c -> StringUtils.isNotBlank(finalGpuMap.get(c.getGpuType()))).collect(Collectors.toList());
            if (huawei) {
                if (applyResource == null) {
                    throw new AiAutomatedException(chooseGpuType + "显卡资源申请资源为空");
                }
                //资源充足的
                List<PhysicalGpuDTO> collect = list.stream().filter(t -> NumberUtil.sub(t.getFreeMem(), applyResource).intValue() > 0).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    throw new AiAutomatedException(chooseGpuType + "显卡资源申请资源为空");
                }
                physicalGpuDTO = list.stream().min(Comparator.comparingLong(c -> Long.parseLong(c.getFreeMem()))).get();
            } else {
                //选择对应的资源最多的gpu卡槽
                physicalGpuDTO = list.stream().max(Comparator.comparingLong(c -> Long.parseLong(c.getFreeMem()))).get();
            }
        } else {
            physicalGpuDTO = list.stream().max(Comparator.comparingLong(c -> Long.parseLong(c.getFreeMem()))).get();
        }
        log.info("gpu选择成功:{},设备号:{},ip:{}", physicalGpuDTO.getGpuType(), physicalGpuDTO.getHost(), physicalGpuDTO.getGpuId());

        return physicalGpuDTO;
    }


    /**
     * 扩容时提前获取可部署的物理卡
     *
     * @param chooseGpuType
     * @param atomGpuApply
     * @return
     */
    public PhysicalGpuDTO choosePhysicalGpuHuawei(String chooseGpuType, List<AtomGpuApplyDTO> atomGpuApply) {
        List<PhysicalGpuDTO> physicalGpuDTOS = choosePhysicalGpuHuawei(chooseGpuType, true, null, atomGpuApply, null);

        return CollectionUtil.getFirst(physicalGpuDTOS);
    }


    /**
     * 选择物理gpu资源: 部署在同一个物理gpu卡上
     *
     * @param chooseGpuType 部署需要卡类型
     * @param isScale       是否扩容
     * @param physicalGpuId 物理GpuId
     * @param atomGpuApply  atom需要的资源信息
     * @return
     */
    public List<PhysicalGpuDTO> choosePhysicalGpuHuawei(String chooseGpuType, Boolean isScale, PhysicalGpuDTO physicalGpuId,
                                                        List<AtomGpuApplyDTO> atomGpuApply, List<String> tokenGpusForHuawei) {

        if (CollectionUtil.isEmpty(atomGpuApply)) {
            return new ArrayList<>();
        }
        //是否华为卡部署
        List<PhysicalGpuDTO> list = getPhysicalGpuList(true, physicalGpuId, tokenGpusForHuawei);
        if (CollectionUtil.isEmpty(list)) {
            throw new AiAutomatedException(chooseGpuType + "显卡资源未找到");
        }
        //gpu类型选择
        if (StringUtils.isNotBlank(chooseGpuType)) {
            Map<String, String> finalGpuMap = getFinalGupType(chooseGpuType);
            log.info("查询gpu,isScale:{},om包解析的gpu是:{},所拥有的gpu列表：{}，映射关系：{}", isScale, chooseGpuType,
                    JSON.toJSONString(list.stream().map(PhysicalGpuDTO::getGpuType).collect(Collectors.toList())), JSON.toJSONString(finalGpuMap));
            list = list.stream().filter(c -> StringUtils.isNotBlank(finalGpuMap.get(c.getGpuType()))).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new AiAutomatedException(chooseGpuType + "显卡资源未找到");
        }
        //排序选出最优解:从小到大
        atomGpuApply.sort(Comparator.comparing(AtomGpuApplyDTO::getNeedResource));
        list.sort(Comparator.comparing(PhysicalGpuDTO::getFreeMem));

        //需要在同一个物理gpu上
        Map<String, List<PhysicalGpuDTO>> collect = list.stream().collect(Collectors.groupingBy(PhysicalGpuDTO::getPhysicalGpuId));
        List<PhysicalGpuDTO> physicalGpusFinale = new ArrayList<>();
        for (Map.Entry<String, List<PhysicalGpuDTO>> m : collect.entrySet()) {
            List<PhysicalGpuDTO> value = m.getValue();
            //单host不满足要求
            if (value.size() < atomGpuApply.size()) {
                continue;
            }

            List<PhysicalGpuDTO> physicalGpus = new ArrayList<>();
            List<String> selectedId = new ArrayList<>();
            for (AtomGpuApplyDTO a : atomGpuApply) {
                Optional<PhysicalGpuDTO> first = value.stream().filter(t -> NumberUtil.sub(t.getFreeMem(), a.getNeedResource().toString()).intValue() > 0)
                        .filter(t -> !selectedId.contains(t.getUuid()))
                        .findFirst();
                if (first.isPresent()) {
                    PhysicalGpuDTO physicalGpuDTO = first.get();
                    physicalGpuDTO.setAtomK8sName(a.getAtomK8sName());
                    physicalGpus.add(physicalGpuDTO);
                    selectedId.add(physicalGpuDTO.getUuid());
                }
            }
            if (physicalGpus.size() == atomGpuApply.size()) {
                physicalGpusFinale.addAll(physicalGpus);
                break;
            }
        }
        if (CollectionUtil.isEmpty(physicalGpusFinale)) {
            throw new AiAutomatedException(chooseGpuType + "显卡资源未找到");
        }
        return physicalGpusFinale;

    }


    /**
     * 映射关系
     *
     * @param chooseGpuType
     * @return
     */
    private Map<String, String> getFinalGupType(String chooseGpuType) {
        //映射
        HashMap<String, List<String>> map = kubernetesConfigProperties.getGpuMapping();
        Map<String, String> gpuMap = new HashMap<>();
        if (MapUtils.isNotEmpty(map)) {
            //存在映射关系
            List<String> gpuList = map.getOrDefault(chooseGpuType, Lists.newArrayList(chooseGpuType));
            gpuMap = gpuList.stream().collect(Collectors.toMap(c -> c, c -> c));
        } else {
            //无映射关系
            gpuMap.put(chooseGpuType, chooseGpuType);
        }
        return gpuMap;
    }


    /**
     * 获取数据库中记录的占用
     *
     * @return
     */
    private List<String> getTokenGpus() {
        List<String> tokenGpus = new ArrayList<>();
        QueryWrapper<TaskServerRouterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskServerRouterEntity::getGpuInfo).eq(TaskServerRouterEntity::getIsDeleted, 0).isNotNull(TaskServerRouterEntity::getGpuInfo);
        List<TaskServerRouterEntity> taskServerRouterEntities = taskServerRouterMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(taskServerRouterEntities)) {
            Set<String> collect = taskServerRouterEntities.stream().map(TaskServerRouterEntity::getGpuInfo).map(t -> StrUtil.split(t, ',')).flatMap(Collection::stream).collect(Collectors.toSet());
            tokenGpus.addAll(collect);
        }
        log.info("华为卡获取数据库中记录的占用：{}", JSON.toJSONString(tokenGpus));
        return tokenGpus;
    }

    /**
     * 获取gpu信息
     *
     * @return
     */
    public List<PhysicalGpuDTO> getPhysicalGpuList(Boolean isHuawei, PhysicalGpuDTO physicalGpu, List<String> tokenGpusForHuawei) {
        List<GpuHostItemDTO> physicalGpuInfo = getGpuInfo(false);
        List<PhysicalGpuDTO> list = new ArrayList<>();
        String physicalGpuId = physicalGpu == null ? "" : physicalGpu.getPhysicalGpuId();
        String physicalHost = physicalGpu == null ? "" : physicalGpu.getHost();

        List<String> tokenGpus = new ArrayList<>();
        if (BooleanUtil.isTrue(useToken) && BooleanUtil.isTrue(isHuawei) && CollectionUtil.isEmpty(tokenGpusForHuawei)) {
            List<String> tokenGpus1 = getTokenGpus();
            if (CollectionUtil.isNotEmpty(tokenGpus1)) {
                tokenGpus.addAll(tokenGpus1);
            }
        }
        if (CollectionUtil.isNotEmpty(tokenGpusForHuawei)) {
            tokenGpus.addAll(tokenGpusForHuawei);
        }

        for (GpuHostItemDTO gpuHostItemDTO : physicalGpuInfo) {
            //需要过滤物理gpuID
            if (StrUtil.isNotBlank(physicalHost) && !physicalHost.equals(gpuHostItemDTO.getHost())) {
                continue;
            }
            List<GpuItemDTO> detail = gpuHostItemDTO.getDetail();
            for (GpuItemDTO gpuItemDTO : detail) {
                String physicalGpuId0 = gpuItemDTO.getPhysicalGpuId();
                //需要过滤物理gpuID
                if (StrUtil.isNotBlank(physicalGpuId) && !physicalGpuId.equals(physicalGpuId0)) {
                    continue;
                }
                //华为卡的跳过已安装过算法的卡
                if (BooleanUtil.isTrue(isHuawei)) {
                    List<PodInfoDTO> pod = gpuItemDTO.getPod();
                    String key = gpuHostItemDTO.getHost() + ":" + gpuItemDTO.getGpuId();
                    if (CollectionUtil.isNotEmpty(pod) || tokenGpus.contains(key)) {
                        continue;
                    }
                }
                PhysicalGpuDTO physicalGpu0 = PhysicalGpuDTO.convert(gpuItemDTO, gpuHostItemDTO);
                list.add(physicalGpu0);
            }
        }
        return list;
    }


    /**
     * 获取gpu信息
     *
     * @return
     */
    public List<GpuHostItemDTO> getGpuInfo(Boolean realInfo) {
        List<GpuHostItemDTO> physicalGpuInfo = abilityOperateService.getPhysicalGpuInfo(realInfo);
        if (CollectionUtil.isEmpty(physicalGpuInfo)) {
            throw new AiAutomatedException("gpu 获取失败");
        }
        return physicalGpuInfo;
    }


    /**
     * 添加gpu环境变量
     *
     * @param envs
     * @param gpuId
     */
    public void handleEnvGpu(String gpuType, List<String> envs, String gpuId, Boolean isHuaweiVirtual) {
        if (envs == null) {
            envs = new ArrayList<>();
        }
        if (StringUtils.isBlank(gpuId)) {
            return;
        }
        List<String> split = StrUtil.split(gpuType, "_");
        String code = CollectionUtil.getFirst(split);
        String env = GpuEnvEnums.getGpuEnv(code).getEnv();
        log.info("handleEnvGpu 选择的gpuType：{},env:{},是否虚拟卡：{}", gpuType, env, isHuaweiVirtual);
        for (int i = 0; i < envs.size(); i++) {
            String s = envs.get(i);
            if (s.contains(env)) {
                envs.remove(i);
                break;
            }
        }
        envs.add(0, env + "=" + gpuId);
        //华为卡额外配置
        // TODO && BooleanUtil.isTrue(isHuaweiVirtual) 需求延后
        if (GpuEnvEnums.HUAWEI.getEnv().equals(env)) {
            envs.add("ASCEND_RUNTIME_OPTIONS=VIRTUAL");
        }
    }


    /**
     * 获取部署的gpuType
     *
     * @param deployInfo
     * @return
     */
    public String getDeployInfoChooseGpu(String deployInfo) {
        String chooseGpuType = null;
        if (com.vos.kernel.common.utils.StringUtils.isNotEmpty(deployInfo)) {
            AbilityDeployDTO abilityDeployDTO = JSON.parseObject(deployInfo, AbilityDeployDTO.class);
            chooseGpuType = abilityDeployDTO.getOmChooseGpuType();
        }
        return chooseGpuType;
    }


    /**
     * 获取远程gpu信息
     *
     * @param slaveAddress
     * @return
     */
    private List<GpuHostItemDTO> getRemoteGpu(String slaveAddress) {
        IEmbedService slaveClient = masterScheduleConfig.getSlaveClient(slaveAddress, "");
        if (null == slaveClient) {
            return new ArrayList<>();
        }
        //获取该类型下资源最大的集群
        R<MasterGpuDTO> instantlyGpuResource = slaveClient.getInstantlyGpuResource(false);
        List<GpuHostItemDTO> list = instantlyGpuResource.getData().getList();
        if (CollectionUtil.isNotEmpty(list)) {
            for (GpuHostItemDTO g : list) {
                g.setMachineGroup(slaveAddress);
            }
        }
        return list;
    }


    /**
     * 检测gpu剩余
     *
     * @param host
     * @param gpuId
     * @return
     */
    public PhysicalGpuDTO checkGpuFreeMem(String machineGroup, String host, String gpuId) {
        log.info("checkGpuFreeMem,machineGroup:{},host:{},gpuId:{}", machineGroup, host, gpuId);
        List<PhysicalGpuDTO> list = new ArrayList<>();
        //单机房｜跨机房
        List<GpuHostItemDTO> physicalGpuInfo = (StrUtil.isBlank(machineGroup) || "default".equals(machineGroup)) ? abilityOperateService.getPhysicalGpuInfo(false) : getRemoteGpu(machineGroup);
        if (CollectionUtil.isEmpty(physicalGpuInfo)) {
            throw new AiAutomatedException("gpu 获取失败");
        }

        boolean isHuawei = false;
        for (GpuHostItemDTO gpuHostItemDTO : physicalGpuInfo) {
            gpuHostItemDTO.setMachineGroup(StrUtil.isBlank(gpuHostItemDTO.getMachineGroup()) ? "default" : gpuHostItemDTO.getMachineGroup());
            List<GpuItemDTO> detail = gpuHostItemDTO.getDetail();
            for (GpuItemDTO gpuItemDTO : detail) {
                PhysicalGpuDTO physicalGpu = PhysicalGpuDTO.convert(gpuItemDTO, gpuHostItemDTO);
                list.add(physicalGpu);
            }
        }
        List<PhysicalGpuDTO> physicalList = list.stream().filter(t -> host.equals(t.getHost()) && gpuId.equals(t.getPhysicalGpuId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(physicalList)) {
            throw new DubboBaseException("显存不足");
        }
        PhysicalGpuDTO first = CollectionUtil.getFirst(physicalList);
        if (first.getGpuType().contains("HUAWEI")) {
            isHuawei = true;
        }
        if (isHuawei) {
            List<PhysicalGpuDTO> collect = list.stream().filter(t -> host.equals(t.getHost()) && gpuId.equals(t.getPhysicalGpuId()) && Integer.parseInt(t.getFreeMem()) > 0).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                throw new DubboBaseException("显存不足");
            }
            PhysicalGpuDTO physicalGpuDTO = new PhysicalGpuDTO();
            physicalGpuDTO.setMachineGroup(machineGroup);
            physicalGpuDTO.setPhysicalGpuId(gpuId);
            physicalGpuDTO.setGpuId(gpuId);
            physicalGpuDTO.setHost(host);

            return physicalGpuDTO;
        } else {
            for (PhysicalGpuDTO physicalGpuDTO : list) {
                String gpuHost = physicalGpuDTO.getHost();
                String dtoGpuId = physicalGpuDTO.getGpuId();
                if (Objects.equals(gpuHost, host)) {
                    if (dtoGpuId.equals(gpuId)) {
                        BigDecimal freeMem = new BigDecimal(physicalGpuDTO.getFreeMem());
                        BigDecimal useMem = new BigDecimal(physicalGpuDTO.getUsedMem());
                        BigDecimal total = freeMem.add(useMem);
                        BigDecimal div = NumberUtil.div(useMem, total);
                        BigDecimal bigDecimal = new BigDecimal("" + residualVideoMemory);

                        if (div.compareTo(bigDecimal) > 0) {
                            throw new DubboBaseException("显存不足");
                        }
                        return physicalGpuDTO;
                    }
                }
            }
        }

        throw new DubboBaseException("显卡信息异常");
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            getGpuDetailInfo();
        } catch (Exception e) {

        }
    }

    /**
     * 检测gpu剩余
     *
     * @param host
     * @param gpuId
     * @return
     */
    public List<PhysicalGpuDTO> getGpuFreeMem(String gpuType, String machineGroup, String host, String gpuId) {
        log.info("checkGpuFreeMem,machineGroup:{},host:{},gpuId:{}", machineGroup, host, gpuId);
        List<PhysicalGpuDTO> list = new ArrayList<>();
        //单机房｜跨机房
        List<GpuHostItemDTO> physicalGpuInfo = (StrUtil.isBlank(machineGroup) || "default".equals(machineGroup)) ? abilityOperateService.getPhysicalGpuInfo(false) : getRemoteGpu(machineGroup);
        if (CollectionUtil.isEmpty(physicalGpuInfo)) {
            return list;
        }
        boolean isHuawei = false;
        for (GpuHostItemDTO gpuHostItemDTO : physicalGpuInfo) {
            List<GpuItemDTO> detail = gpuHostItemDTO.getDetail();
            for (GpuItemDTO gpuItemDTO : detail) {
                PhysicalGpuDTO physicalGpu = PhysicalGpuDTO.convert(gpuItemDTO, gpuHostItemDTO);
                list.add(physicalGpu);
            }
        }
        if (gpuType.contains("HUAWEI")) {
            isHuawei = true;
        }
        //获取映射关系
        HashMap<String, List<String>> gpuMapping = kubernetesConfigProperties.getGpuMapping();
        List<String> gpuMappings = new ArrayList<>();
        gpuMappings.add(gpuType);
        if (MapUtils.isNotEmpty(gpuMapping) && CollectionUtil.isNotEmpty(gpuMapping.get(gpuType))) {
            gpuMappings.addAll(gpuMapping.get(gpuType));
        }
        if (isHuawei) {
            if (StrUtil.isNotBlank(gpuId)) {
//                return list.stream().filter(t -> host.equals(t.getHost()) && t.getGpuType().equals(gpuType) && CollectionUtil.isEmpty(t.getPod())).collect(Collectors.toList());
                return list.stream().filter(t -> host.equals(t.getHost()) && gpuMappings.contains(t.getGpuType()) && CollectionUtil.isEmpty(t.getPod())).collect(Collectors.toList());
            } else {
//                return list.stream().filter(t -> host.equals(t.getHost()) && t.getGpuType().equals(gpuType) && gpuId.equals(t.getPhysicalGpuId()) && CollectionUtil.isEmpty(t.getPod())).collect(Collectors.toList());
                return list.stream().filter(t -> host.equals(t.getHost()) && gpuMappings.contains(t.getGpuType()) && gpuId.equals(t.getPhysicalGpuId()) && CollectionUtil.isEmpty(t.getPod())).collect(Collectors.toList());
            }
        } else {
            List<PhysicalGpuDTO> filtered = new ArrayList<>();
            for (PhysicalGpuDTO physicalGpuDTO : list) {
                String gpuHost = physicalGpuDTO.getHost();
                String dtoGpuId = physicalGpuDTO.getGpuId();
//                if (Objects.equals(gpuHost, host) && physicalGpuDTO.getGpuType().equals(gpuType)) {
                if (Objects.equals(gpuHost, host) && gpuMappings.contains(physicalGpuDTO.getGpuType())) {
                    if (StrUtil.isNotBlank(gpuId)) {
                        if (dtoGpuId.equals(gpuId)) {
                            filtered.add(physicalGpuDTO);
                        }
                    } else {
                        filtered.add(physicalGpuDTO);
                    }
                }
            }
            return filtered;
        }
    }

}
