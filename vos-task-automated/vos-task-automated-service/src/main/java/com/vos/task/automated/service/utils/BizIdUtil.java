package com.vos.task.automated.service.utils;

import com.linker.basic.utils.RandomUtil;

import java.math.BigInteger;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2022/11/4 下午3:17
 * @DESC:
 */
public class BizIdUtil {

    private static final AtomicInteger seq = new AtomicInteger(0);

    /**
     * 业务简称+3位随机数+13位时间戳+1位seq（0-9）
     *
     * @param bizName
     * @return
     */
    public static synchronized String nextId(String bizName) {
        int seqV = seq.incrementAndGet();
        final String s = RandomUtil.random(100, 1000) + String.valueOf(System.currentTimeMillis()) + seqV;
        if (seqV >= 9) {
            seq.set(0);
        }
        BigInteger n = new BigInteger(s, 10);
        String v = n.toString(36);//0~9,a~z
        return bizName + v;
    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
        System.out.println(BizIdUtil.nextId("ZH"));
    }

}
