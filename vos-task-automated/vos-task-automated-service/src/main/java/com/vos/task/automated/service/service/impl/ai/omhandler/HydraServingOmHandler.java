package com.vos.task.automated.service.service.impl.ai.omhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.enums.InstallActionEnum;
import com.vos.kernel.common.install.ForkTaskData;
import com.vos.kernel.common.install.OmInstallWorkflowTask;
import com.vos.kernel.common.utils.SnowFlakeUtil;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.*;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.dto.om.AtomConfigVo;
import com.vos.task.automated.api.model.dto.om.ImageInfo;
import com.vos.task.automated.api.model.dto.om.ServingConfigVo;
import com.vos.task.automated.api.model.entity.AtomInfo;
import com.vos.task.automated.api.model.entity.HydraModelInfoEntity;
import com.vos.task.automated.api.model.entity.ServingInfo;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.DeployPatternEnum;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import com.vos.task.automated.service.common.Constants;
import com.vos.task.automated.service.common.ServingTypeEnum;
import com.vos.task.automated.service.config.DeployConfigProperties;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.service.IAbilityOperateService;
import com.vos.task.automated.service.service.batisplus.IHydraModelInfoService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.automated.service.service.impl.ai.gpu.GpuInfoHelper;
import com.vos.task.automated.service.service.impl.ai.install.AiAbilityCheckApi;
import com.vos.task.automated.service.service.impl.ai.install.BaseDeployOperate;
import com.vos.task.manage.api.model.entity.TaskApp;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.vos.task.automated.service.common.Constants.HUAWEI_GPU_TYPE;

/**
 * <AUTHOR>
 * @date 2023年10月30日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class HydraServingOmHandler extends AbstractOmAiHandler {

    /**
     * ai算法命名空间
     */
    @Value("${deploy.aiNamespace:ai}")
    private String aiNamespace;


    @Value("${deploy.ingressIp:''}")
    private String ingressIp;

    @Value("${deploy.ingressPort:''}")
    private String ingressPort;

    /**
     * gpu资源预支
     */
    @Value("${deploy.gpuToken:true}")
    private Boolean gpuToken;

    /**
     * 延迟删除
     */
    @Value("${deploy.removeLately:0}")
    private Integer removeLately;

    @Resource
    BaseDeployOperate baseDeployOperate;

    @Resource
    IHydraModelInfoService hydraModelInfoService;

    @Resource
    GpuInfoHelper gpuInfoHelper;

    @Resource
    IAbilityOperateService abilityOperateService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    AiAbilityCheckApi aiAbilityCheckApi;

    @Resource
    KubernetesConfigProperties kubernetesConfigProperties;

    @Resource
    DeployConfigProperties deployConfigProperties;

    public HydraServingOmHandler() {
        type = ServingTypeEnum.HYDRA.name();
    }

    @Override
    protected void doHealthCheck(InstallPipelineContext context) {
        AbilityApiTypeEnum abilityApiType = context.getAbilityApiType();
        OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("serving健康检查", "servingHealthCheck", MapUtil.of("abilityApiType", abilityApiType));
        if (abilityApiType != null && abilityApiType.getCodeInteger() == 4) {
            workflowTask.success();
            return;
        }
        //大语言模型算法自己占用
        if (AbilityApiTypeEnum.LLM.equals(abilityApiType) || AbilityApiTypeEnum.V3_CHAT.equals(abilityApiType)) {
            workflowTask.success();
            return;
        }
        workflowTask.addInput("hydraModelInstalled", context.getHydraModelInstalled());
        workflowTask.addInput("deployPattern", context.getDeployPattern());

        //不是蛇身的扩容或者迁移都需要检验serving是否启动成功
        if (!(DeployPatternEnum.SINGLE_ATOM.getPattern().equals(context.getDeployPattern()) && BooleanUtil.isTrue(context.getIsScale())) &&
                !(BooleanUtil.isTrue(context.getIsRelocate()))) {
            ServingInfo servingInfo = context.getServingInfo();
            try {
                //等待算法部署状态为active
                baseDeployOperate.healthPodCheck(servingInfo.getK8sName(), context.getDeployTime());
                workflowTask.success();
            } catch (Exception e) {
                workflowTask.error(e.getMessage());
                throw e;
            }
        } else {
            workflowTask.success();
        }

        log.info("九头蛇serving健康检查完成,进行下一步骤");
        if (!BooleanUtil.isTrue(context.getHydraModelInstalled())) {
            //atom健康检查
            List<AtomInfo> atomInfoEntityList = context.getAtomInfoEntityList();
            List<OmInstallWorkflowTask> atomHealth = context.getOmInstallWorkflow().buildForkTask("Atom健康检查Fork", "hydraAtomHealthCheck", MapUtil.empty(),
                    atomInfoEntityList.stream().map(t -> new ForkTaskData(t.getK8sName() + "-health", "健康检查", null)).collect(Collectors.toList()));
            for (AtomInfo a : atomInfoEntityList) {
                OmInstallWorkflowTask workflowSubTask = atomHealth.stream().filter(t -> t.getTaskDefName().contains(a.getK8sName())).findFirst().get();
                try {
                    baseDeployOperate.healthPodCheck(a.getK8sName(), context.getDeployTime());
                    workflowSubTask.success();
                } catch (Exception e) {
                    workflowSubTask.error(e.getMessage());
                    throw e;
                }
            }
            OmInstallWorkflowTask workflowAtomHealthTask = context.getOmInstallWorkflow().buildJoinTask("九头蛇Atom健康检查Join", "hydraAtomHealthRes", MapUtil.empty(),
                    atomHealth.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));
            workflowAtomHealthTask.success();
        }
    }

    @Override
    protected void doOccupyGpu(InstallPipelineContext context) {
        if ((BooleanUtil.isTrue(context.getIsScale())
                && context.getDeployPattern().equals(DeployPatternEnum.SINGLE_ATOM.getPattern())) || (BooleanUtil.isTrue(context.getIsRelocate())
                && context.getDeployPattern().equals(DeployPatternEnum.SINGLE_ATOM.getPattern()))) {
            //蛇身扩容直接返回
            log.info("蛇身扩容不占用gpu，直接返回");
            return;
        }
        AbilityApiTypeEnum abilityApiType = context.getAbilityApiType();
        //大语言模型算法自己占用
        if (AbilityApiTypeEnum.LLM.equals(abilityApiType) || AbilityApiTypeEnum.V3_CHAT.equals(abilityApiType)) {
            return;
        }
        log.info("开始请求算法,占用gpu");
        FileReader fileReader = new FileReader(kubernetesConfigProperties.getPreRequestAiParamsPath());
        JSONObject requestParams = JSONObject.parseObject(fileReader.readString());
        String occupyGpuUrl = context.getOccupyGpuUrl();
        if (StrUtil.isNotBlank(occupyGpuUrl)) {
            OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("请求初始化算法", "requestAbility", MapUtil.of("url", occupyGpuUrl));
            try {
                requestParams.getJSONObject("schedulingCenter").put("linker_ai_debug_flag", "debug_mode_activate");
                workflowTask.addInput("gpuToken", gpuToken);
                if ((abilityApiType != null && abilityApiType.getCodeInteger() == 3)) {
                    JSONArray src = requestParams.getJSONArray("src");
                    for (int i = 0; i < src.size(); i++) {
                        JSONObject jsonObject = src.getJSONObject(i);
                        jsonObject.put("kwargs", JSON.parseObject("{\"displayAllBboxes\":false,\"coldActivation\":{\"includeClasses\":[],\"type\":\"\"},\"ommodelv3\":{\"task\":\"识别人\",\"threshold\":0.6,\"label\":\"person\",\"uniqueId\":\"0\",\"labels\":[\"person\"]},\"boxEnlargeSize\":1}"));
                    }
                }
                JSONObject check = aiAbilityCheckApi.check(occupyGpuUrl, requestParams);
                log.debug("占用gpu请求返回结果：{}", JSON.toJSONString(check));
                if (!BooleanUtil.isTrue(gpuToken)) {
                    if (!BooleanUtil.isTrue(check.getBoolean("isSuccess"))) {
                        String errorMsg = check.getJSONObject("body").getString("message");
                        if (removeLately != null && removeLately > 0) {
                            ThreadUtil.safeSleep(removeLately);
                        }
                        workflowTask.error(errorMsg);
                        throw new AiAutomatedException(errorMsg);
                    } else {
                        workflowTask.success();
                    }
                } else {
                    //判断预估值与真实值
                    workflowTask.success();
                }
            } catch (Exception e) {
                if (!BooleanUtil.isTrue(gpuToken)) {
                    workflowTask.error(MapUtil.of("requestError", e.getMessage()));
                    throw new AiAutomatedException("算法初始请求失败：" + e.getMessage());
                } else {
                    workflowTask.success(MapUtil.of("requestError", e.getMessage()));
                }
            }

        }
        //华为卡部署：休眠5S等待运维gpuInfo接口可查询到
        if (context.getChooseGpuType() != null && context.getChooseGpuType().contains(HUAWEI_GPU_TYPE)) {
            ThreadUtil.safeSleep(5000);
        }
    }

    @Override
    protected void doDeploy(InstallPipelineContext context) {
        if (kubernetesConfigProperties.isVgpuEnable()) {
            throw new AiAutomatedException("九头蛇模式部署仅支持物理gpu");
        }
        List<AtomInfo> atomInfos = new ArrayList<>();
        List<String> podInstall = new ArrayList<>();
        if (BooleanUtil.isTrue(context.getIsScale())) {
            //扩容
            scaleDeploy(context, atomInfos, podInstall);
        } else if (BooleanUtil.isTrue(context.getIsRelocate())) {
            //迁移逻辑
            relocateDeploy(context, atomInfos, podInstall);
        } else {
            //部署
            installDeploy(context, atomInfos, podInstall);
        }
        //跨机房&单机都需要
        context.setDeployTime(LocalDateTime.now());
        context.setAtomInfoEntityList(atomInfos);
        context.setPodDeployNameInstalled(podInstall);

        //非跨机房部署继续构造上下文
        if (!BooleanUtil.isTrue(context.getIsSlave())) {
            buildDataBaseContext0(context);
        }
    }

    @Override
    protected void buildDataBaseContext0(InstallPipelineContext context) {
        ServingInfo servingInfo = context.getServingInfo();
        //组装route数据
        String idStr = SnowFlakeUtil.getIdStr();
        TaskServerRouterEntity taskServerRouterEntity = new TaskServerRouterEntity();
        taskServerRouterEntity.setTaskServerUrl(servingInfo.getServingRequestUrl());
        taskServerRouterEntity.setServingKey(idStr);
        taskServerRouterEntity.setSlavePods(JSON.toJSONString(context.getPodDeployNameInstalled()));
        //部署信息，扩容时使用
        taskServerRouterEntity.setDeployInfo(JSON.toJSONString(context.getAbilityDeployInfo()));
        taskServerRouterEntity.setSlaveAddress(context.getDeploySlaveAddress());
        taskServerRouterEntity.setModelId(context.getModelId());
        //configMap
        taskServerRouterEntity.setServingConfigMap(servingInfo.getServingConfigMap());
        //预热阶段
        taskServerRouterEntity.setStatus(2);
        taskServerRouterEntity.setModelTypeInfo(JSON.toJSONString(context.getAbilityModelInfo()));
        context.setTaskServerRouter(taskServerRouterEntity);
        //serving 返回servingKey
        servingInfo.setServingRequestUrl(idStr);
        context.setServingInfo(servingInfo);
    }

    @Override
    protected AbilityDeployDTO buildDeployInfo0(InstallPipelineContext context) {
        AbilityDeployDTO abilityDeployDTO = new AbilityDeployDTO();

        if (BooleanUtil.isTrue(context.getIsScale())) {
            //扩容模式下
        } else {
            //部署模式下
            abilityDeployDTO.setOmChooseGpuType(context.getChooseGpuType());
            abilityDeployDTO.setServingType(context.getServingType());
            context.setAbilityDeployInfo(abilityDeployDTO);
            abilityDeployDTO.setAbilityApiType(context.getAbilityApiType());
            //构造算子数据
            buildAtomDeployInfo(context);
            //构造serving部署信息
            buildServingDeployInfo(context);
        }
        return abilityDeployDTO;
    }

    /**
     * 扩容部署
     *
     * @param context
     */
    protected void scaleDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {
        log.info("atom扩容信息构造,当前context：{}", JSON.toJSONString(context));
        Integer deployPattern = context.getDeployPattern();
        if (deployPattern.equals(DeployPatternEnum.SINGLE_ATOM.getPattern())) {
            atomDeploy(context, atomInfos, podInstall);
        } else if (deployPattern.equals(DeployPatternEnum.SINGLE_SERVING.getPattern())) {
            if (BooleanUtil.isTrue(context.getAtomOnly())) {
                throw new AiAutomatedException("serving-atom合并的新架构不支持单独扩容serving");
            }
            servingDeploy(context, podInstall);
        } else {
            atomDeploy(context, atomInfos, podInstall);
            servingDeploy(context, podInstall);
        }
    }


    /**
     * 迁移部署
     *
     * @param context
     */
    protected void relocateDeploy(InstallPipelineContext context, List<AtomInfo> atomInfo, List<String> podInstall) {

        log.info("atom部署信息构造,当前context：{}", JSON.toJSONString(context));
        Integer deployPattern = context.getDeployPattern();
        if (deployPattern.equals(DeployPatternEnum.SINGLE_ATOM.getPattern())) {
            //扩容的蛇身的迁移
            atomDeploy(context, atomInfo, podInstall);
        } else {
            //整个算法的迁移 蛇头+蛇身
            //部署算子
            atomDeploy(context, atomInfo, podInstall);
            //部署serving
            servingDeploy(context, podInstall);
        }
    }

    /**
     * 安装部署
     *
     * @param context
     */
    protected void installDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {
//        if (!BooleanUtil.isTrue(context.getIsSlave())) {
//            //构造部署信息
//            buildDeployInfo(context);
//        }
        log.info("atom部署信息构造,当前context：{}", JSON.toJSONString(context));
        //部署算子
        atomDeploy(context, atomInfos, podInstall);
        //部署serving
        servingDeploy(context, podInstall);
    }


    /**
     * 判断九头蛇同一modelId的蛇身是否已经安装
     *
     * @param modelId
     * @return
     */
    private HydraModelInfoEntity checkHydraModelInstalled(String modelId) {

        QueryWrapper<HydraModelInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HydraModelInfoEntity::getDeleted, 0)
                .eq(HydraModelInfoEntity::getModelId, modelId).select(HydraModelInfoEntity::getId, HydraModelInfoEntity::getCreateTime, HydraModelInfoEntity::getRouterId).last(" limit 1");

        return hydraModelInfoService.getOne(queryWrapper);
    }


    /**
     * 获取service
     *
     * @param atomDeployInfos
     * @return
     */
    private Map<String, String> getAtomKeyUrlMap(List<AtomDeployDTO> atomDeployInfos) {
        Map<String, String> atomUrls = new HashMap<>();
        for (AtomDeployDTO a : atomDeployInfos) {
            AtomInfo atomInfo = a.getAtomInfo();
            AtomConfigVo atomConfig = a.getAtomConfig();

            atomUrls.put(atomInfo.getAtomKey(), CommonConstant.HTTP_PROTOCOL + atomInfo.getContainerName() + CommonConstant.CLUSTER_IP_SUFFIX + ":" + atomConfig.getInner_port() + atomConfig.getSuffix());
        }
        return atomUrls;
    }


    /**
     * 部署算子
     *
     * @param context
     */
    private void atomDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {

        List<AtomDeployDTO> atomDeployInfos = context.getAbilityDeployInfo().getAtomDeployInfo();
        if (CollectionUtil.isNotEmpty(atomDeployInfos)) {
            String modelId = context.getModelId();
            //非扩容下已存在蛇身
            if (BooleanUtil.isTrue(context.getHydraModelInstalled()) && !BooleanUtil.isTrue(context.getIsScale())) {
                OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("九头蛇蛇身共用部署", "hydraAtomDeploy", MapUtil.of("HydraModelInstalled", true));
                log.info("初次校验九头蛇蛇身modelID:{},系统中已经安装", modelId);
                for (AtomDeployDTO a : atomDeployInfos) {
                    AtomInfo atomInfo = a.getAtomInfo();
                    atomInfos.add(atomInfo);
                }
                workflowTask.success(MapUtil.of("modelId", modelId));

                //serving部署到的物理机
                if (StrUtil.isNotBlank(CollectionUtil.getFirst(atomDeployInfos).getDeployedHost())) {
                    PhysicalGpuDTO physicalGpuDTO = new PhysicalGpuDTO();
                    physicalGpuDTO.setHost(CollectionUtil.getFirst(atomDeployInfos).getDeployedHost());
                    context.setPhysicalGpuDTO(physicalGpuDTO);
                }

            } else {
//                PhysicalGpuDTO physicalGpuDTO = context.getPhysicalGpuDTO();
//                //部署模式下筛选卡
//                if (BooleanUtil.isFalse(context.checkIsHuawei())) {
//                    if (physicalGpuDTO == null) {
//                        physicalGpuDTO = gpuInfoHelper.choosePhysicalGpu(context.getChooseGpuType(), false, "0");
//                        //校验预分配资源是否够
//                        this.checkPreSignGpu(context.getOmGpuInfo(), physicalGpuDTO.getFreeMem());
//                        context.setPhysicalGpuDTO(physicalGpuDTO);
//                    }
//                    //跨机房ip
//                    if (StrUtil.isNotBlank(context.getDeploySlaveAddress())) {
//                        physicalGpuDTO.setMachineGroup(context.getDeploySlaveAddress());
//                    }
//                    //atom物理gpu环境变量添加
//                    buildAtomPhysicalGpu(atomDeployInfos, Collections.singletonList(physicalGpuDTO), context.getChooseGpuType());
//                } else if (BooleanUtil.isTrue(context.checkIsHuawei())) {
//                    //华为卡部署：判断是否传递了部署物理卡的限制
//                    List<AtomGpuApplyDTO> atomGpuApply = buildAtomGpuApply(atomDeployInfos);
//                    List<PhysicalGpuDTO> physicalGpus = gpuInfoHelper.choosePhysicalGpuHuawei(context.getChooseGpuType(), physicalGpuDTO != null, physicalGpuDTO, atomGpuApply, context.getTokenGpusForHuawei());
//                    //跨机房ip
//                    if (StrUtil.isNotBlank(context.getDeploySlaveAddress())) {
//                        physicalGpus.forEach(t -> t.setMachineGroup(context.getDeploySlaveAddress()));
//                    }
//                    if (physicalGpuDTO == null) {
//                        context.setPhysicalGpuDTO(CollectionUtil.getFirst(physicalGpus));
//                    }
//                    //atom物理gpu环境变量添加
//                    buildAtomPhysicalGpu(atomDeployInfos, physicalGpus, context.getChooseGpuType());
//                }
                List<PhysicalGpuDTO> collect = atomDeployInfos.stream().filter(t -> t.getAtomInfo().getPhysicalGpuDTO() != null)
                        .map(t -> t.getAtomInfo().getPhysicalGpuDTO()).collect(Collectors.toList());
                PhysicalGpuDTO physicalGpuDTO = CollectionUtil.getFirst(collect);
                context.setPhysicalGpuDTO(physicalGpuDTO);
                Map<String, String> atomKeyUrlMap1 = getAtomKeyUrlMap(atomDeployInfos);

                List<OmInstallWorkflowTask> atomDeploy = context.getOmInstallWorkflow().buildForkTask("Atom部署Fork", "atomDeploy", MapUtil.of("type", "hydra"),
                        atomDeployInfos.stream().map(t -> new ForkTaskData(t.getAtomInfo().getK8sName() + "-deploy", "Atom部署", null)).collect(Collectors.toList()));
                //未安装过相同modelID的
                for (AtomDeployDTO a : atomDeployInfos) {
                    AtomInfo atomInfo = a.getAtomInfo();
                    AtomConfigVo atomConfig = a.getAtomConfig();
                    atomInfos.add(atomInfo);
                    //atom环境变量处理
                    buildAtomEnv(a, atomKeyUrlMap1);
                    if (BooleanUtil.isTrue(a.getNonRequiredInstall())) {
                        continue;
                    }
                    //service
                    String atomKeyBase = atomInfo.getContainerName();
                    addAtomService(atomKeyBase, atomConfig.getInner_port());
                    //真实创建ingress
                    String s = addAtomIngress(atomKeyBase, atomConfig.getInner_port(), false, context.getHydraModelInstalled());
                    //增加环境变量，算法元数据
                    buildEnvMetaData(a.getEnvs(), atomConfig);
                    OmInstallWorkflowTask workflowTask = atomDeploy.stream().filter(t -> t.getTaskDefName().contains(atomInfo.getK8sName())).findFirst().get();
                    workflowTask.addInput("k8sName", atomKeyBase);
                    workflowTask.addInput("env", a.getEnvs());
                    workflowTask.addInput("atomUrl", s);
                    workflowTask.addInput("config", a.getAtomConfig());
                    workflowTask.addInput("deployHost", a.getDeployedHost());
                    try {
                        //部署
                        abilityOperateService.addAtomPodDeploy(a);
                        workflowTask.success();
                    } catch (Exception e) {
                        workflowTask.error(e.getMessage());
                        throw e;
                    }
                    podInstall.add(atomInfo.getK8sName());
                }
                OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildJoinTask("九头蛇Atom部署Join", "atomDeployRes", MapUtil.of("modelId", modelId), atomDeploy.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));
                workflowTask.success();
                //hydra_model
                HydraModelInfoEntity hydraModelInfoEntity = new HydraModelInfoEntity();
                hydraModelInfoEntity.setModelId(modelId);
                hydraModelInfoEntity.setPods(JSON.toJSONString(atomDeployInfos.stream().map(t -> t.getAtomInfo().getK8sName()).collect(Collectors.toList())));
                context.setHydraModelInfoEntity(hydraModelInfoEntity);
                if (context.getServingInfo() == null && context.getAbilityDeployInfo().getServingDeployInfo() != null) {
                    ServingDeployDTO servingDeployInfo = context.getAbilityDeployInfo().getServingDeployInfo();
                    ServingInfo servingInfo = servingDeployInfo.getServingDeploy();
                    context.setServingInfo(servingInfo);
                }
            }
        }
    }


    /**
     * atom环境变量处理
     *
     * @param atomDeploy
     */
    private void buildAtomEnv(AtomDeployDTO atomDeploy, Map<String, String> atomKeyUrlMap) {
        AtomConfigVo atomConfig = atomDeploy.getAtomConfig();
        if (MapUtil.isNotEmpty(atomConfig.getEnv()) && MapUtil.isNotEmpty(atomKeyUrlMap)) {
            List<String> atomEnvList = new ArrayList<>();

            for (Map.Entry<String, String> e : atomConfig.getEnv().entrySet()) {
                String key = e.getKey();
                String value = e.getValue();
                if (atomKeyUrlMap.containsKey(value)) {
                    value = atomKeyUrlMap.get(value);
                }
                atomEnvList.add(key + "=" + value);
            }
            //设置环境变量
            List<String> envs = atomDeploy.getEnvs();
            if (CollectionUtil.isEmpty(envs)) {
                atomDeploy.setEnvs(atomEnvList);
            } else {
                envs.addAll(atomEnvList);
            }

        }

    }

    /**
     * 添加atom service
     */
    private String addAtomService(String podName, Integer port) {
        String serviceName = podName + CommonConstant.CLUSTER_IP_SUFFIX;
        //service 筛选出相同label的pod进行负载；未创建创建；已创建不处理
        if (!BooleanUtil.isTrue(abilityOperateService.hasService(serviceName, ""))) {
            //部署对应的service;
            abilityOperateService.addClusterIpService(podName, port, null);
        }
        return serviceName;
    }

    /**
     * 添加atom ingress
     */
    private String addAtomIngress(String ingressName, Integer port, Boolean getIngressDirect, Boolean hydraModelInstalled) {

        if (BooleanUtil.isFalse(deployConfigProperties.getHydraUseIngress())) {
            //未安装过不适用ingress;使用svc
            String serviceName = ingressName + CommonConstant.CLUSTER_IP_SUFFIX;
            return CommonConstant.HTTP_PROTOCOL + serviceName + ":" + port;
        } else {
            String ingressHost = StrUtil.replace(ingressName, "-", "") + ".ingress.com";
            if (!BooleanUtil.isTrue(getIngressDirect)) {
                //创建、更新ingress
                if (!BooleanUtil.isTrue(abilityOperateService.hasIngress(ingressName, aiNamespace))) {
                    String serviceName = ingressName + CommonConstant.CLUSTER_IP_SUFFIX;
                    //创建
                    abilityOperateService.addIngressToService(aiNamespace, ingressName, ingressHost, "/v3", serviceName, port);
                }
            }
            return CommonConstant.HTTP_PROTOCOL + ingressHost + ":" + ingressPort + "/v3";
        }

    }


    /**
     * 部署serving
     *
     * @param context
     * @param podInstall
     */
    private void servingDeploy(InstallPipelineContext context, List<String> podInstall) {
        ServingDeployDTO servingDeployInfo = context.getAbilityDeployInfo().getServingDeployInfo();
        ServingInfo servingInfo = servingDeployInfo.getServingDeploy();
        ServingConfigVo servingConfig = servingDeployInfo.getServingConfig();
        OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("serving部署", "servingDeploy", MapUtil.of("atomOnly", context.getAtomOnly()));

        if (!BooleanUtil.isTrue(context.getAtomOnly()) && !BooleanUtil.isTrue(context.getRelocateNoHeader())) {
            Boolean isScale = context.getIsScale();
            HashMap<String, String> atomKeyUrlMap = context.getAbilityDeployInfo().getAtomKeyUrlMap();
            //serving环境变量处理
            List<String> envs = buildServingEnv(servingDeployInfo.getEnvs(), atomKeyUrlMap);
            //n79替换OMMODEL_V3环境变量
            if (CollectionUtil.isNotEmpty(envs)) {
                List<String> collect = envs.stream().filter(t -> t.contains("v3/") && t.contains("url")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    String first = CollectionUtil.getFirst(collect);
                    List<String> split1 = StrUtil.split(first, "=");
                    for (int i = 0; i < envs.size(); i++) {
                        if (envs.get(i).contains("http://v3-image-model-c-s.ai:8000/omdetv2/serving/inf_predict")) {
                            envs.set(i, envs.get(i).replace("http://v3-image-model-c-s.ai:8000/omdetv2/serving/inf_predict", CollectionUtil.getLast(split1)));
                        }
                    }
                }
            }
            servingDeployInfo.setEnvs(envs);
            if (StrUtil.isNotBlank(servingDeployInfo.getConfigMapName())) {
                context.getConfigMapInstalled().add(servingDeployInfo.getConfigMapName());
                servingInfo.setServingConfigMap(servingDeployInfo.getConfigMapName());
                abilityOperateService.addConfigmap(servingDeployInfo.getConfigMapName(), "vql_factory.yml", servingDeployInfo.getServingYml());
            }

            //扩容serving替换
            if (BooleanUtil.isTrue(isScale) && BooleanUtil.isTrue(context.getIsSlave()) || BooleanUtil.isTrue(context.getIsRelocate()) && BooleanUtil.isTrue(context.getIsSlave())) {
                String clusterId = abilityOperateService.getClusterId();
                String domain = CommonConstant.HTTP_PROTOCOL + clusterId + "-ai.com/";
                String s = servingInfo.getServingRequestUrl().replaceAll(domain, "");
                String s1 = StrUtil.subAfter(s, "/", false);
                servingInfo.setServingRequestUrl("/" + s1);
            }

            if (!BooleanUtil.isFalse(deployConfigProperties.getHydraUseIngress())) {
                //设置ingress host
                if (StrUtil.isNotBlank(ingressIp) && StrUtil.isNotBlank(ingressPort)) {
                    K8sHostAliasesDTO k8sHostAliasesDTO = new K8sHostAliasesDTO();
                    k8sHostAliasesDTO.setUseIngress(true);
                    k8sHostAliasesDTO.setIngressIp(ingressIp);
                    List<String> list = ListUtil.list(false, atomKeyUrlMap.values());
                    List<String> collect = list.stream().map(t -> StrUtil.subBefore(t, ":", true)).map(t -> t.replaceAll(CommonConstant.HTTP_PROTOCOL, "")).collect(Collectors.toList());
                    k8sHostAliasesDTO.setHosts(collect);
                    servingDeployInfo.setK8sHostAliasesDTO(k8sHostAliasesDTO);
                }
            }

            //atom与serving是否在同一主机
            if (BooleanUtil.isTrue(kubernetesConfigProperties.isServingConnectToAtomHost())) {
                PhysicalGpuDTO physicalGpuDTO = context.getPhysicalGpuDTO();
                if (physicalGpuDTO != null && StrUtil.isNotBlank(physicalGpuDTO.getHost())) {
                    servingDeployInfo.setDeployedHost(physicalGpuDTO.getHost());
                }
            }
            workflowTask.addInput("config", servingDeployInfo.getServingConfig());
            workflowTask.addInput("env", servingDeployInfo.getEnvs());

            try {
                abilityOperateService.addServingPodDeploy(servingDeployInfo);
                //新增service
                String servingService = abilityOperateService.addClusterIpService(servingInfo.getK8sName(), servingConfig.getInner_port(), null);
                servingInfo.setServingRequestUrl(servingService + servingConfig.getSuffix());
                servingInfo.setServingHealthRequestUrl(servingService + servingConfig.getHealth_check());
                List<String> serviceNameInstalled = context.getServiceNameInstalled();
                if (CollectionUtil.isEmpty(serviceNameInstalled)) {
                    ArrayList<String> arrayList = new ArrayList<>();
                    arrayList.add(servingService);
                    context.setServiceNameInstalled(arrayList);
                } else {
                    context.getServiceNameInstalled().add(servingService);
                }

                workflowTask.success(MapUtil.of("servingUrl", servingInfo.getServingRequestUrl()));
            } catch (Exception e) {
                workflowTask.error(e.getMessage());
                throw e;
            }
            podInstall.add(servingInfo.getK8sName());
            context.setOccupyGpuUrl(servingInfo.getServingRequestUrl());
        } else {
            //只有atom
            String requestUrl = CommonConstant.HTTP_PROTOCOL + servingInfo.getK8sName() + CommonConstant.CLUSTER_IP_SUFFIX + "." + aiNamespace + ":" + servingConfig.getInner_port() + servingConfig.getSuffix();
            servingInfo.setServingRequestUrl(requestUrl);
            context.setOccupyGpuUrl(requestUrl);

            workflowTask.success(MapUtil.of("servingUrl", servingInfo.getServingRequestUrl()));
        }
        if (context.getPhysicalGpuDTO() == null) {
            //为空时任意指定一个
            PhysicalGpuDTO physicalGpuDTO = new PhysicalGpuDTO();
            physicalGpuDTO.setGpuId("default");
            physicalGpuDTO.setHost("default");
            physicalGpuDTO.setMachineGroup(StrUtil.isNotBlank(context.getDeploySlaveAddress()) ? context.getDeploySlaveAddress() : "default");
            servingInfo.setPhysicalGpuDTO(physicalGpuDTO);
            context.setPhysicalGpuDTO(physicalGpuDTO);
        } else {
            servingInfo.setPhysicalGpuDTO(context.getPhysicalGpuDTO());
        }
        if (context.getServingInfo() == null) {
            context.setServingInfo(servingInfo);
        }
    }

    @NotNull
    public static String buildContainerName(AtomInfo atomInfo, String atomModel, HydraModelInfoEntity hydraModelInfoEntity) {
        //63字符长度限制
        String atomKeyBase = CollectionUtil.getLast(StrUtil.split(atomInfo.getImage(), "/"))
                .replaceAll("\\.", "").replaceAll("_", "");
        LocalDateTime versionChange = LocalDateTime.of(2024, 6, 25, 10, 30);
        if (hydraModelInfoEntity != null && hydraModelInfoEntity.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().isBefore(versionChange)) {
            //兼容历史
            atomKeyBase = atomKeyBase.replaceAll(":", "");
            return StrUtil.replace(atomModel.toLowerCase(), "_", "") + "-" + atomKeyBase;
        } else {
            //新部署的使用atom镜像名
            List<String> split = StrUtil.split(atomModel.toLowerCase(), "om-");
            String last = CollectionUtil.getLast(split);
            String s = StrUtil.replace(last, "_", "") + "-" + CollectionUtil.getFirst(StrUtil.split(atomKeyBase, ":"));
            return StrUtil.replace(s, ".", "");
        }
    }

    /**
     * 构造部署信息
     *
     * @param context
     */
    private void buildAtomDeployInfo(InstallPipelineContext context) {
        LinkedHashMap<String, String> atomKeyUrlMap = new LinkedHashMap<>(16);
        //atom信息
        JSONObject atomConfig = context.getOmInstallInfo().getAtomConfig();
        String modelId = atomConfig.getString("model_id");
        context.setModelId(modelId);
        JSONObject atoms = atomConfig.getJSONObject("atoms");
        //同个serving下atoms分属于同一组
        String atomGroupId = UUID.randomUUID().toString().replaceAll("-", "");
        List<AtomDeployDTO> atomDeployInfo = new ArrayList<>();
        HydraModelInfoEntity hydraModelInfoEntity = checkHydraModelInstalled(modelId);
        log.info("初次校验九头蛇蛇身modelID:{},系统中是否已经安装了：{}", modelId, JSON.toJSONString(hydraModelInfoEntity));
        if (null != hydraModelInfoEntity) {
            context.setHydraModelInstalled(true);
            context.setDependHydraModelId(hydraModelInfoEntity.getId());
            context.setDependHydraRouterId(hydraModelInfoEntity.getRouterId());
        }
        for (String atomKey : atoms.keySet()) {
            // 组装自动化部署Atom相关信息
            AtomConfigVo atomConfigVo = atoms.getObject(atomKey, AtomConfigVo.class);
            AtomInfo atomInfo = baseDeployOperate.buildSingleAtom(context.getTaskApp(), atomKey, atomConfigVo);
            String atomModel = context.getModelId();
            String containerName = buildContainerName(atomInfo, atomModel, hydraModelInfoEntity);
            atomInfo.setContainerName(containerName);
            atomInfo.setGroupId(atomGroupId);
            //Atom部署调度
            baseDeployOperate.atomResourceCalculate(atomKey, atomConfigVo, atomInfo, context);
            // 部署Atom
            AtomDeployDTO atomDeployDTO = new AtomDeployDTO(atomInfo, atomConfigVo, new ArrayList<>(), "", context.getChooseGpuType(), false, null, null);
            atomDeployInfo.add(atomDeployDTO);

            //提前获取atom请求地址->atom之间存在依赖关系，相互引用
            String atomIngress = addAtomIngress(atomInfo.getContainerName(), atomConfigVo.getInner_port(), true, context.getHydraModelInstalled());
            String atomUrl = atomIngress + atomConfigVo.getSuffix();
            atomKeyUrlMap.put(atomInfo.getAtomKey(), atomUrl);
        }
        if (BooleanUtil.isTrue(context.getHydraModelInstalled())) {
            //安装过相同modelID，存在依赖关系，占用锁防止共享atom删除
            String key = Constants.HYDRA_MODEL_KEY + modelId;
            stringRedisTemplate.opsForValue().set(key, "installing");
        } else {
            for (AtomDeployDTO a : atomDeployInfo) {
                AtomInfo atomInfo = a.getAtomInfo();
                //新增
                context.getPodDeployNameInstalled().add(atomInfo.getK8sName());
                context.getAtomInfoEntityList().add(atomInfo);
            }
        }

        //设置部署信息
        context.getAbilityDeployInfo().setAtomDeployInfo(atomDeployInfo);
        context.getAbilityDeployInfo().setAtomKeyUrlMap(atomKeyUrlMap);
    }

    /**
     * 构造serving部署信息
     *
     * @param context
     */
    private void buildServingDeployInfo(InstallPipelineContext context) {
        String configMapName = UUID.randomUUID().toString().replaceAll("-", "");
        LicenseDataDto licenseData = context.getLicenseData();
        TaskApp taskApp = context.getTaskApp();
        // 组装omAppId
        String omAppId = licenseData.getOmVersionId() + "-" + taskApp.getAppKey();
        //获取serving配置信息
        ServingConfigVo servingConfig = context.getOmInstallInfo().getServingConfig();
        String k8sName;
        String servingImage;
        String servingRequestUrl;
        String servingHealthRequestUrl;

        if (BooleanUtil.isTrue(context.getAtomOnly())) {
            servingImage = "";
            //选出atoms第一个作为调用入口
            AtomDeployDTO firstAtomDeploy = CollectionUtil.getFirst(context.getAbilityDeployInfo().getAtomDeployInfo());
            k8sName = firstAtomDeploy.getAtomInfo().getContainerName();
            servingRequestUrl = firstAtomDeploy.getAtomConfig().getSuffix();
            servingHealthRequestUrl = firstAtomDeploy.getAtomConfig().getHealth_check();
            servingConfig.setSuffix(firstAtomDeploy.getAtomConfig().getSuffix());
            servingConfig.setInner_port(firstAtomDeploy.getAtomConfig().getInner_port());
        } else {
            String servingTime = DateUtil.format(LocalDateTime.now(), CommonConstant.DEPLOY_DATA);
            servingImage = servingConfig.getDocker().getImage();
            ImageInfo servingImageInfo = baseDeployOperate.parseImageStr(servingImage);
            String servingImageName = servingImageInfo.getImageName();
            k8sName = baseDeployOperate.getShortName(servingImageName + "-" + servingTime);
            servingRequestUrl = servingConfig.getSuffix();
            servingHealthRequestUrl = servingConfig.getHealth_check();
        }

        //组装数据库ServingInfo信息
        ServingInfo servingInfo = new ServingInfo();
        servingInfo.setTenantId(taskApp.getTenantId())
                .setAbilityCode(licenseData.getOmVersionId())
                .setAppId(taskApp.getAppId())
                .setOmAppId(omAppId)
                .setAppVersionId(licenseData.getAppVersionId())
                .setAppName(licenseData.getAppName())
                .setAction(InstallActionEnum.INSTALL.getValue())
                .setProcessingSpeed(String.valueOf(licenseData.getConcurrent()))
                .setFixedConcurrent(licenseData.getConcurrent())
                .setK8sName(k8sName)
                .setImage(servingImage)
                .setServingRequestUrl(servingRequestUrl)
                .setServingHealthRequestUrl(servingHealthRequestUrl);
        //部署信息
        ServingDeployDTO servingDeployDTO = new ServingDeployDTO(servingInfo, context.getOmInstallInfo().getServingYml(), servingConfig, context.getOmInstallInfo().getEnvsBak(), configMapName, "", null);
        context.getAbilityDeployInfo().setServingDeployInfo(servingDeployDTO);
        if (!BooleanUtil.isTrue(context.getAtomOnly())) {
            //新增
            context.getPodDeployNameInstalled().add(k8sName);
        }
        context.setServingInfo(servingInfo);
    }

}
