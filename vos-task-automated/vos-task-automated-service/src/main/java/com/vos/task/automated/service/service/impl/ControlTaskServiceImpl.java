package com.vos.task.automated.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.task.automated.service.mapper.ControlTaskMapper;
import com.vos.task.automated.api.model.entity.ControlTask;
import com.vos.task.automated.service.service.ControlTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
//@Transactional(rollbackFor = Exception.class)
public class ControlTaskServiceImpl extends ServiceImpl<ControlTaskMapper, ControlTask> implements ControlTaskService {


}
