package com.vos.task.automated.service.service.rpc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.utils.StringUtils;
import com.vos.task.automated.api.model.dto.*;
import com.vos.task.automated.api.model.dto.k8s.GpuHostItemDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.entity.AbilityNodeInfoEntity;
import com.vos.task.automated.api.model.entity.AtomInfo;
import com.vos.task.automated.api.model.entity.HydraModelInfoEntity;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.AbilityNodeStatusEnum;
import com.vos.task.automated.api.model.enums.DeployPatternEnum;
import com.vos.task.automated.api.service.rpc.IAbilityNodeRpcService;
import com.vos.task.automated.service.common.ServingTypeEnum;
import com.vos.task.automated.service.entity.HydraModelDO;
import com.vos.task.automated.service.mapper.AtomMapper;
import com.vos.task.automated.service.mapper.TaskAbilityMapper;
import com.vos.task.automated.service.mapper.TaskServerRouterMapper;
import com.vos.task.automated.service.service.ITaskServerRouterService;
import com.vos.task.automated.service.service.batisplus.IAbilityNodeInfoService;
import com.vos.task.automated.service.service.batisplus.IHydraModelInfoService;
import com.vos.task.automated.service.service.impl.ai.gpu.GpuInfoHelper;
import com.vos.task.automated.service.service.impl.ai.master.MasterVirtualGpuDeployOperate;
import com.vos.task.automated.service.service.impl.ai.master.RoleProperties;
import com.vos.task.manage.api.model.entity.TaskAbility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.vos.task.automated.service.common.Constants.HUAWEI_GPU_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
public class AbilityNodeRpcServiceImpl implements IAbilityNodeRpcService {
    @Autowired
    private IAbilityNodeInfoService abilityNodeInfoService;
    @Autowired
    private TaskAbilityMapper taskAbilityMapper;
    @Autowired
    AtomMapper atomMapper;
    @Autowired
    IHydraModelInfoService hydraModelInfoService;
    @Autowired
    GpuInfoHelper gpuInfoHelper;
    @Autowired
    private TaskServerRouterMapper taskServerRouterMapper;
    @Autowired
    ITaskServerRouterService taskServerRouterService;

    @Resource
    RoleProperties roleProperties;

    @Resource
    MasterVirtualGpuDeployOperate masterVirtualGpuDeployOperate;

    @Override
    public List<AbilityNodeInfoEntity> abilityScaleNodeList(Long abilityId) {
        TaskAbility taskAbility = taskAbilityMapper.selectById(abilityId);
        if (taskAbility == null || taskAbility.getIsDeleted() == 1) {
            throw new DubboBaseException("算法不存在");
        }
        String servingType = taskAbility.getServingType();

        QueryWrapper<AbilityNodeInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AbilityNodeInfoEntity::getAbilityId, abilityId)
                .eq(AbilityNodeInfoEntity::getDeleted, false);
        if (servingType.equals(ServingTypeEnum.HYDRA.name())) {
            queryWrapper.lambda().eq(AbilityNodeInfoEntity::getDeployPattern, DeployPatternEnum.SINGLE_SERVING.getPattern());
        }
        List<AbilityNodeInfoEntity> list = abilityNodeInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        buildExtra(list, 1);

        return list;
    }

    /**
     * @param list      nodeList
     * @param isInstall 是否安装：1：安装 2：扩容  3 预装  4 九头蛇蛇身
     */
    private void buildExtra(List<AbilityNodeInfoEntity> list, Integer isInstall) {
        List<Long> collect = list.stream().map(AbilityNodeInfoEntity::getId).collect(Collectors.toList());
        Map<Long, Integer> numMap = taskServerRouterService.countCurrentNodeNum(collect);
        Map<Long, Boolean> installNodeMap = taskServerRouterService.getInstallNodeByAbilityNodeId(collect, isInstall);
        if (MapUtils.isNotEmpty(numMap)) {
            list.forEach(c -> {
                c.setCurrentNodeNum(numMap.getOrDefault(c.getId(), 0));
                c.setExistInstallNode(installNodeMap.getOrDefault(c.getId(), false));
            });
        }
    }

    @Override
    public List<AbilityNodeInfoEntity> hydraScaleNodeList(String atomId) {
        QueryWrapper<AbilityNodeInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AbilityNodeInfoEntity::getAtomId, atomId).eq(AbilityNodeInfoEntity::getDeleted, false);
        List<AbilityNodeInfoEntity> list = abilityNodeInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        buildExtra(list, 4);

        return list;
    }

    @Override
    public void addScaleNode(AbilityNodeInfoEntity abilityNodeInfoEntity) {

        String host = abilityNodeInfoEntity.getHost();
        String gpuId = abilityNodeInfoEntity.getGpuId();
        String atomId = abilityNodeInfoEntity.getAtomId();
        String abilityId = abilityNodeInfoEntity.getAbilityId();
        if (StringUtils.isEmpty(atomId) && StringUtils.isEmpty(abilityId)) {
            throw new DubboBaseException("参数异常");
        }

        if (StringUtils.isNotEmpty(atomId)) {
            abilityNodeInfoEntity.setAbilityId("-1");
            abilityNodeInfoEntity.setDeployPattern(DeployPatternEnum.SINGLE_ATOM.getPattern());
        }
        if (StringUtils.isNotEmpty(abilityId)) {
            TaskAbility taskAbility = taskAbilityMapper.selectById(abilityId);
            if (taskAbility == null || taskAbility.getIsDeleted() == 1) {
                throw new DubboBaseException("算法不存在");
            }
            String servingType = taskAbility.getServingType();
            if (servingType.equals(ServingTypeEnum.HYDRA.name())) {
                abilityNodeInfoEntity.setDeployPattern(DeployPatternEnum.SINGLE_SERVING.getPattern());
            } else {
                checkGpuAvailable(abilityNodeInfoEntity, abilityNodeInfoEntity.getExceptDuplicateNum());
            }
        }
        boolean exist = abilityNodeInfoService.exist(host, gpuId, abilityId, atomId);

        if (exist) {
            throw new DubboBaseException("节点已存在");
        }
        abilityNodeInfoService.save(abilityNodeInfoEntity);
    }


    /**
     * 检查资源是否可用
     *
     * @param abilityNodeInfoEntity
     */
    private void checkGpuAvailable(AbilityNodeInfoEntity abilityNodeInfoEntity, Integer incNum) {
        if (!DeployPatternEnum.SINGLE_SERVING.getPattern().equals(abilityNodeInfoEntity.getDeployPattern())) {
            String abilityId = abilityNodeInfoEntity.getAbilityId();
            AbilityLeftDTO abilityNode = taskServerRouterService.getAbilityNodeCapcity(abilityId);

            if (StrUtil.isNotBlank(abilityNode.getOmChooseGpuType())) {
                //获取选择的资源列表
                List<PhysicalGpuDTO> gpuFreeMem = gpuInfoHelper.getGpuFreeMem(abilityNode.getOmChooseGpuType(), abilityNodeInfoEntity.getMachineGroup(), abilityNodeInfoEntity.getHost(), abilityNodeInfoEntity.getGpuId());
                if (CollectionUtil.isEmpty(gpuFreeMem)) {
                    throw new DubboBaseException("资源不足，不可操作");
                }
                gpuFreeMem = gpuFreeMem.stream().filter(t -> !BooleanUtil.isTrue(t.getForbidden())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(gpuFreeMem)) {
                    throw new DubboBaseException("资源不足，禁用gpu卡不可用");
                }
                if (abilityNode.getOmChooseGpuType().contains(HUAWEI_GPU_TYPE)) {
                    //华为卡扩容
                    int need = DeployPatternEnum.SINGLE_ATOM.getPattern().equals(abilityNodeInfoEntity.getDeployPattern()) ? incNum : abilityNode.getNeedList().size() * incNum;
                    if (gpuFreeMem.size() < need) {
                        log.warn("可用资源：{},需要扩容数：{}", JSON.toJSONString(gpuFreeMem), need);
                        throw new DubboBaseException("资源不足，不可操作");
                    }
                } else {
                    //英伟达卡扩容
                    Integer needGpu = DeployPatternEnum.SINGLE_ATOM.getPattern().equals(abilityNodeInfoEntity.getDeployPattern()) ? getAbilityAtomNeedGpu(abilityNodeInfoEntity.getAtomId())
                            : abilityNode.getNeedGpu();
                    int totalNeed = NumberUtil.mul(needGpu, incNum).intValue();
                    int sum = gpuFreeMem.stream().mapToInt(t -> Integer.parseInt(t.getFreeMem())).sum();
                    if (sum < totalNeed) {
                        log.warn("可用资源：{},需要扩容数：{}", JSON.toJSONString(gpuFreeMem), totalNeed);
                        throw new DubboBaseException("资源不足，不可操作");
                    }
                }
            }
        }
    }


    @Override
    public void updateAbilityNode(Long abilityNodeId, Integer exceptDuplicateNum) {
        AbilityNodeInfoEntity abilityNodeInfoEntity = abilityNodeInfoService.getById(abilityNodeId);
        if (Objects.isNull(abilityNodeInfoEntity)) {
            throw new DubboBaseException("节点配置不存在");
        }
        //需要扩容的
        BigDecimal sub = NumberUtil.sub(exceptDuplicateNum, abilityNodeInfoEntity.getExceptDuplicateNum());
        if (sub.compareTo(new BigDecimal(0)) > 0) {
            checkGpuAvailable(abilityNodeInfoEntity, sub.intValue());
        }
        abilityNodeInfoEntity.setStatus(AbilityNodeStatusEnum.READY.getStatus());
        abilityNodeInfoEntity.setDeployProcess(AbilityNodeStatusEnum.READY.getStatus());
        abilityNodeInfoEntity.setExceptDuplicateNum(exceptDuplicateNum);
        abilityNodeInfoService.updateById(abilityNodeInfoEntity);
    }

    @Override
    public Boolean deleteAbilityNode(Long abilityNodeId) {
        AbilityNodeInfoEntity abilityNodeInfoEntity = abilityNodeInfoService.getById(abilityNodeId);
        if (Objects.isNull(abilityNodeInfoEntity)) {
            throw new DubboBaseException("节点配置不存在");
        }
        //当前的状态
        Integer status = abilityNodeInfoEntity.getStatus();
        if (!AbilityNodeStatusEnum.SUCCESS.getStatus().equals(status) && !AbilityNodeStatusEnum.FAIL.getStatus().equals(status) && !AbilityNodeStatusEnum.READY.getStatus().equals(status)) {
            throw new DubboBaseException("节点状态只有在正常或失败下才能操作");
        }
        //当前节点下的节点数
        Long abilityNumByNodeId = taskServerRouterService.getAbilityNumByNodeId(abilityNodeId);
        if (abilityNumByNodeId != 0) {
            throw new DubboBaseException("节点下存在算法不可操作");
        }
        //更改掉状态
        abilityNodeInfoEntity.setDeleted(true);

        return abilityNodeInfoService.updateById(abilityNodeInfoEntity);
    }

    @Override
    public AbilityLeftDTO getAbilityNodeCapcity(AbilityLeftRequestDTO request) {
        AbilityLeftDTO abilityNode = taskServerRouterService.getAbilityNodeCapcity(request.getAbilityId());
        //获取所选节点剩余gpu信息
//        废弃
//        Integer gpuFreeMem = gpuInfoHelper.getGpuFreeMem(request.getMachineGroup(), request.getHost(), request.getGpuId());
//        abilityNode.setLeftGpu(gpuFreeMem);
        return abilityNode;
    }

    @Override
    public Integer getAbilityNeedGpu(String abilityId) {
        AbilityLeftDTO abilityNode = taskServerRouterService.getAbilityNodeCapcity(abilityId);
        return abilityNode.getNeedGpu();
    }

    @Override
    public Integer getAbilityAtomNeedGpu(String atomId) {
        AtomInfo atomInfo = atomMapper.selectById(atomId);
        if (atomInfo == null) {
            return 0;
        }

        return Integer.parseInt(atomInfo.getResourceSituation());
    }

    @Override
    public void deployAbilityNode(Long abilityNodeId) {
        AbilityNodeInfoEntity abilityNodeInfoEntity = abilityNodeInfoService.getById(abilityNodeId);
        if (Objects.isNull(abilityNodeInfoEntity)) {
            throw new DubboBaseException("节点配置不存在");
        }
//        QueryWrapper<TaskServerRouterEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(c->c.getAbilityNodeId(),abilityNodeId).eq(c->c.getIsInstall(),)
//        taskServerRouterMapper.selectList(queryWrapper);

        String atomId = abilityNodeInfoEntity.getAtomId();
        String abilityId = abilityNodeInfoEntity.getAbilityId();
        String gpuType = "";
        Integer deployPattern = abilityNodeInfoEntity.getDeployPattern();
        Long id = abilityNodeInfoEntity.getId();
        Map<Long, Integer> map = taskServerRouterService.countCurrentNodeNum(Lists.newArrayList(id));
        Integer exceptDuplicateNum = abilityNodeInfoEntity.getExceptDuplicateNum();

        Integer currentNodes = map.getOrDefault(id, 0);
        if (!deployPattern.equals(DeployPatternEnum.SINGLE_SERVING.getPattern())) {
            if (currentNodes < exceptDuplicateNum) {
                //扩容才检查,serving不用检查显存
                if (!abilityNodeInfoEntity.getMachineGroup().contains(CommonConstant.HTTP)) {
                    gpuInfoHelper.checkGpuFreeMem(abilityNodeInfoEntity.getMachineGroup(), abilityNodeInfoEntity.getHost(), abilityNodeInfoEntity.getGpuId());
                }
            }

        }
        if (!Objects.equals(currentNodes, exceptDuplicateNum)) {
            abilityNodeInfoEntity.setStatus(AbilityNodeStatusEnum.DOING.getStatus());
        }

        abilityNodeInfoService.updateById(abilityNodeInfoEntity);
    }

    @Override
    public List<TaskServerRouterEntity> abilityScaleNodeDetail(Long abilityNodeId) {
        QueryWrapper<TaskServerRouterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TaskServerRouterEntity::getAbilityNodeId, abilityNodeId).eq(TaskServerRouterEntity::getIsDeleted, 0);
        return taskServerRouterMapper.selectList(queryWrapper);
    }

    @Override
    public Page<HydraModelPageDTO> hydraModelPage(ModelSearchPageDTO request) {

        Page<HydraModelInfoEntity> page = new Page<>(request.getPage(), request.getPageSize());
        String keyword = request.getKeyword();
        QueryWrapper<HydraModelInfoEntity> hydraModelInfoEntityQueryWrapper = new QueryWrapper<>();
        hydraModelInfoEntityQueryWrapper.lambda().eq(HydraModelInfoEntity::getDeleted, false)
                .eq(StringUtils.isNotEmpty(keyword), HydraModelInfoEntity::getModelId, keyword);
        Page<HydraModelInfoEntity> result = hydraModelInfoService.page(page, hydraModelInfoEntityQueryWrapper);
        Page<HydraModelPageDTO> objectPage = new Page<>();
        List<HydraModelInfoEntity> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return objectPage;
        }
        List<String> k8sList = new ArrayList<>();
        List<String> collect = records.stream().map(c -> c.getModelId()).collect(Collectors.toList());
        Map<String, List<HydraModelDO>> modelIdAbilityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(collect)) {
            List<HydraModelDO> hydraModelDoList = taskServerRouterMapper.listJoinAbilityByModelIds(collect);
            if (CollectionUtils.isNotEmpty(hydraModelDoList)) {
                modelIdAbilityMap = hydraModelDoList.stream().collect(Collectors.groupingBy(HydraModelDO::getModelId, Collectors.toList()));
            }
        }
        records.forEach(c -> {
            String pods = c.getPods();
            if (StringUtils.isNotEmpty(pods)) {
                List<String> parseArray = JSONArray.parseArray(pods, String.class);
                k8sList.addAll(parseArray);
            }
        });
        Map<String, AtomInfo> atomMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(k8sList)) {
            QueryWrapper<AtomInfo> atomInfoQueryWrapper = new QueryWrapper<>();
            atomInfoQueryWrapper.lambda().in(AtomInfo::getDeployName, k8sList);
//                    .eq(BaseEntity::getIsDeleted, 0);
            List<AtomInfo> atomInfos = atomMapper.selectList(atomInfoQueryWrapper);
            atomMap = atomInfos.stream().collect(Collectors.toMap(AtomInfo::getK8sName, c -> c));
        }
        List<HydraModelPageDTO> list = new ArrayList<>();
        for (HydraModelInfoEntity record : records) {
            String modelId = record.getModelId();
            List<HydraModelPageDTO.AtomDTO> atoms = new ArrayList<>();
            HydraModelPageDTO hydraModelPageDTO = new HydraModelPageDTO();
            hydraModelPageDTO.setModelId(modelId);
            String pods = record.getPods();
            List<HydraModelDO> hydraModelDOList = modelIdAbilityMap.get(modelId);
            if (CollectionUtils.isNotEmpty(hydraModelDOList)) {
                hydraModelPageDTO.setAbilityList(hydraModelDOList.stream().map(HydraModelDO::getAbilityName).collect(Collectors.toList()));
            }
            if (StringUtils.isNotEmpty(pods)) {
                List<String> parseArray = JSONArray.parseArray(pods, String.class);
                for (String s : parseArray) {
                    if (MapUtils.isNotEmpty(atomMap)) {
                        AtomInfo atomInfo = atomMap.get(s);
                        if (Objects.nonNull(atomInfo)) {
                            HydraModelPageDTO.AtomDTO atomDTO = new HydraModelPageDTO.AtomDTO();
                            atomDTO.setAtomId(atomInfo.getAtomId());
                            atomDTO.setPodName(s);
                            atomDTO.setAtomName(atomInfo.getAtomName());
                            atoms.add(atomDTO);
                        }

                    }
                }
            }
            hydraModelPageDTO.setPodList(atoms);
            list.add(hydraModelPageDTO);
        }
        objectPage.setRecords(list);
        objectPage.setTotal(request.getPage());
        return objectPage;
    }

    @Override
    public List<GpuHostItemDTO> machineGroupInfo() {
        if (BooleanUtil.isTrue(roleProperties.getIsMaster())) {
            List<GpuHostItemDTO> gpuInfo = masterVirtualGpuDeployOperate.getGpuInfo(false);
            return huaweiGpuHost(gpuInfo);
        } else {
            List<GpuHostItemDTO> gpuInfo = gpuInfoHelper.getGpuInfo(false);
            if (CollectionUtil.isNotEmpty(gpuInfo)) {
                gpuInfo.forEach(t -> t.setMachineGroup("default"));
            }
            return huaweiGpuHost(gpuInfo);
        }
    }


    /**
     * 华为卡以物理卡的形式展示
     *
     * @return
     */
    private List<GpuHostItemDTO> huaweiGpuHost(List<GpuHostItemDTO> list) {
        List<GpuHostItemDTO> gpuHostItems = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (GpuHostItemDTO g : list) {
                List<GpuItemDTO> detail = g.getDetail();
                if ("HUAWEI".equals(g.getDriverType())) {
                    Map<String, GpuItemDTO> collect = detail.stream().collect(Collectors.toMap(GpuItemDTO::getPhysicalGpuId, x -> x, (var1, var2) -> var1));
                    Collection<GpuItemDTO> values = collect.values();
                    values.forEach(t -> t.setGpuId(t.getPhysicalGpuId()));
                    g.setDetail(new ArrayList<>(values));
                }
            }
            return list;
        }
        return gpuHostItems;
    }


}