package com.vos.task.automated.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.common.entity.GpuStatusOperateReq;
import com.vos.kernel.common.entity.NodeStatusOperateReq;
import com.vos.kernel.common.entity.R;
import com.vos.kernel.common.enums.BizResponseEnum;
import com.vos.kernel.common.install.OmInstallWorkflow;
import com.vos.kernel.common.install.OmInstallWorkflowTask;
import com.vos.task.automated.api.MasterGpuDTO;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.GpuChooseConfig;
import com.vos.task.automated.api.model.dto.gpu.*;
import com.vos.task.automated.api.model.dto.k8s.GpuHostItemDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.entity.SlaveEntity;
import com.vos.task.automated.service.config.GpuConfigProperties;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.convert.AbilityConvertMapper;
import com.vos.task.automated.service.service.IGpuService;
import com.vos.task.automated.service.service.ISlaveService;
import com.vos.task.automated.service.service.impl.ai.IClusterNodeDrainService;
import com.vos.task.automated.service.service.impl.ai.OmWorkflowDataBuilderService;
import com.vos.task.automated.service.service.impl.ai.gpu.*;
import com.vos.task.automated.service.service.impl.ai.master.IEmbedService;
import com.vos.task.automated.service.service.impl.ai.master.MasterScheduleConfig;
import com.vos.task.automated.service.service.impl.ai.master.RoleProperties;
import com.vos.task.automated.service.service.impl.ai.slave.CompletableFutureExpandUtils;
import com.vos.task.automated.service.utils.k8s.GpuApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.vos.task.automated.service.common.Constants.HUAWEI_GPU_TYPE;

/**
 * <AUTHOR>
 * @date 2024年06月17日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class GpuServiceImpl implements IGpuService {


    @Resource
    MasterScheduleConfig masterScheduleConfig;

    @Resource
    GpuApiService gpuApiService;

    @Resource
    GpuConfigProperties gpuConfigProperties;

    @Resource
    RedissonClient redissonClient;

    @Resource
    GpuInfoHelper gpuInfoHelper;

    @Resource
    KubernetesConfigProperties kubernetesConfigProperties;

    @Resource
    ISlaveService slaveService;

    @Resource
    ThreadPoolExecutor installThreadPool;

    @Resource
    AbilityConvertMapper abilityConvertMapper;

    @Resource
    RoleProperties roleProperties;

    /**
     * 节点下显存剩余最大占比
     */
    @Value("${deploy.residual-video-memory:0.8}")
    private Double residualVideoMemory;


    @Resource
    IClusterNodeDrainService clusterNodeDrainService;

    /**
     * 占用全局锁
     */
    public static final String OCCUPY_GPU_KEY = "occupy_gpu";

    @Override
    public Boolean occupyReset(String slaveAddress, List<GpuOccupyResetItem> occupyResetList) {
        if ("default".equals(slaveAddress)) {
            return gpuOccupyReset(occupyResetList);
        } else {
            R<Boolean> occupyReset = masterScheduleConfig.getSlaveClient(slaveAddress, "").gpuOccupyReset(occupyResetList);
            if (!BizResponseEnum.OK.getBizCode().equals(occupyReset.getCode())) {
                log.error("occupyReset失败：" + occupyReset.getMessage());
                return false;
            } else {
                return occupyReset.getData();
            }
        }


    }

    @Override
    public Boolean gpuOccupyReset(List<GpuOccupyResetItem> occupyResetList) {
        if (StrUtil.isBlank(gpuConfigProperties.getBaseUrl())) {
            throw new AiAutomatedException("gpu配置信息有误");
        }
        if (CollectionUtil.isNotEmpty(occupyResetList)) {
            Map<String, List<GpuOccupyResetItem>> collect = occupyResetList.stream().collect(Collectors.groupingBy(GpuOccupyResetItem::getHost));
            GpuRestReq gpuRestReq = new GpuRestReq();
            List<GpuRestReqItem> resetList = new ArrayList<>();
            for (Map.Entry<String, List<GpuOccupyResetItem>> entry : collect.entrySet()) {
                GpuRestReqItem gpuRestReqItem = new GpuRestReqItem();
                gpuRestReqItem.setHost(entry.getKey());
                gpuRestReqItem.setResetItemList(entry.getValue());
                resetList.add(gpuRestReqItem);
            }
            gpuRestReq.setResetList(resetList);
            return gpuApiService.resetGpuOccupyInfo(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getResetRouter(), gpuRestReq);
        } else {
            GpuRestReq gpuRestReq = new GpuRestReq();
            gpuRestReq.setResetList(new ArrayList<>());
            return gpuApiService.resetGpuOccupyInfo(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getResetRouter(), gpuRestReq);
        }
    }

    @Override
    public void occupyGpu(List<AtomGpuApplyDTO> atomDeployOccupyNeed, GpuChooseConfig gpuConfig, OmInstallWorkflow omInstallWorkflow) {
        RLock occupyLock = redissonClient.getLock(OCCUPY_GPU_KEY);
        try {
            if (occupyLock.tryLock(5, 10, TimeUnit.SECONDS)) {
                log.info("开始进入occupyGpu流程");
                checkAndGetGpuChooseConfig(atomDeployOccupyNeed, gpuConfig, omInstallWorkflow);

                OmInstallWorkflowTask workflowTask = omInstallWorkflow.buildSimpleTask("预占用gpu", "occupyGpu", MapUtil.of("resource", atomDeployOccupyNeed));
                //预占用
                Boolean occupyGpuResult = occupyGpuToGpuService(atomDeployOccupyNeed);
                if (!BooleanUtil.isTrue(occupyGpuResult)) {
                    String error = "预占用gpu资源发生异常";
                    workflowTask.error(error);
                    throw new AiAutomatedException(error);
                }
                workflowTask.success();
            } else {
                throw new AiAutomatedException("occupyGpu出现异常，未抢到占用锁");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (occupyLock.isLocked() && occupyLock.isHeldByCurrentThread()) {
                occupyLock.unlock();
            }
        }
    }

    /**
     * 向从节点申请资源
     *
     * @param slaveAddress
     * @param atomDeployOccupyNeed
     */
    private Boolean occupyGpuToGpuService(String slaveAddress, List<AtomGpuApplyDTO> atomDeployOccupyNeed) {
        IEmbedService slaveClient = masterScheduleConfig.getSlaveClient(slaveAddress, "");
        if (null != slaveClient) {
            try {
                R<Boolean> occupyGpuResult = slaveClient.gpuOccupy(atomDeployOccupyNeed);
                if (!BizResponseEnum.OK.getBizCode().equals(occupyGpuResult.getCode())) {
                    return false;
                } else {
                    return occupyGpuResult.getData();
                }
            } catch (Exception e) {
                log.error("{},跨机房获取gpu信息异常", slaveAddress, e);
            }
        } else {
            throw new AiAutomatedException("预占资源失败，未找到对应的从节点：{}", slaveAddress);
        }
        return false;
    }


    /**
     * 向gpu服务占用显存
     *
     * @param atomDeployOccupyNeed
     */
    @Override
    public Boolean occupyGpuToGpuService(List<AtomGpuApplyDTO> atomDeployOccupyNeed) {

        try {
            GpuOperateDTO gpuOperateDTO = new GpuOperateDTO();
            gpuOperateDTO.setTakenOrRelease(1);
            gpuOperateDTO.setOperateItemList(buildGpuOperate(atomDeployOccupyNeed));
            return gpuApiService.operateGpu(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getOperateRouter(), gpuOperateDTO);
        } catch (Exception e) {
            log.error("向gpu服务占用显存异常:{}", JSON.toJSONString(atomDeployOccupyNeed), e);
            return false;
        }
    }

    /**
     * 向gpu服务变更卡状态
     *
     * @param gpuOperate
     */
    @Override
    public Boolean forbiddenGpuToGpuService(GpuStatusOperateReq gpuOperate) {

        try {
            if (StrUtil.isBlank(gpuOperate.getHost()) || StrUtil.isBlank(gpuOperate.getPhysicalGpuId()) || StrUtil.isBlank(gpuOperate.getGpuUuid())) {
                log.error("gpu状态变更信息缺失");
                return false;
            }
            if (gpuOperate.getForbidden() == null) {
                log.error("gpu是否禁用状态缺失");
                return false;
            }
            return gpuApiService.operateStatusGpu(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getOperateStatusRouter(), gpuOperate);
        } catch (Exception e) {
            log.error("向gpu服务更改显存异常:{}", JSON.toJSONString(gpuOperate), e);
            return false;
        }
    }

    @Override
    public Boolean forbiddenNodeToGpuService(NodeStatusOperateReq nodeStatusOperateReq) {
        try {
            if (StrUtil.isBlank(nodeStatusOperateReq.getHost())) {
                log.error("node状态变更信息缺失");
                return false;
            }
            if (nodeStatusOperateReq.getForbidden() == null) {
                log.error("node是否禁用状态缺失");
                return false;
            }
            return gpuApiService.nodeOperateStatusGpu(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getNodeOperateStatusRouter(), nodeStatusOperateReq);
        } catch (Exception e) {
            log.error("向node服务更改显存异常:{}", JSON.toJSONString(nodeStatusOperateReq), e);
            return false;
        }
    }

    /**
     * 构造参数
     *
     * @param atomDeployOccupyNeed
     * @return
     */
    private List<GpuOperateItemDTO> buildGpuOperate(List<AtomGpuApplyDTO> atomDeployOccupyNeed) {
        List<GpuOperateItemDTO> operateItemList = new ArrayList<>();
        for (AtomGpuApplyDTO a : atomDeployOccupyNeed) {
            PhysicalGpuDTO physicalGpu = a.getPhysicalGpu();
            if (a.getNumGpus() != null && a.getNumGpus() > 0) {
                log.info("多卡占用构造数据：{}", JSON.toJSONString(physicalGpu));
                List<String> split = StrUtil.split(physicalGpu.getGpuId(), ",");
                for (String s : split) {
                    GpuOperateItemDTO gpuOperateItem = new GpuOperateItemDTO();
                    gpuOperateItem.setGpuId(s);
                    gpuOperateItem.setHost(physicalGpu.getHost());
                    gpuOperateItem.setAbilityDeployName(a.getAtomK8sName());
                    gpuOperateItem.setResourceNum(NumberUtil.add(physicalGpu.getUsedMem(), physicalGpu.getFreeMem()).intValue());
                    operateItemList.add(gpuOperateItem);
                }
            } else {
                GpuOperateItemDTO gpuOperateItem = new GpuOperateItemDTO();
                gpuOperateItem.setGpuId(physicalGpu.getGpuId());
                gpuOperateItem.setHost(physicalGpu.getHost());
                gpuOperateItem.setAbilityDeployName(a.getAtomK8sName());
                gpuOperateItem.setResourceNum(a.getNeedResource());
                operateItemList.add(gpuOperateItem);
            }
        }
        return operateItemList;
    }

    @Override
    public Boolean occupyGpuRelease(List<AtomGpuApplyDTO> atomDeployOccupyNeed) {
        try {
            GpuOperateDTO gpuOperateDTO = new GpuOperateDTO();
            gpuOperateDTO.setTakenOrRelease(0);
            gpuOperateDTO.setOperateItemList(buildGpuOperate(atomDeployOccupyNeed));
            return gpuApiService.operateGpu(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getOperateRouter(), gpuOperateDTO);
        } catch (Exception e) {
            log.error("向gpu服务释放显存异常，{}", JSON.toJSONString(atomDeployOccupyNeed), e);
            return true;
        }
    }

    @Override
    public Boolean occupyGpuReleasei(List<GpuOperateItemDTO> gpuOperateItem) {
        if (CollectionUtil.isEmpty(gpuOperateItem)) {
            return true;
        }
        try {
            Map<String, List<GpuOperateItemDTO>> collect = gpuOperateItem.stream().collect(Collectors.groupingBy(GpuOperateItemDTO::getHost));
            for (Map.Entry<String, List<GpuOperateItemDTO>> m : collect.entrySet()) {
                GpuOperateDTO gpuOperateDTO = new GpuOperateDTO();
                gpuOperateDTO.setTakenOrRelease(0);
                gpuOperateDTO.setOperateItemList(m.getValue());
                gpuApiService.operateGpu(gpuConfigProperties.getBaseUrl() + gpuConfigProperties.getOperateRouter(), gpuOperateDTO);
            }
        } catch (Exception e) {
            log.error("向gpu服务释放显存异常，{}", JSON.toJSONString(gpuOperateItem), e);
        }
        return true;
    }

    /**
     * 获取并检查选择的gpu主机卡信息
     *
     * @param gpuConfig
     */
    private void checkAndGetGpuChooseConfig(List<AtomGpuApplyDTO> atomDeployOccupyNeed, GpuChooseConfig gpuConfig, OmInstallWorkflow omInstallWorkflow) {
        //选择卡
        String gpuType = gpuConfig.getChooseGpuType();
        boolean isHuawei = false;
        if (gpuType.contains(HUAWEI_GPU_TYPE)) {
            isHuawei = true;
        }
        List<PhysicalGpuDTO> currentClusterSuitGpu = getCurrentClusterSuitGpu(gpuConfig, gpuType, isHuawei, omInstallWorkflow);
        log.info("筛选后gpu数据:{}", JSON.toJSONString(currentClusterSuitGpu));
        if (CollectionUtil.isNotEmpty(currentClusterSuitGpu)) {
            OmInstallWorkflowTask workflowTask = omInstallWorkflow.buildSimpleTask("集群gpu资源检测", "checkGpu", OmWorkflowDataBuilderService.buildSingleOccupyGpuInputData(atomDeployOccupyNeed));
            try {
                if (isHuawei) {
                    //华为卡选卡逻辑
                    huaweiChooseGpu(atomDeployOccupyNeed, currentClusterSuitGpu);
                } else {
                    //英伟达选卡逻辑
                    nvidiaChooseGpu(atomDeployOccupyNeed, currentClusterSuitGpu);
                }
                workflowTask.success();
            } catch (Exception e) {
                workflowTask.error(e.getMessage());
                throw e;
            }

        } else {
            throw new AiAutomatedException("当前集群未找到符合要求的显卡资源：{}", JSON.toJSONString(gpuConfig));
        }


    }


    /**
     * 构造跨机房gpu申请参数
     *
     * @param gpuType
     * @return
     */
    private MultiClusterAtomGpuApply buildMultiClusterAtomGpuApply(String gpuType) {
        List<String> availableGpuType = new ArrayList<>();
        availableGpuType.add(gpuType);
        HashMap<String, List<String>> gpuMapping = kubernetesConfigProperties.getGpuMapping();
        if (MapUtil.isNotEmpty(gpuMapping) && gpuMapping.containsKey(gpuType)) {
            availableGpuType.addAll(gpuMapping.get(gpuType));
        }
        MultiClusterAtomGpuApply gpuApply = new MultiClusterAtomGpuApply();
        gpuApply.setAvailableGpuType(availableGpuType);
        log.info("multiClusterAtomGpuApply:{}", JSON.toJSONString(gpuApply));
        return gpuApply;
    }


    /**
     * 数据格式转换
     *
     * @param currentClusterGpu
     * @return
     */
    private CurrentClusterSuitGpuResult getCurrentClusterSuitGpu(List<GpuHostItemDTO> currentClusterGpu, GpuChooseConfig gpuConfig) {
        CurrentClusterSuitGpuResult currentClusterSuitGpuResult = new CurrentClusterSuitGpuResult();
        String host = gpuConfig.getHost();
        String physicalGpuId = gpuConfig.getPhysicalGpuId();

        String notHost = gpuConfig.getNotHost();
        String notPhysicalGpuId = gpuConfig.getNotPhysicalGpuId();

        if (CollectionUtil.isNotEmpty(currentClusterGpu)) {
            List<PhysicalGpuDTO> physicalGpus = new ArrayList<>();
            for (GpuHostItemDTO g : currentClusterGpu) {
                if (StrUtil.isNotBlank(host)) {
                    if (!g.getHost().equals(host)) {
                        continue;
                    }
                }
                //排除整个主机
                if (StrUtil.isNotBlank(notHost) && StrUtil.isBlank(notPhysicalGpuId)) {
                    if (g.getHost().equals(notHost)) {
                        continue;
                    }
                }
                if (CollectionUtil.isNotEmpty(g.getDetail())) {
                    for (GpuItemDTO i : g.getDetail()) {
                        if (BooleanUtil.isTrue(i.getForbidden())) {
                            continue;
                        }
                        if (StrUtil.isNotBlank(physicalGpuId)) {
                            if (!i.getPhysicalGpuId().equals(physicalGpuId)) {
                                continue;
                            }
                        }
                        if (StrUtil.isNotBlank(notHost) && StrUtil.isNotBlank(notPhysicalGpuId)) {
                            if (g.getHost().equals(notHost) && i.getPhysicalGpuId().equals(notPhysicalGpuId)) {
                                continue;
                            }
                        }

                        PhysicalGpuDTO physicalGpu0 = PhysicalGpuDTO.convert(i, g);
                        physicalGpus.add(physicalGpu0);
                    }
                }
            }
            currentClusterSuitGpuResult.setList(physicalGpus);
            currentClusterSuitGpuResult.setTotalOver(physicalGpus.stream().mapToInt(t -> Integer.parseInt(t.getFreeMem())).sum());
        } else {
            currentClusterSuitGpuResult.setList(new ArrayList<>());
            currentClusterSuitGpuResult.setTotalOver(0);
        }
        return currentClusterSuitGpuResult;
    }


    /**
     * 所有集群中找到符合要求的最优的资源
     *
     * @param atomDeployOccupyNeed
     * @param gpuConfig
     */
    private String multiCheckAndGetGpuChooseConfig(List<AtomGpuApplyDTO> atomDeployOccupyNeed, GpuChooseConfig gpuConfig, OmInstallWorkflow omInstallWorkflow) {

        //选择卡
        String gpuType = gpuConfig.getChooseGpuType();
        MultiClusterAtomGpuApply gpuApply = buildMultiClusterAtomGpuApply(gpuType);
        //独占资源
        List<AtomGpuApplyDTO> multiGpu = atomDeployOccupyNeed.stream().filter(t -> t.getNumGpus() != null && t.getNumGpus() > 0).collect(Collectors.toList());
        boolean exclusive = CollectionUtil.isNotEmpty(multiGpu);
        gpuApply.setExclusive(exclusive);
        //获取可用从节点
        QueryWrapper<SlaveEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SlaveEntity::getIsDeleted, 0).eq(SlaveEntity::getIsOnline, 1);
        if (roleProperties.getOpenRegisterManual()) {
            queryWrapper.lambda().eq(SlaveEntity::getManaged, 1);
        }

        OmInstallWorkflowTask workflowTask = omInstallWorkflow.buildSimpleTask("集群信息获取", "multiCheckAndGetGpuCheck", OmWorkflowDataBuilderService.buildMultiCheckAndGetGpuCheckInputData(atomDeployOccupyNeed, gpuConfig));
        //选择了机房
        if (StrUtil.isNotBlank(gpuConfig.getMachineGroup())) {
            queryWrapper.lambda().eq(SlaveEntity::getSlaveAddress, gpuConfig.getMachineGroup());
        }
        List<SlaveEntity> list = slaveService.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            log.error("跨机房获取gpu信息，暂时无可用的从节点");
            workflowTask.error("跨机房获取gpu信息，暂时无可用的从节点");
            return "";
        }
        Set<String> allDrainCluster = clusterNodeDrainService.getAllDrainCluster();
        if (CollectionUtil.isNotEmpty(allDrainCluster)) {
            list = list.stream().filter(t -> !allDrainCluster.contains(t.getSlaveMachineCode())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(list)) {
                log.error("跨机房获取gpu信息，暂时无可用的从节点,处于节点驱逐中");
                workflowTask.error("跨机房获取gpu信息，暂时无可用的从节点,处于节点驱逐中");
                return "";
            }
        }
        log.info("multiCheckAndGetGpuChooseConfig 机房信息：{}", JSON.toJSONString(list));
        if (CollectionUtil.isEmpty(atomDeployOccupyNeed)) {
            String slaveUrl = StrUtil.isNotBlank(gpuConfig.getMachineGroup()) ? gpuConfig.getMachineGroup() : CollectionUtil.getFirst(list).getSlaveAddress();
            workflowTask.success(MapUtil.of("slaveAddress", slaveUrl));
            return slaveUrl;
        } else {
            workflowTask.success();
        }
        //从节点获取可用资源信息
        List<OmInstallWorkflowTask> slaveGetGpuInfoForkTask = omInstallWorkflow.buildForkTask("从节点gpu获取Fork", "slaveGetGpuInfo", MapUtil.empty(),
                OmWorkflowDataBuilderService.buildGetGpuForkSubTaskInputData(gpuApply, list));

        List<CompletableFuture<MultiClusterAtomGpuApplyResult>> collect = list.stream().map(slave -> CompletableFutureExpandUtils.orTimeout(CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
            MultiClusterAtomGpuApplyResult multiClusterAtomGpuApplyResult = new MultiClusterAtomGpuApplyResult();
            String slaveAddress = slave.getSlaveAddress();
            multiClusterAtomGpuApplyResult.setSlaveAddress(slaveAddress);
            IEmbedService slaveClient = masterScheduleConfig.getSlaveClient(slaveAddress, "");
            multiClusterAtomGpuApplyResult.setSuite(false);

            OmInstallWorkflowTask workflowTaskSlave = slaveGetGpuInfoForkTask.stream().filter(t -> t.getTaskDefName().equals(slaveAddress)).findFirst().get();
            if (null != slaveClient) {
                try {
                    //获取该类型下资源最大的集群
                    R<MasterGpuDTO> gpuResource = slaveClient.getGpuResource(gpuApply);
                    if (BizResponseEnum.OK.getBizCode().equals(gpuResource.getCode())) {
                        List<GpuHostItemDTO> currentClusterGpu = gpuResource.getData().getList();
                        log.info("{}符合要求的资源信息:{}", slaveAddress, JSON.toJSONString(currentClusterGpu));
                        CurrentClusterSuitGpuResult currentClusterSuitGpu = getCurrentClusterSuitGpu(currentClusterGpu, gpuConfig);
                        if (CollectionUtil.isNotEmpty(currentClusterSuitGpu.getList())) {
                            workflowTaskSlave.addInput("suite", currentClusterSuitGpu.getList());
                            List<AtomGpuApplyDTO> atomGpuApply = abilityConvertMapper.atomDeployOccupyNeedConvert(atomDeployOccupyNeed);
                            if (gpuType.contains(HUAWEI_GPU_TYPE)) {
                                //华为卡选卡逻辑
                                huaweiChooseGpu(atomGpuApply, currentClusterSuitGpu.getList());
                            } else {
                                //华为卡选卡逻辑
                                nvidiaChooseGpu(atomGpuApply, currentClusterSuitGpu.getList());
                            }
                            multiClusterAtomGpuApplyResult.setAtomDeployOccupyResult(atomGpuApply);
                            multiClusterAtomGpuApplyResult.setSuite(true);
                            multiClusterAtomGpuApplyResult.setFree(currentClusterSuitGpu.getTotalOver());
                            workflowTaskSlave.success(MapUtil.of("resource", multiClusterAtomGpuApplyResult));
                        } else {
                            HashMap<String, Object> resource = MapUtil.of("resource", currentClusterGpu);
                            resource.put("error", "未找到符合要求的资源");
                            workflowTaskSlave.error(resource);
                        }
                    } else {
                        workflowTaskSlave.error("获取gpu信息异常：" + gpuResource.getMessage());
                    }
                } catch (Exception e) {
                    log.error("{},跨机房获取gpu信息异常", slaveAddress, e);
                    workflowTaskSlave.error("跨机房获取gpu信息异常：" + e.getMessage());
                }
            } else {
                workflowTaskSlave.error("未找到slaveClient");
            }
            return multiClusterAtomGpuApplyResult;
        }), installThreadPool), 60, TimeUnit.SECONDS, false)).collect(Collectors.toList());

        List<MultiClusterAtomGpuApplyResult> gpus = collect.stream().map(CompletableFuture::join)
                .filter(Objects::nonNull).filter(t -> BooleanUtil.isTrue(t.getSuite())).collect(Collectors.toList());
        OmInstallWorkflowTask workflowTaskRes = omInstallWorkflow.buildJoinTask("集群Gpu资源Join", "multiGpuResult", MapUtil.empty(), slaveGetGpuInfoForkTask.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(gpus)) {
            String error = "跨机房获取gpu信息，未找到符合安装要求的资源";
            log.error(error);
            workflowTaskRes.error(error);
            return "";
        }
        MultiClusterAtomGpuApplyResult multiClusterAtomGpuApplyResult = gpus.stream().max(Comparator.comparingLong(MultiClusterAtomGpuApplyResult::getFree)).get();
        log.info("最终符合要求的资源信息:{}", JSON.toJSONString(multiClusterAtomGpuApplyResult));
        workflowTaskRes.success(MapUtil.of("resource", multiClusterAtomGpuApplyResult));
        List<AtomGpuApplyDTO> atomDeployOccupyResult = multiClusterAtomGpuApplyResult.getAtomDeployOccupyResult();
        for (AtomGpuApplyDTO a : atomDeployOccupyNeed) {
            AtomGpuApplyDTO atomGpuApply = atomDeployOccupyResult.stream().filter(t -> t.getAtomK8sName().equals(a.getAtomK8sName())).findFirst().get();
            PhysicalGpuDTO physicalGpu = atomGpuApply.getPhysicalGpu();
            a.setPhysicalGpu(physicalGpu);
            //独占
            if (exclusive) {
                a.setNeedResource(Integer.parseInt(physicalGpu.getFreeMem()));
            }

        }
        return multiClusterAtomGpuApplyResult.getSlaveAddress();
    }


    /**
     * 英伟达选择最优的卡部署
     *
     * @param atomDeployOccupyNeed
     * @param currentClusterSuitGpu
     */
    private void nvidiaChooseGpu(List<AtomGpuApplyDTO> atomDeployOccupyNeed, List<PhysicalGpuDTO> currentClusterSuitGpu) {

        List<AtomGpuApplyDTO> multiGpu = atomDeployOccupyNeed.stream().filter(t -> t.getNumGpus() != null && t.getNumGpus() > 0).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(multiGpu)) {
            if (multiGpu.size() > 1) {
                throw new AiAutomatedException("多卡部署暂时仅支持单atom");
            }
            //多卡部署
            Integer numGpus = CollectionUtil.getFirst(atomDeployOccupyNeed).getNumGpus();
            Map<String, List<PhysicalGpuDTO>> collect = currentClusterSuitGpu.stream().collect(Collectors.groupingBy(PhysicalGpuDTO::getHost));
            List<PhysicalGpuDTO> physicalGpuChoose = new ArrayList<>();
            for (Map.Entry<String, List<PhysicalGpuDTO>> m : collect.entrySet()) {
                List<PhysicalGpuDTO> value = m.getValue();
                List<PhysicalGpuDTO> empty = value.stream().filter(t -> CollectionUtil.isEmpty(t.getPod())).collect(Collectors.toList());
                if (empty.size() < numGpus) {
                    continue;
                }
                physicalGpuChoose.addAll(CollectionUtil.sub(empty, 0, numGpus));
                break;
            }
            if (CollectionUtil.isEmpty(physicalGpuChoose)) {
                throw new AiAutomatedException("多卡部署资源不足，需要独占显卡数：" + numGpus);
            }

            PhysicalGpuDTO first = CollectionUtil.getFirst(physicalGpuChoose);
            PhysicalGpuDTO gpuDTO = new PhysicalGpuDTO();
            gpuDTO.setMachineGroup(first.getMachineGroup());
            gpuDTO.setHost(first.getHost());
            gpuDTO.setFreeMem(first.getFreeMem());
            gpuDTO.setUsedMem(first.getUsedMem());
            gpuDTO.setGpuId(physicalGpuChoose.stream().map(PhysicalGpuDTO::getGpuId).collect(Collectors.joining(",")));
            log.info("英伟达多卡部署选择卡：{},gpuDTO:{}", JSON.toJSONString(physicalGpuChoose), JSON.toJSONString(gpuDTO));
            for (AtomGpuApplyDTO p : atomDeployOccupyNeed) {
                p.setPhysicalGpu(gpuDTO);
                p.setNeedResource(Integer.parseInt(first.getFreeMem()));
            }

        } else {
            //选择gpu资源最多的id
            PhysicalGpuDTO gpuDTO = currentClusterSuitGpu.stream().max(Comparator.comparingLong(c -> Long.parseLong(c.getFreeMem()))).get();
            int left = Integer.parseInt(gpuDTO.getFreeMem());
            Double mul = NumberUtil.mul((Double) NumberUtil.add(gpuDTO.getFreeMem(), gpuDTO.getUsedMem()).doubleValue(), residualVideoMemory);
            int sum = atomDeployOccupyNeed.stream().mapToInt(AtomGpuApplyDTO::getNeedResource).sum();
            if (sum > left) {
                throw new AiAutomatedException("显存不足,预分配显存为:" + sum + ",剩余显存为:" + gpuDTO.getFreeMem());
            }
            int add = NumberUtil.add(gpuDTO.getUsedMem(), String.valueOf(sum)).intValue();
            if (add > mul.intValue()) {
                throw new AiAutomatedException("显存不足,预分配显存为:" + sum + "已使用：" + gpuDTO.getUsedMem() + ",显存80%为:" + mul.intValue());
            }
            //设置预占用
            for (AtomGpuApplyDTO a : atomDeployOccupyNeed) {
                a.setPhysicalGpu(gpuDTO);
            }
        }

    }


    /**
     * 华为卡选择最优的卡部署
     *
     * @param atomDeployOccupyNeed
     * @param currentClusterSuitGpu
     */
    private void huaweiChooseGpu(List<AtomGpuApplyDTO> atomDeployOccupyNeed, List<PhysicalGpuDTO> currentClusterSuitGpu) {
        //从小到大排序
        currentClusterSuitGpu.sort(Comparator.comparing(PhysicalGpuDTO::getFreeMem));
        atomDeployOccupyNeed.sort(Comparator.comparing(AtomGpuApplyDTO::getNeedResource));
        //需要在同一个物理gpu上
        Map<String, List<PhysicalGpuDTO>> collect = currentClusterSuitGpu.stream().collect(Collectors.groupingBy(PhysicalGpuDTO::getPhysicalGpuId));
        for (Map.Entry<String, List<PhysicalGpuDTO>> m : collect.entrySet()) {
            List<PhysicalGpuDTO> value = m.getValue();
            //单host不满足要求
            if (value.size() < atomDeployOccupyNeed.size()) {
                continue;
            }
            List<String> selectedId = new ArrayList<>();
            for (AtomGpuApplyDTO a : atomDeployOccupyNeed) {
                //筛选出满足显卡资源要求的最小的那个
                Optional<PhysicalGpuDTO> first = value.stream().filter(t -> NumberUtil.sub(t.getFreeMem(), a.getNeedResource().toString()).intValue() > 0)
                        .filter(t -> !selectedId.contains(t.getUuid()))
                        .findFirst();
                if (first.isPresent()) {
                    PhysicalGpuDTO physicalGpuDTO = first.get();
                    a.setPhysicalGpu(physicalGpuDTO);
                    selectedId.add(physicalGpuDTO.getUuid());
                }
            }
            //所有的申请都已匹配跳出
            if (CollectionUtil.isEmpty(atomDeployOccupyNeed.stream().filter(t -> t.getPhysicalGpu() == null).collect(Collectors.toList()))) {
                break;
            }
        }
        if (CollectionUtil.isNotEmpty(atomDeployOccupyNeed.stream().filter(t -> t.getPhysicalGpu() == null).collect(Collectors.toList()))) {
            log.error("未找到满足需求的华为卡gpu,{}", JSON.toJSONString(atomDeployOccupyNeed));
            throw new AiAutomatedException("未找到满足需求的华为卡gpu");
        }

    }


    /**
     * 选择当前节点符合要求的gpu列表
     *
     * @param gpuConfig
     * @return
     */
    private List<PhysicalGpuDTO> getCurrentClusterSuitGpu(GpuChooseConfig gpuConfig, String gpuType, boolean isHuawei, OmInstallWorkflow omInstallWorkflow) {
        OmInstallWorkflowTask workflowTask = omInstallWorkflow.buildSimpleTask("获取gpu信息", "getGpuInfo", MapUtil.of("gpuConfig", gpuConfig));
        //获取当前集群gpu信息
        List<GpuHostItemDTO> gpuInfo = gpuInfoHelper.getGpuInfo(false);
        if (CollectionUtil.isEmpty(gpuInfo)) {
            workflowTask.error("gpu 服务异常");
            throw new AiAutomatedException(gpuConfig.getMachineGroup() + ",获取当前节点gpu信息异常");
        }
        //gpu选择配置
        String physicalGpuId = StrUtil.isBlank(gpuConfig.getPhysicalGpuId()) ? "" : gpuConfig.getPhysicalGpuId();
        String physicalHost = StrUtil.isBlank(gpuConfig.getHost()) ? "" : gpuConfig.getHost();

        String notPhysicalGpuId = StrUtil.isBlank(gpuConfig.getNotPhysicalGpuId()) ? "" : gpuConfig.getNotPhysicalGpuId();
        String notPhysicalHost = StrUtil.isBlank(gpuConfig.getNotHost()) ? "" : gpuConfig.getNotHost();
        //筛选gpu类型
        HashMap<String, List<String>> gpuMapping = kubernetesConfigProperties.getGpuMapping();
        List<PhysicalGpuDTO> physicalGpu = new ArrayList<>();
        for (GpuHostItemDTO gpuHostItemDTO : gpuInfo) {
            //需要过滤物理gpuID
            if (StrUtil.isNotBlank(physicalHost) && !physicalHost.equals(gpuHostItemDTO.getHost())) {
                continue;
            }
            if (StrUtil.isNotBlank(notPhysicalHost) && notPhysicalHost.equals(gpuHostItemDTO.getHost())) {
                continue;
            }
            List<GpuItemDTO> detail = gpuHostItemDTO.getDetail();
            for (GpuItemDTO gpuItemDTO : detail) {
                //删选出去禁用的gpu
                if (BooleanUtil.isTrue(gpuItemDTO.getForbidden())) {
                    continue;
                }
                String physicalGpuId0 = gpuItemDTO.getPhysicalGpuId();
                //需要过滤物理gpuID
                if (StrUtil.isNotBlank(physicalGpuId) && !physicalGpuId.equals(physicalGpuId0)) {
                    continue;
                }
                if (StrUtil.isNotBlank(notPhysicalGpuId) && notPhysicalGpuId.equals(physicalGpuId0)) {
                    continue;
                }
                //跳过gpuType选择
                if (MapUtils.isNotEmpty(gpuMapping)) {
                    List<String> gpuMappings = gpuMapping.get(gpuType);
                    if (CollectionUtils.isNotEmpty(gpuMappings)) {
                        if (!gpuMappings.contains(gpuType) && !gpuType.equals(gpuItemDTO.getGpuType())) {
                            continue;
                        }
                    } else {
                        if (!gpuType.equals(gpuItemDTO.getGpuType())) {
                            continue;
                        }
                    }
                } else {
                    if (!gpuType.equals(gpuItemDTO.getGpuType())) {
                        continue;
                    }
                }
                //华为卡的跳过已安装过算法的卡
                if (BooleanUtil.isTrue(isHuawei)) {
                    if (CollectionUtil.isNotEmpty(gpuItemDTO.getPod())) {
                        continue;
                    }
                }
                if (StrUtil.isNotBlank(gpuConfig.getMachineGroup())) {
                    gpuHostItemDTO.setMachineGroup(gpuConfig.getMachineGroup());
                }
                PhysicalGpuDTO physicalGpu0 = PhysicalGpuDTO.convert(gpuItemDTO, gpuHostItemDTO);
                physicalGpu.add(physicalGpu0);
            }
        }
        if (CollectionUtil.isEmpty(physicalGpu)) {
            workflowTask.error("未找到符合需求的gpu");
        } else {
            workflowTask.success(MapUtil.of("gpuInfo", physicalGpu));
        }
        return physicalGpu;
    }


    @Override
    public String multiClusterOccupyGpu(List<AtomGpuApplyDTO> atomDeployOccupyNeed, GpuChooseConfig gpuConfig, OmInstallWorkflow omInstallWorkflow) {
        RLock occupyLock = redissonClient.getLock(OCCUPY_GPU_KEY);
        try {
            if (occupyLock.tryLock(10, 15, TimeUnit.SECONDS)) {
                log.info("开始进入跨机房occupyGpu流程");
                String slaveAddress = multiCheckAndGetGpuChooseConfig(atomDeployOccupyNeed, gpuConfig, omInstallWorkflow);
                if (StrUtil.isBlank(slaveAddress)) {
                    throw new AiAutomatedException("跨机房获取gpu信息，未找到符合安装要求的资源");
                }
                if (CollectionUtil.isNotEmpty(atomDeployOccupyNeed)) {
                    OmInstallWorkflowTask workflowTask = omInstallWorkflow.buildSimpleTask("预占用gpu", "occupyGpu", OmWorkflowDataBuilderService.buildOccupyGpuInputData(slaveAddress, atomDeployOccupyNeed));
                    //预占用
                    Boolean occupyGpuResult = occupyGpuToGpuService(slaveAddress, atomDeployOccupyNeed);
                    if (!BooleanUtil.isTrue(occupyGpuResult)) {
                        String error = "跨机房occupyGpu出现异常，预占用失败";
                        workflowTask.error(error);
                        throw new AiAutomatedException(error);
                    }
                    workflowTask.success();
                }
                return slaveAddress;
            } else {
                log.error("跨机房occupyGpu出现异常，未抢到占用锁");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (occupyLock.isLocked() && occupyLock.isHeldByCurrentThread()) {
                occupyLock.unlock();
            }
        }
        return "";
    }
}
