package com.vos.task.automated.service.service.impl.ai.omhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.enums.InstallActionEnum;
import com.vos.kernel.common.install.ForkTaskData;
import com.vos.kernel.common.install.OmInstallWorkflowTask;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.AbilityDeployDTO;
import com.vos.task.automated.api.model.dto.AtomDeployDTO;
import com.vos.task.automated.api.model.dto.LicenseDataDto;
import com.vos.task.automated.api.model.dto.ServingDeployDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuHostItemDTO;
import com.vos.task.automated.api.model.dto.k8s.GpuItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.dto.k8s.PodInfoDTO;
import com.vos.task.automated.api.model.dto.om.AtomConfigVo;
import com.vos.task.automated.api.model.dto.om.ImageInfo;
import com.vos.task.automated.api.model.dto.om.ServingConfigVo;
import com.vos.task.automated.api.model.entity.AtomInfo;
import com.vos.task.automated.api.model.entity.ServingInfo;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import com.vos.task.automated.service.common.ServingTypeEnum;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.service.IAbilityOperateService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.automated.service.service.impl.ai.gpu.GpuInfoHelper;
import com.vos.task.automated.service.service.impl.ai.install.AiAbilityCheckApi;
import com.vos.task.automated.service.service.impl.ai.install.BaseDeployOperate;
import com.vos.task.automated.service.utils.k8s.GpuApiService;
import com.vos.task.manage.api.model.entity.TaskApp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.vos.task.automated.service.common.Constants.HUAWEI_GPU_TYPE;

/**
 * python serving的om包处理类，原om包处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PythonServingOmHandler extends AbstractOmAiHandler {


    @Autowired
    BaseDeployOperate baseDeployOperate;
    /**
     * 节点下显存剩余最大占比
     */
    @Value("${deploy.residual-video-memory}")
    private Double residualVideoMemory;

    @Resource
    IAbilityOperateService abilityOperateService;

    @Resource
    AiAbilityCheckApi aiAbilityCheckApi;

    @Resource
    KubernetesConfigProperties kubernetesConfigProperties;

    @Resource
    GpuApiService gpuApiService;


    /**
     * gpu占用监控时长
     */
    @Value("${deploy.checkGpuTokenDuration:60}")
    private Long checkGpuTokenDuration;


    /**
     * 延迟删除
     */
    @Value("${deploy.removeLately:0}")
    private Integer removeLately;

    /**
     * gpu资源预支
     */
    @Value("${deploy.gpuToken:true}")
    private Boolean gpuToken;


    @Autowired
    private GpuInfoHelper gpuInfoHelper;

    public PythonServingOmHandler() {
        type = ServingTypeEnum.PYTHON.name();
    }


    @Override
    protected void doOccupyGpu(InstallPipelineContext context) {
        boolean isV3Serving = BooleanUtil.isTrue(context.getIsV3Serving());
        AbilityApiTypeEnum abilityApiType = context.getAbilityApiType();
        //chat 、向量化算法请求参数不通，占用会异常
        if (isV3Serving || (abilityApiType != null && abilityApiType.getCodeInteger() == 9) || (abilityApiType != null && abilityApiType.getCodeInteger() == 4)) {
            return;
        }
        //大语言模型算法自己占用
        if (AbilityApiTypeEnum.LLM.equals(abilityApiType) || AbilityApiTypeEnum.V3_CHAT.equals(abilityApiType)) {
            return;
        }

        List<String> gpuTokenPods = context.getAtomInfoEntityList().stream().map(c -> c.getK8sName()).collect(Collectors.toList());

        log.info("开始请求算法,占用gpu");
        //老算法手动调用占用gpu
        FileReader fileReader = new FileReader(kubernetesConfigProperties.getPreRequestAiParamsPath());
        JSONObject requestParams = JSONObject.parseObject(fileReader.readString());
        String occupyGpuUrl = context.getOccupyGpuUrl();
        if (StrUtil.isNotBlank(occupyGpuUrl)) {
            OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("请求初始化算法", "requestAbility", MapUtil.of("url", occupyGpuUrl));
            try {
                workflowTask.addInput("gpuToken", gpuToken);
                requestParams.getJSONObject("schedulingCenter").put("linker_ai_debug_flag", "debug_mode_activate");
//                linker_ai_debug_flag
                if ((abilityApiType != null && abilityApiType.getCodeInteger() == 3)) {
                    JSONArray src = requestParams.getJSONArray("src");
                    for (int i = 0; i < src.size(); i++) {
                        JSONObject jsonObject = src.getJSONObject(i);
                        jsonObject.put("kwargs", JSON.parseObject("{\"displayAllBboxes\":false,\"coldActivation\":{\"includeClasses\":[],\"type\":\"\"},\"ommodelv3\":{\"task\":\"识别人\",\"threshold\":0.6,\"label\":\"person\",\"uniqueId\":\"0\",\"labels\":[\"person\"]},\"boxEnlargeSize\":1}"));
                    }
                }
                JSONObject check = aiAbilityCheckApi.check(occupyGpuUrl, requestParams);
                log.debug("占用gpu请求返回结果：{}", JSON.toJSONString(check));
                if (!BooleanUtil.isTrue(gpuToken)) {
                    if (!BooleanUtil.isTrue(check.getBoolean("isSuccess"))) {
                        String errorMsg = check.getJSONObject("body").getString("message");
                        if (removeLately != null && removeLately > 0) {
                            ThreadUtil.safeSleep(removeLately);
                        }
                        workflowTask.error(errorMsg);
                        throw new AiAutomatedException(errorMsg);
                    } else {
                        workflowTask.success();
                    }
                } else {
                    //判断预估值与真实值
                    workflowTask.success();
                }
            } catch (Exception e) {
                if (!BooleanUtil.isTrue(gpuToken)) {
                    workflowTask.error(e.getMessage());
                    throw new AiAutomatedException("算法初始请求失败：" + e.getMessage());
                } else {
                    workflowTask.success(MapUtil.of("requestError", e.getMessage()));
                }
            }

        }

        log.info("请求算法,占用gpu结束");
        //gpu非手动分配下，需验证gpu占用
        if (!BooleanUtil.isTrue(gpuToken) && kubernetesConfigProperties.isVgpuEnable()) {
            //检测算法实时占用gpu情况，是否超出了该物理卡的80%
//            checkRunningGpu(gpuTokenPods, LocalDateTime.now());
        }

        //华为卡部署：休眠5S等待运维gpuInfo接口可查询到
        if (context.getChooseGpuType() != null && context.getChooseGpuType().contains(HUAWEI_GPU_TYPE)) {
            ThreadUtil.safeSleep(5000);
        }
    }

    @Override
    protected void doHealthCheck(InstallPipelineContext context) {
        boolean isV3Serving = BooleanUtil.isTrue(context.getIsV3Serving());
        AbilityApiTypeEnum abilityApiType = context.getAbilityApiType();
        OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("serving健康检查", "servingHealthCheck", MapUtil.of("abilityApiType", abilityApiType));
        //chat 加载过慢跳过健康检查
        if (isV3Serving || (abilityApiType != null && abilityApiType.getCodeInteger() == 4)) {
            workflowTask.success();
            return;
        }
        ServingInfo servingInfo = context.getServingInfo();
        try {
            //等待算法部署状态为active
            baseDeployOperate.healthPodCheck(servingInfo.getK8sName(), context.getDeployTime());
            workflowTask.success();
        } catch (Exception e) {
            workflowTask.error(e.getMessage());
            throw e;
        }

        log.info("健康检查完成,进行下一步骤");
        //atom健康检查
        List<AtomInfo> atomInfoEntityList = context.getAtomInfoEntityList();
        List<OmInstallWorkflowTask> atomHealth = context.getOmInstallWorkflow().buildForkTask("Atom健康检查Fork", "atomHealthCheck", MapUtil.empty(),
                atomInfoEntityList.stream().map(t -> new ForkTaskData(t.getK8sName() + "-health", "健康检查", null)).collect(Collectors.toList()));
        for (AtomInfo a : atomInfoEntityList) {
            OmInstallWorkflowTask workflowSubTask = atomHealth.stream().filter(t -> t.getTaskDefName().contains(a.getK8sName())).findFirst().get();
            try {
                baseDeployOperate.healthPodCheck(a.getK8sName(), context.getDeployTime());
                workflowSubTask.success();
            } catch (Exception e) {
                workflowSubTask.error(e.getMessage());
                throw e;
            }
        }
        OmInstallWorkflowTask workflowAtomHealthTask = context.getOmInstallWorkflow().buildJoinTask("Atom健康检查Join", "atomHealthRes", MapUtil.empty(),
                atomHealth.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));
        workflowAtomHealthTask.success();

    }

    @Override
    protected AbilityDeployDTO buildDeployInfo0(InstallPipelineContext context) {
        AbilityDeployDTO abilityDeployDTO = new AbilityDeployDTO();
        //部署模式下
        abilityDeployDTO.setOmChooseGpuType(context.getChooseGpuType());
        abilityDeployDTO.setServingType(context.getServingType());
        abilityDeployDTO.setAbilityApiType(context.getAbilityApiType());
        context.setAbilityDeployInfo(abilityDeployDTO);
        //构造算子数据
        buildAtomDeployInfo(context, context.getOmInstallInfo().getAtomEnvs());
        //构造serving部署信息
        buildServingDeployInfo(context);
        return abilityDeployDTO;
    }

    @Override
    protected void doDeploy(InstallPipelineContext context) {

        List<String> podInstall = new ArrayList<>();
        List<AtomInfo> atomInfos = new ArrayList<>();
        if (BooleanUtil.isTrue(context.getIsScale())) {
            //扩容
            scaleDeploy(context, atomInfos, podInstall);

        } else {
            //部署
            installDeploy(context, atomInfos, podInstall);
        }
        //跨机房&单机都需要
        context.setDeployTime(LocalDateTime.now());
        context.setAtomInfoEntityList(atomInfos);
        context.setPodDeployNameInstalled(podInstall);
        context.setServiceNameInstalled(podInstall.stream().map(t -> t + CommonConstant.CLUSTER_IP_SUFFIX).collect(Collectors.toList()));

        //非跨机房部署继续构造上下文
        if (!BooleanUtil.isTrue(context.getIsSlave())) {
            buildDataBaseContext0(context);
        }

    }

    @Override
    protected void buildDataBaseContext0(InstallPipelineContext context) {
        //组装route数据
        ServingInfo servingInfo = context.getAbilityDeployInfo().getServingDeployInfo().getServingDeploy();
        String idStr = IdWorker.getIdStr();
        TaskServerRouterEntity taskServerRouterEntity = new TaskServerRouterEntity();
        taskServerRouterEntity.setTaskServerUrl(servingInfo.getServingRequestUrl());
        taskServerRouterEntity.setServingKey(idStr);
        taskServerRouterEntity.setSlavePods(JSON.toJSONString(context.getPodDeployNameInstalled()));
        //configMap
        taskServerRouterEntity.setServingConfigMap(servingInfo.getServingConfigMap());
        //部署信息，扩容时使用
        taskServerRouterEntity.setDeployInfo(JSON.toJSONString(context.getAbilityDeployInfo()));
        taskServerRouterEntity.setSlaveAddress(context.getDeploySlaveAddress());
        //预热阶段
        taskServerRouterEntity.setStatus(2);
        taskServerRouterEntity.setModelTypeInfo(JSON.toJSONString(context.getAbilityModelInfo()));
        context.setTaskServerRouter(taskServerRouterEntity);
        //serving 返回servingKey
        servingInfo.setServingRequestUrl(idStr);
        context.setServingInfo(servingInfo);
    }

    /**
     * 扩容部署
     *
     * @param context
     */
    protected void scaleDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {
        log.info("atom扩容信息构造,当前context：{}", JSON.toJSONString(context));
        atomDeploy(context, atomInfos, podInstall);
        servingDeploy(context, podInstall);
    }

    /**
     * 安装部署
     *
     * @param context
     */
    protected void installDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {
        //非跨机房部署
//        if (!BooleanUtil.isTrue(context.getIsSlave())) {
//            //构造部署信息
//            buildDeployInfo(context);
//        }
        log.info("部署atom,当前context：{}", JSON.toJSONString(context));
        atomDeploy(context, atomInfos, podInstall);
        servingDeploy(context, podInstall);
    }


    /**
     * 部署算子
     *
     * @param context
     */
    private void atomDeploy(InstallPipelineContext context, List<AtomInfo> atomInfos, List<String> podInstall) {
        List<AtomDeployDTO> atomDeployInfos = context.getAbilityDeployInfo().getAtomDeployInfo();
        LinkedHashMap<String, String> atomKeyUrlMap = new LinkedHashMap<>(16);

        if (CollectionUtil.isNotEmpty(atomDeployInfos)) {
//            PhysicalGpuDTO physicalGpuDTO = context.getPhysicalGpuDTO();
//            if (BooleanUtil.isFalse(context.checkIsHuawei())) {
//                if (physicalGpuDTO == null) {
//                    physicalGpuDTO = gpuInfoHelper.choosePhysicalGpu(context.getChooseGpuType(), false, "0");
//                    //校验预分配资源是否够
//                    this.checkPreSignGpu(context.getOmGpuInfo(), physicalGpuDTO.getFreeMem());
//                    context.setPhysicalGpuDTO(physicalGpuDTO);
//                }
//                //跨机房ip
//                if (StrUtil.isNotBlank(context.getDeploySlaveAddress())) {
//                    physicalGpuDTO.setMachineGroup(context.getDeploySlaveAddress());
//                }
//                //atom物理gpu环境变量添加
//                buildAtomPhysicalGpu(atomDeployInfos, Collections.singletonList(physicalGpuDTO), context.getChooseGpuType());
//            } else if (BooleanUtil.isTrue(context.checkIsHuawei())) {
//                //华为卡部署：判断是否传递了部署物理卡的限制
//                List<AtomGpuApplyDTO> atomGpuApply = buildAtomGpuApply(atomDeployInfos);
//                List<PhysicalGpuDTO> physicalGpus = gpuInfoHelper.choosePhysicalGpuHuawei(context.getChooseGpuType(), physicalGpuDTO != null, physicalGpuDTO, atomGpuApply, context.getTokenGpusForHuawei());
//                //跨机房ip
//                if (StrUtil.isNotBlank(context.getDeploySlaveAddress())) {
//                    physicalGpus.forEach(t -> t.setMachineGroup(context.getDeploySlaveAddress()));
//                }
//                if (physicalGpuDTO == null) {
//                    context.setPhysicalGpuDTO(CollectionUtil.getFirst(physicalGpus));
//                }
//                //atom物理gpu环境变量添加
            //buildAtomPhysicalGpu(atomDeployInfos, physicalGpus, context.getChooseGpuType());
//            }
            PhysicalGpuDTO physicalGpuDTO = CollectionUtil.getFirst(atomDeployInfos).getAtomInfo().getPhysicalGpuDTO();
            context.setPhysicalGpuDTO(physicalGpuDTO);
            List<OmInstallWorkflowTask> atomDeploy = context.getOmInstallWorkflow().buildForkTask("Atom部署Fork", "atomDeploy", MapUtil.of("type", "python"),
                    atomDeployInfos.stream().map(t -> new ForkTaskData(t.getAtomInfo().getK8sName() + "-deploy", "Atom部署", null)).collect(Collectors.toList()));

            for (AtomDeployDTO a : atomDeployInfos) {
                String configMapName = a.getConfigMapName();
                AtomInfo atomInfo = a.getAtomInfo();
                AtomConfigVo atomConfig = a.getAtomConfig();
                //第三方算法配置文件
                if (StrUtil.isNotBlank(configMapName) && StrUtil.isNotBlank(a.getConfigMapValue())) {
                    context.getConfigMapInstalled().add(configMapName);
                    abilityOperateService.addConfigmap(configMapName, "conf.env", a.getConfigMapValue());
                }
                atomInfos.add(atomInfo);
                //service
                String atomService = abilityOperateService.addClusterIpService(atomInfo.getK8sName(), atomConfig.getInner_port(), null);
                //替换atom请求地址
                String atomUrl = atomService + atomConfig.getSuffix();
                atomKeyUrlMap.put(atomInfo.getAtomKey(), atomUrl);
                //增加环境变量，算法元数据
                buildEnvMetaData(a.getEnvs(), atomConfig);
                OmInstallWorkflowTask workflowTask = atomDeploy.stream().filter(t -> t.getTaskDefName().contains(atomInfo.getK8sName())).findFirst().get();
                workflowTask.addInput("atomUrl", atomUrl);
                workflowTask.addInput("k8sName", atomInfo.getK8sName());
                workflowTask.addInput("config", a.getAtomConfig());
                workflowTask.addInput("deployHost", a.getDeployedHost());
                workflowTask.addInput("env", a.getEnvs());
                try {
                    //部署
                    abilityOperateService.addAtomPodDeploy(a);
                    workflowTask.success();
                } catch (Exception e) {
                    workflowTask.error(e.getMessage());
                    throw e;
                }
                podInstall.add(atomInfo.getK8sName());
            }
            OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildJoinTask("Atom部署Join", "atomDeployRes", MapUtil.empty(), atomDeploy.stream().map(OmInstallWorkflowTask::getTaskDefName).collect(Collectors.toList()));
            workflowTask.success();
            context.getAbilityDeployInfo().setAtomKeyUrlMap(atomKeyUrlMap);
        }
    }


    /**
     * 扩容时重置serving环境变量
     */
    private void scaleResetServingEnv(InstallPipelineContext context) {
        if (BooleanUtil.isTrue(context.getIsScale()) || BooleanUtil.isTrue(context.getIsRelocate())) {
            List<String> servingEnvs = context.getAbilityDeployInfo().getServingDeployInfo().getEnvs();
            if (CollectionUtil.isNotEmpty(servingEnvs)) {
                ArrayList<String> envs = new ArrayList<>();
                for (String e : servingEnvs) {
                    if (e.contains("--")) {
                        String[] split = e.split("=");
                        String[] splitE = e.split("--");
                        envs.add(split[0] + "=" + splitE[0]);
                    } else {
                        envs.add(e);
                    }
                }
                context.getAbilityDeployInfo().getServingDeployInfo().setEnvs(envs);
            }

        }
    }


    /**
     * 部署serving
     *
     * @param context
     * @param podInstall
     */
    private void servingDeploy(InstallPipelineContext context, List<String> podInstall) {
        ServingDeployDTO servingDeployInfo = context.getAbilityDeployInfo().getServingDeployInfo();
        ServingInfo servingInfo = servingDeployInfo.getServingDeploy();

        OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("serving部署", "servingDeploy", MapUtil.of("atomOnly", context.getAtomOnly()));
        //serving+atom
        if (!BooleanUtil.isTrue(context.getAtomOnly())) {
            HashMap<String, String> atomKeyUrlMap = context.getAbilityDeployInfo().getAtomKeyUrlMap();
            //serving环境变量处理
            scaleResetServingEnv(context);
            List<String> envs = buildServingEnv(servingDeployInfo.getEnvs(), atomKeyUrlMap);
            servingDeployInfo.setEnvs(envs);
            if (StrUtil.isNotBlank(servingDeployInfo.getConfigMapName())) {
                context.getConfigMapInstalled().add(servingDeployInfo.getConfigMapName());
                abilityOperateService.addConfigmap(servingDeployInfo.getConfigMapName(), "vql_factory.yml", servingDeployInfo.getServingYml());
            }
            servingInfo.setServingConfigMap(String.join(",", context.getConfigMapInstalled()));
            ServingConfigVo servingConfig = servingDeployInfo.getServingConfig();

            //atom与serving是否在同一主机
            if (BooleanUtil.isTrue(kubernetesConfigProperties.isServingConnectToAtomHost())) {
                PhysicalGpuDTO physicalGpuDTO = context.getPhysicalGpuDTO();
                if (physicalGpuDTO != null && StrUtil.isNotBlank(physicalGpuDTO.getHost())) {
                    servingDeployInfo.setDeployedHost(physicalGpuDTO.getHost());
                }
                workflowTask.addInput("deployHost", servingDeployInfo.getDeployedHost());
            }
            workflowTask.addInput("config", servingDeployInfo.getServingConfig());
            workflowTask.addInput("env", servingDeployInfo.getEnvs());

            try {
                abilityOperateService.addServingPodDeploy(servingDeployInfo);
                //新增service
                String servingService = abilityOperateService.addClusterIpService(servingInfo.getK8sName(), servingConfig.getInner_port(), null);
                servingInfo.setServingRequestUrl(servingService + servingConfig.getSuffix());
                servingInfo.setServingHealthRequestUrl(servingService + servingConfig.getHealth_check());
                workflowTask.success(MapUtil.of("servingUrl", servingInfo.getServingRequestUrl()));
            } catch (Exception e) {
                workflowTask.error(e.getMessage());
                throw e;
            }
            podInstall.add(servingInfo.getK8sName());
            context.setOccupyGpuUrl(servingInfo.getServingRequestUrl());

        } else {
            //只有atom
            HashMap<String, String> atomKeyUrlMap = context.getAbilityDeployInfo().getAtomKeyUrlMap();
            servingInfo.setServingRequestUrl(CollectionUtil.getFirst(atomKeyUrlMap.values()));
            context.setOccupyGpuUrl(servingInfo.getServingRequestUrl());
            workflowTask.success(MapUtil.of("servingUrl", servingInfo.getServingRequestUrl()));
        }
//        //跨机房扩容
//        if (BooleanUtil.isTrue(context.getIsScale()) && BooleanUtil.isTrue(context.getIsSlave())) {
//            String clusterId = abilityOperateService.getClusterId();
//            String domain = CommonConstant.HTTP_PROTOCOL + clusterId + "-ai.com/";
//            String s = servingInfo.getServingRequestUrl().replaceAll(domain, "");
//            String s1 = StrUtil.subAfter(s, "/", false);
//            servingInfo.setServingRequestUrl("/" + s1);
//        }
    }

    /**
     * 部署serving
     *
     * @param context
     */
    private ServingInfo buildServingDeployInfo(InstallPipelineContext context) {

        LicenseDataDto licenseData = context.getLicenseData();
        TaskApp taskApp = context.getTaskApp();
        // 组装omAppId
        String omAppId = licenseData.getOmVersionId() + "-" + taskApp.getAppKey();
        //获取serving配置信息
        ServingConfigVo servingConfig = context.getOmInstallInfo().getServingConfig();
        String k8sName;
        String servingImage;
        String servingRequestUrl;
        String servingHealthRequestUrl;

        if (BooleanUtil.isTrue(context.getAtomOnly())) {
            servingImage = "";
            //选出atoms第一个作为调用入口
            AtomDeployDTO firstAtomDeploy = CollectionUtil.getFirst(context.getAbilityDeployInfo().getAtomDeployInfo());
            k8sName = firstAtomDeploy.getAtomInfo().getK8sName();
            servingRequestUrl = firstAtomDeploy.getAtomConfig().getSuffix();
            servingHealthRequestUrl = firstAtomDeploy.getAtomConfig().getHealth_check();
            servingConfig.setSuffix(firstAtomDeploy.getAtomConfig().getSuffix());
            servingConfig.setInner_port(firstAtomDeploy.getAtomConfig().getInner_port());
        } else {
            String servingTime = DateUtil.format(LocalDateTime.now(), CommonConstant.DEPLOY_DATA);
            servingImage = servingConfig.getDocker().getImage();
            ImageInfo servingImageInfo = baseDeployOperate.parseImageStr(servingImage);
            String servingImageName = servingImageInfo.getImageName();
            k8sName = baseDeployOperate.getShortName(servingImageName + "-" + servingTime);
            servingRequestUrl = servingConfig.getSuffix();
            servingHealthRequestUrl = servingConfig.getHealth_check();
        }
        //组装数据库ServingInfo信息
        ServingInfo servingInfo = new ServingInfo();
        servingInfo.setTenantId(taskApp.getTenantId())
                .setAbilityCode(licenseData.getOmVersionId())
                .setAppId(taskApp.getAppId())
                .setOmAppId(omAppId)
                .setAppVersionId(licenseData.getAppVersionId())
                .setAppName(licenseData.getAppName())
                .setAction(InstallActionEnum.INSTALL.getValue())
                .setProcessingSpeed(String.valueOf(licenseData.getConcurrent()))
                .setFixedConcurrent(licenseData.getConcurrent())
                .setK8sName(k8sName)
                .setImage(servingImage)
                .setServingRequestUrl(servingRequestUrl)
                .setServingHealthRequestUrl(servingHealthRequestUrl);

        String configMapName = UUID.randomUUID().toString().replaceAll("-", "");
        ServingDeployDTO servingDeployDTO = new ServingDeployDTO(servingInfo, context.getOmInstallInfo().getServingYml(), servingConfig, context.getOmInstallInfo().getEnvsBak(), configMapName, "", null);
        context.getAbilityDeployInfo().setServingDeployInfo(servingDeployDTO);
        if (!BooleanUtil.isTrue(context.getAtomOnly())) {
            //新增
            context.getPodDeployNameInstalled().add(k8sName);
        }
        log.info("serving部署，信息：{}", JSON.toJSONString(servingInfo));
        context.setServingInfo(servingInfo);
        return servingInfo;
    }


    /**
     * 部署atom及对应的DNS
     *
     * @param context
     */
    private void buildAtomDeployInfo(InstallPipelineContext context, String atomEnvs) {

        //atom信息
        JSONObject atomConfig = context.getOmInstallInfo().getAtomConfig();

        JSONObject atoms = atomConfig.getJSONObject("atoms");
        //同个serving下atoms分属于同一组
        String atomGroupId = UUID.randomUUID().toString().replaceAll("-", "");
        List<AtomDeployDTO> atomDeployInfo = new ArrayList<>();
        for (String atomKey : atoms.keySet()) {
            // 组装自动化部署Atom相关信息
            AtomConfigVo atomConfigVo = atoms.getObject(atomKey, AtomConfigVo.class);
            AtomInfo atomInfo = baseDeployOperate.buildSingleAtom(context.getTaskApp(), atomKey, atomConfigVo);
            atomInfo.setGroupId(atomGroupId);
            //Atom部署调度
            baseDeployOperate.atomResourceCalculate(atomKey, atomConfigVo, atomInfo, context);

            List<String> atomEnvList = new ArrayList<>();
            //atom环境变量读取
            if (MapUtil.isNotEmpty(atomConfigVo.getEnv())) {
                for (Map.Entry<String, String> e : atomConfigVo.getEnv().entrySet()) {
                    String key = e.getKey();
                    String value = e.getValue();
                    atomEnvList.add(key + "=" + value);
                }
            }
            // 部署Atom
            AtomDeployDTO atomDeployDTO = new AtomDeployDTO(atomInfo, atomConfigVo, atomEnvList, atomEnvs, context.getChooseGpuType(), false, null, null);
            atomDeployInfo.add(atomDeployDTO);
            context.getAtomInfoEntityList().add(atomInfo);
            //新增
            context.getPodDeployNameInstalled().add(atomInfo.getK8sName());
        }
        //设置部署信息
        context.getAbilityDeployInfo().setAtomDeployInfo(atomDeployInfo);

    }

    /**
     * 检测算法实时占用gpu情况，是否超出了该物理卡的80%
     * 如果超过了直接抛出异常：算力不足；记录当前使用情况
     */
    private void checkRunningGpu(List<String> gpuTokenPods, LocalDateTime checkBeginTime) {
        if (CollectionUtil.isEmpty(gpuTokenPods)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        long between = LocalDateTimeUtil.between(checkBeginTime, now, ChronoUnit.SECONDS);
        if (between > checkGpuTokenDuration) {
            return;
        }
        List<GpuHostItemDTO> clusterGpuInfo = gpuApiService.getClusterGpuInfo(kubernetesConfigProperties.isVgpuEnable() ? kubernetesConfigProperties.getGpuInfoUrl() : kubernetesConfigProperties.getPhysicsGpuInfoUrl());
        log.debug("gpu获取信息:{}", JSON.toJSONString(clusterGpuInfo));
        if (CollectionUtil.isEmpty(clusterGpuInfo)) {
            throw new AiAutomatedException("gpu获取信息错误");
        }
        for (GpuHostItemDTO g : clusterGpuInfo) {
            List<GpuItemDTO> detail = g.getDetail();
            if (CollectionUtil.isNotEmpty(detail)) {
                for (GpuItemDTO gi : detail) {
                    List<PodInfoDTO> podNames = gi.getPod();
                    if (CollectionUtil.isNotEmpty(podNames)) {
                        for (PodInfoDTO p : podNames) {
                            for (String t : gpuTokenPods) {
                                if (p.getPodName().contains(t) && StrUtil.isNotBlank(gi.getUsedMem())) {
                                    //判断是否使用的Gpu超过了配置
                                    BigDecimal total = NumberUtil.add(gi.getFreeMem(), gi.getUsedMem());
                                    int compare = NumberUtil.div(gi.getUsedMem(), total.toString())
                                            .compareTo(new BigDecimal(residualVideoMemory.toString()));
                                    if (compare > 0) {
                                        log.error("部署算力不足，pod：{},已占用：{},总量：{},实时算力：{}", t,
                                                gi.getUsedMem(),
                                                total,
                                                JSON.toJSONString(clusterGpuInfo));
                                        throw new AiAutomatedException("实时算力不足");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        ThreadUtil.safeSleep(2000);
        checkRunningGpu(gpuTokenPods, checkBeginTime);
    }

}
