package com.vos.task.automated.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 部署相关配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/23
 * @description: com.vos.task.automated.service.utils.k8s
 */
@Data
@Component
@ConfigurationProperties(prefix = "deploy")
@RefreshScope
public class DeployConfigProperties {

    /**
     * 健康检查时间
     */
    private Long deployHealthDuration;


    /**
     * gpu检测时间
     */
    private Long checkGpuTokenDuration;


    /**
     * 是否检测基础模型
     */
    private Boolean checkBaseModel = true;

    /**
     * 九头蛇模式使用ingress 进行负载；false时使用nodePort 使用svc负载
     */
    private Boolean hydraUseIngress = true;


    /**
     * om包留存时间
     */
    private Integer omLeftDay;
    /**
     * servinghttp地址ip:端口
     */
    private String servingHttpHost;

    /**
     * serving端口
     */
    private String servingNameSpace = "serving";
    /**
     * 扩缩容 类型 0不扩容 1 自动扩容 2手动扩容
     */
    private Integer scaleType = 2;
    /**
     * 扩容最大值
     */
    private Integer scaleMax = -1;

    /**
     * 扩容批量
     */
    private Integer scaleBatchSize = 5;

    private Integer podListNum = 500;
}
