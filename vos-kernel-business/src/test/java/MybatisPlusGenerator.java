import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/23
 * @description: com.hzlh.gb28181
 */
public class MybatisPlusGenerator {

    public static final String moduleName = "vos-kernel-business";
    public static final String packageConfig = "com.vos.kernel.business";
    public static final String separator = "/";

    /**
     * 数据源配置
     */
    private static final DataSourceConfig.Builder DATA_SOURCE_CONFIG = new DataSourceConfig
            .Builder("*****************************************", "root", "Ff1z@TOFr^iwd%Ra");


    public static void main(String[] args) {
        FastAutoGenerator.create(DATA_SOURCE_CONFIG)
                .globalConfig((scanner, builder) -> builder
                        // （重要）配置输出的文件夹，springboot项目可以使用如下方式
                        .outputDir(System.getProperty("user.dir") + separator + moduleName + separator  + "/src/main/java")
                        // （重要）时间类型，看你喜欢用sql包中的Date、
                        // util包中的Date还是更新的LocalDateTime
                        .dateType(DateType.TIME_PACK)
                        // 配置生成文件中的author
                        .author(scanner.apply("请输入作者名称"))
                        // 是否启用kotlin模式
//                            .enableKotlin()
                        // 是否启用swagger，比如启用后会在entity中自动生成字段ApiModel注释等等
//                            .enableSwagger()
                        // 注释日期的格式
                        .commentDate("yyyy-MM-dd")
                        .build())
                .packageConfig(builder -> builder.parent(packageConfig)
                        .pathInfo(Collections.singletonMap(OutputFile.xml, System.getProperty("user.dir") +
                                separator + moduleName  + "/src/main/resources/mapper"))
                        // 设置父包名:一般情况下使用默认即可；对于不需要的可生成在delete中，后期移除
                        .service("service.batisplus")
                        .serviceImpl("service.batisplus.impl")
                        .controller("controller"))
                .strategyConfig((scanner, builder) -> builder
                        .addInclude(scanner.apply("请输入表名，多个表名用,隔开").split(","))
                        // 设置过滤表前缀
                        .addTablePrefix("t_", "tb_")
                        // 跳过视图的生成
                        .enableSkipView()
                        .entityBuilder()
                        // （重要）主键模式，这里设置自动模式，配合mysql的自增主键
                        .idType(IdType.AUTO)
                        .enableLombok()
                        //去除is头
                        .enableRemoveIsPrefix()
                        // entity文件名，这里配置后面统一加Entity后缀
                        .formatFileName("%sEntity")
                        // activeRecord模式，使用上来说就是
                        // 可以直接在entity对象上执行insert、update等操作
//                        .enableActiveRecord()
                        // 添加tableField注解
                        .enableTableFieldAnnotation()
                        // 自动填充字段
                        .addTableFills(Arrays.asList(
                                new Column("create_time", FieldFill.INSERT),
                                new Column("update_time", FieldFill.INSERT_UPDATE)
                        ))
                        .build())
                .strategyConfig(builder -> builder
                        .mapperBuilder()
                        .enableMapperAnnotation()
                        .enableBaseColumnList()
                        .enableBaseResultMap().build()
                )
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
