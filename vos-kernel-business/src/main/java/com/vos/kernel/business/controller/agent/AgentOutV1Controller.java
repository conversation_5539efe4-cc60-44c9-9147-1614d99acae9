package com.vos.kernel.business.controller.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.enums.RespCodeEnum;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.dto.agent.AgentDetailInfoDTO;
import com.vos.kernel.business.dto.agent.AgentInfoDTO;
import com.vos.kernel.business.dto.agent.ListInfoQueryDTO;
import com.vos.kernel.business.service.agent.IAgentManagementService;
import com.vos.kernel.common.dubbo.DubboDataContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/agent/v1")
public class AgentOutV1Controller {
    @Resource
    private IAgentManagementService agentManagementService;

    /**
     *  agent列表
     * @param queryDTO  agent列表查询DTO
     * @return  agent列表
     */
    @Token
    @GetMapping("/agents")
    public BaseResp<IPage<AgentInfoDTO>> getAgentList(
            ListInfoQueryDTO queryDTO,
            @RequestHeader(value = "tenantId", required = false) String tenantId) {

        queryDTO.setTenantId(DubboDataContext.getAuthIdHolder());

        IPage<AgentInfoDTO> page = agentManagementService.getAgentList(queryDTO);

        return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), page);
    }

    @Token
    @GetMapping("/{agentId}/info")
    public BaseResp<AgentDetailInfoDTO> getAgentDetail(@PathVariable @NotBlank String agentId) {
        try {
            AgentDetailInfoDTO agentDetailInfoDTO = agentManagementService.getAgentDetail(DubboDataContext.getAuthIdHolder(), agentId);
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), agentDetailInfoDTO);
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取智能体详情失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "获取智能体详情失败");
        }
    }
}
