package com.vos.kernel.business.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/12/11 10:46
 * @DESC: 聊天基础信息
 */
@Data
public class ChatBaseDTO implements Serializable {
    /**
     * 会话ID
     * 必选字段，用于标识会话
     */
    private String conversationId;

    /**
     * 对话ID
     * 必选字段，用于标识对话
     */
    private String chatId;

    /**
     * 智能体ID
     * 必选字段，用于标识具体的智能体
     */
    private String agentId;

    /**
     * 对话开始时间
     * 必选字段，使用时间戳表示
     */
    private Long createTime;

    /**
     * 对话结束时间
     * 必选字段，使用时间戳表示
     */
    private Long endTime;
}
