package com.vos.kernel.business.service.appconfig;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.basic.exception.BusinessException;
import com.linker.core.utils.IdWorker;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.entity.TaskTypeConfigAiEntity;
import com.vos.kernel.business.entity.TaskTypeConfigCodeEntity;
import com.vos.kernel.business.service.batisplus.ITaskTypeConfigAiService;
import com.vos.kernel.business.service.batisplus.ITaskTypeConfigCodeService;
import com.vos.kernel.business.vo.TypeInventedSub;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.AiAdvancedConfigEnum;
import com.vos.task.automated.api.model.entity.ScaleHistoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年07月25日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class ModelConfigDesignServiceImpl implements IModelConfigDesignService {

    @Resource
    ITaskTypeConfigCodeService taskTypeConfigCodeService;

    @Resource
    ITaskTypeConfigAiService taskTypeConfigAiService;


    @Override
    @Cached(name = "cache:modelConfigCodeInit", expire = 300, cacheType = CacheType.BOTH)
    public List<AppConfigDTO> getModelInitConfig(Integer taskTypeId) {
        List<AppConfigDTO> appConfigs = new ArrayList<>();
        QueryWrapper<TaskTypeConfigAiEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskTypeConfigAiEntity::getConfig).eq(TaskTypeConfigAiEntity::getTaskTypeId, taskTypeId).last(" limit 1");

        TaskTypeConfigAiEntity one = taskTypeConfigAiService.getOne(queryWrapper);
        if (one != null && StrUtil.isNotBlank(one.getConfig())) {
            try {
                return JSONArray.parseArray(one.getConfig(), AppConfigDTO.class);
            } catch (Exception e) {
                log.error("解析modelInitConfig fail", e);
            }
        }
        return appConfigs;
    }

    @Override
    @CacheInvalidate(name = "cache:modelConfigCodeInit")
    public void deleteModelInitConfigCache(String taskTypeId) {
        log.info("开始删除deleteModelInitConfigCache：{}", taskTypeId);
    }

    @Override
    @Cached(name = "cache:modelConfigCodeCustom", expire = 300, cacheType = CacheType.BOTH)
    public List<AppConfigThirdContainsResponse> getModelCustomConfig(String configCode) {
        List<AppConfigThirdContainsResponse> appConfigThirdContainsResponses = new ArrayList<>();
        if (StrUtil.isBlank(configCode)) {
            return appConfigThirdContainsResponses;
        }

        QueryWrapper<TaskTypeConfigCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskTypeConfigCodeEntity::getAppConfig).eq(TaskTypeConfigCodeEntity::getConfigCode, configCode)
                .eq(TaskTypeConfigCodeEntity::getIsDel, 0).last(" limit 1");
        TaskTypeConfigCodeEntity one = taskTypeConfigCodeService.getOne(queryWrapper);
        if (null != one) {
            return JSONArray.parseArray(one.getAppConfig(), AppConfigThirdContainsResponse.class);
        }
        return appConfigThirdContainsResponses;
    }

    @Override
    @Cached(name = "cache:modelConfigCodeCustomName", expire = 300, cacheType = CacheType.BOTH)
    public ModelCustomConfigDTO getModelCustomConfigWithName(String configCode) {
        ModelCustomConfigDTO modelCustomConfig = new ModelCustomConfigDTO();
        if (StrUtil.isBlank(configCode)) {
            return modelCustomConfig;
        }

        QueryWrapper<TaskTypeConfigCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskTypeConfigCodeEntity::getAppConfig, TaskTypeConfigCodeEntity::getConfigName,
                        TaskTypeConfigCodeEntity::getConfigRemark).eq(TaskTypeConfigCodeEntity::getConfigCode, configCode)
                .eq(TaskTypeConfigCodeEntity::getIsDel, 0).last(" limit 1");
        TaskTypeConfigCodeEntity one = taskTypeConfigCodeService.getOne(queryWrapper);
        if (null != one) {
            modelCustomConfig.setConfigName(one.getConfigName());
            modelCustomConfig.setConfigRemark(one.getConfigRemark());
            modelCustomConfig.setAppConfig(JSONArray.parseArray(one.getAppConfig(), AppConfigThirdContainsResponse.class));
        }
        return modelCustomConfig;
    }

    @Override
    @CacheInvalidate(name = "cache:modelConfigCodeCustom")
    public void deleteModelCustomConfigCache(String configCode) {
        log.info("开始删除ModelCustomConfigCache：{}", configCode);
    }

    @Override
    @CacheInvalidate(name = "cache:modelConfigCodeCustomName")
    public void deleteModelCustomConfigNameCache(String configCode) {
        log.info("开始删除ModelCustomConfigNameCache：{}", configCode);
    }

    /**
     * 检查并更新配置
     *
     * @param appConfig
     * @param originConfig
     */
    private void checkAppConfig(List<AppConfigCreateUpdateItemRequest> appConfig, List<AppConfigThirdContainsResponse> originConfig) {

        if (CollectionUtil.isNotEmpty(appConfig)) {
            Map<String, AppConfigCreateUpdateItemRequest> collect = appConfig.stream().collect(Collectors.toMap(AppConfigCreateUpdateItemRequest::getConfigKey, x -> x, (var1, var2) -> var1));
            for (AppConfigThirdContainsResponse a : originConfig) {
                if (collect.containsKey(a.getConfigKey())) {
                    String configType = a.getConfigType();
                    String configValue = collect.get(a.getConfigKey()).getConfigValue();
                    if (AiAdvancedConfigEnum.INPUT_NUMBER.getCode().equals(configType)) {
                        AppConfigControlValidate controlValidate = a.getControlValidate();
                        String min = controlValidate.getMin();
                        String max = controlValidate.getMax();
                        boolean greaterMin = NumberUtil.isGreaterOrEqual(new BigDecimal(configValue), new BigDecimal(min));
                        if (!greaterMin) {
                            throw new BusinessException(configValue + "不符合配置规则，需大于最小值：" + min);
                        }
                        boolean lessMax = NumberUtil.isLessOrEqual(new BigDecimal(configValue), new BigDecimal(max));
                        if (!lessMax) {
                            throw new BusinessException(configValue + "不符合配置规则，需小于最大值：" + max);
                        }
                        a.setConfigValue(configValue);
                        a.setCustom(true);
                    } else if (AiAdvancedConfigEnum.SINGLE_SELECT.getCode().equals(configType)) {
                        JSONArray controlOptionList = a.getControlOptionList();
                        if (!controlOptionList.contains(configValue)) {
                            throw new BusinessException(configValue + "不符合配置规则，controlOptionList：" + JSON.toJSONString(controlOptionList));
                        } else {
                            a.setConfigValue(configValue);
                            a.setCustom(true);
                        }
                    } else {
                        a.setConfigValue(configValue);
                        a.setCustom(true);
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createModelConfig(AppConfigCreateRequest appConfigCreateRequest, TaskTypeEntity userAbility, List<AppConfigThirdContainsResponse> originConfig) {

        List<AppConfigCreateUpdateItemRequest> appConfig = appConfigCreateRequest.getAppConfig();
        //检查并更新配置
        checkAppConfig(appConfig, originConfig);

        //新增数据
        TaskTypeConfigCodeEntity taskTypeConfigCodeEntity = new TaskTypeConfigCodeEntity();
        String configCode = String.valueOf(IdWorker.nextId());

        taskTypeConfigCodeEntity.setConfigCode(configCode);
        taskTypeConfigCodeEntity.setConfigName(appConfigCreateRequest.getConfigName());
        taskTypeConfigCodeEntity.setConfigRemark(appConfigCreateRequest.getConfigRemark());
        taskTypeConfigCodeEntity.setTaskTypeId(userAbility.getId().longValue());
        taskTypeConfigCodeEntity.setAppConfig(JSONArray.toJSONString(originConfig));
        taskTypeConfigCodeService.save(taskTypeConfigCodeEntity);

        return configCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateModelConfig(AppConfigUpdateRequest appConfigUpdateRequest, TaskTypeEntity userAbility, List<AppConfigThirdContainsResponse> modelCustomConfig) {
        List<AppConfigCreateUpdateItemRequest> appConfig = appConfigUpdateRequest.getAppConfig();
        //检查并更新配置
        checkAppConfig(appConfig, modelCustomConfig);

        TaskTypeConfigCodeEntity taskTypeConfigCodeEntity = new TaskTypeConfigCodeEntity();
        taskTypeConfigCodeEntity.setAppConfig(JSONArray.toJSONString(modelCustomConfig));
        if (StrUtil.isNotBlank(appConfigUpdateRequest.getConfigName())) {
            taskTypeConfigCodeEntity.setConfigName(appConfigUpdateRequest.getConfigName());
        }
        if (StrUtil.isNotBlank(appConfigUpdateRequest.getConfigRemark())) {
            taskTypeConfigCodeEntity.setConfigRemark(appConfigUpdateRequest.getConfigRemark());
        }
        //更新配置
        QueryWrapper<TaskTypeConfigCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TaskTypeConfigCodeEntity::getConfigCode, appConfigUpdateRequest.getConfigCode());
        taskTypeConfigCodeService.update(taskTypeConfigCodeEntity, queryWrapper);
        return appConfigUpdateRequest.getConfigCode();
    }

    @Override
    public Page<AppConfigListItem> getCustomAppConfigList(TaskTypeEntity userAbility, AppConfigListRequest appConfigListRequest) {

        QueryWrapper<TaskTypeConfigCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskTypeConfigCodeEntity::getConfigCode, TaskTypeConfigCodeEntity::getConfigRemark, TaskTypeConfigCodeEntity::getConfigName, TaskTypeConfigCodeEntity::getUpdateTime)
                .eq(TaskTypeConfigCodeEntity::getTaskTypeId, userAbility.getId())
                .eq(TaskTypeConfigCodeEntity::getIsDel, 0);
        if (StrUtil.isNotBlank(appConfigListRequest.getConfigName())) {
            queryWrapper.lambda().like(TaskTypeConfigCodeEntity::getConfigName, appConfigListRequest.getConfigName());
        }
        if (StrUtil.isNotBlank(appConfigListRequest.getConfigRemark())) {
            queryWrapper.lambda().like(TaskTypeConfigCodeEntity::getConfigRemark, appConfigListRequest.getConfigRemark());
        }
        if (StrUtil.isNotBlank(appConfigListRequest.getConfigCode())) {
            queryWrapper.lambda().like(TaskTypeConfigCodeEntity::getConfigCode, appConfigListRequest.getConfigCode());
        }
        queryWrapper.lambda().orderByDesc(TaskTypeConfigCodeEntity::getUpdateTime);
        // 创建分页对象
        Page<TaskTypeConfigCodeEntity> page = new Page<>(appConfigListRequest.getPage(), appConfigListRequest.getSize());
        Page<TaskTypeConfigCodeEntity> pageResult = taskTypeConfigCodeService.page(page, queryWrapper);

        Page<AppConfigListItem> res = new Page<>(appConfigListRequest.getPage(), appConfigListRequest.getSize());

        if (pageResult != null && CollectionUtil.isNotEmpty(pageResult.getRecords())) {
            List<AppConfigListItem> collect = pageResult.getRecords().stream().map(t -> new AppConfigListItem(t.getConfigCode(), t.getConfigName(), t.getConfigRemark(), LocalDateTimeUtil.formatNormal(t.getUpdateTime())))
                    .collect(Collectors.toList());
            res.setTotal(pageResult.getTotal());
            res.setRecords(collect);
        } else {
            res.setTotal(0);
            res.setRecords(new ArrayList<>());
        }
        return res;
    }

    @Override
    public Boolean deleteModelCustomConfig(String configCode) {
        TaskTypeConfigCodeEntity taskTypeConfigCodeEntity = new TaskTypeConfigCodeEntity();
        taskTypeConfigCodeEntity.setIsDel(1);
        QueryWrapper<TaskTypeConfigCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TaskTypeConfigCodeEntity::getConfigCode, configCode).eq(TaskTypeConfigCodeEntity::getIsDel, 0);
        return taskTypeConfigCodeService.update(taskTypeConfigCodeEntity, queryWrapper);
    }
}
