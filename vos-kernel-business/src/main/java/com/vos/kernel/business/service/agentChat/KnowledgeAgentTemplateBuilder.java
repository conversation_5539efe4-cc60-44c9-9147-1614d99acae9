package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.dto.ModelConfigurationDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.agent.KnowledgeAgentRequest;
import com.vos.kernel.business.dto.agent.KnowledgeAgentTemplateDTO;
import com.vos.kernel.business.entity.AgentInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年06月05日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class KnowledgeAgentTemplateBuilder implements ITemplateChatBuilderService {

    private final static Integer TEMPLATE_TYPE = 6;

    @Resource
    TemplateChatBuilder templateChatBuilder;

    @PostConstruct
    public void init() {
        templateChatBuilder.registerTemplateChatBuilder(TEMPLATE_TYPE, this);
    }

    @Override
    public void buildTemplateParamsInner(Integer templateType, ModelConfigurationDTO modelConfiguration, Map<String, Object> metaData) {

    }

    @Override
    public void buildTemplateParams(StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity) {
        if (streamChatRequest.getMetaData() == null) {
            throw new BusinessException("知识库模板参数metaData为空");
        }
        Object knowledgeAgentData = streamChatRequest.getMetaData().get("knowledgeAgentData");
        if (knowledgeAgentData == null) {
            throw new BusinessException("知识库模板参数metaData.knowledgeAgentData为空");
        }

        KnowledgeAgentRequest knowledgeAgentRequest = JSON.parseObject(JSON.toJSONString(knowledgeAgentData), KnowledgeAgentRequest.class);

        String templateParams = agentInfoEntity.getTemplateParams();
        KnowledgeAgentTemplateDTO knowledgeAgentTemplateDTO = JSON.parseObject(templateParams, KnowledgeAgentTemplateDTO.class);
        knowledgeAgentTemplateDTO.setInputSetting(null);

        //根据传递的参数替换knowledgeConfig信息
        if (CollectionUtil.isNotEmpty(knowledgeAgentRequest.getDocGroupIds()) && null != knowledgeAgentTemplateDTO.getKnowledgeConfig()) {
            knowledgeAgentTemplateDTO.getKnowledgeConfig().setDocGroupIds(knowledgeAgentRequest.getDocGroupIds());
        }
        if (CollectionUtil.isNotEmpty(knowledgeAgentRequest.getFaqGroupIds()) && null != knowledgeAgentTemplateDTO.getKnowledgeConfig()) {
            knowledgeAgentTemplateDTO.getKnowledgeConfig().setFaqGroupIds(knowledgeAgentRequest.getFaqGroupIds());
        }
        if (CollectionUtil.isNotEmpty(knowledgeAgentRequest.getTableGroupIds()) && null != knowledgeAgentTemplateDTO.getKnowledgeConfig()) {
            knowledgeAgentTemplateDTO.getKnowledgeConfig().setTableGroupIds(knowledgeAgentRequest.getTableGroupIds());
        }

        //设置图片列表｜文件列表
        if (CollectionUtil.isNotEmpty(knowledgeAgentRequest.getImages())) {
            knowledgeAgentTemplateDTO.setImages(knowledgeAgentRequest.getImages());
        }
        if (CollectionUtil.isNotEmpty(knowledgeAgentRequest.getResList())) {
            knowledgeAgentTemplateDTO.setResList(knowledgeAgentRequest.getResList());
        }

        //知识智能体独有字段
        streamChatRequest.getMetaData().put("knowledgeAgentData", knowledgeAgentTemplateDTO);

    }
}
