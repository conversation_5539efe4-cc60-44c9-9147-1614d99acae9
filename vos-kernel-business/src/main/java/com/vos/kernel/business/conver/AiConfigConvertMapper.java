package com.vos.kernel.business.conver;

import com.vos.kernel.business.dto.AppConfigDTO;
import com.vos.kernel.business.params.*;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.dto.params.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/13
 * @description: com.vos.kernel.business.conver
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface AiConfigConvertMapper {

    /**
     * 获取配置信息参数转换
     *
     * @param params
     * @return
     */
    AiConfigGetDTO paramsToDto(AiConfigGetParams params);

    /**
     * 业务侧获取配置信息参数转换
     *
     * @param params
     * @return
     */
    AbilityConfigDTO paramsToDto(AiConfigCommonForBiz params);

    /**
     * 编辑参数转换
     *
     * @param params
     * @return
     */
    @Mapping(target = "selfLearningConfig", source = "selfLearnConfig")
    AiEditConfigDTO editParamsToDto(AiConfigEditAllParams params);

    /**
     * AI效果预览参数转换
     *
     * @param params
     * @return
     */
    @Mapping(source = "abilityCode", target = "abilityId")
    AiPreViewCallDTO sendToAiParamsToDto(AiAbilityCallParams params);

    /**
     * AI效果预览 ai配置参数转换
     *
     * @param params
     * @return
     */
    AbilityConfigDTO abilityConfigParamsToDto(AbilityConfigCallParams params);

    /**
     * 基础配置转换
     *
     * @param params
     * @return
     */
    AbilityBasicConfigParamsDTO basicConfigParamsToDTo(AbilityBasicConfigParams params);

    /**
     * 协同过滤
     *
     * @param params
     * @return
     */
    AiTargetAvoidIncludeConfigDTO targetConfigParamsToDto(AiTargetAvoidIncludeConfigParams params);


    /**
     * 识别大模型
     *
     * @param params
     * @return
     */
    IdentificationConfigParamsDTO identificationConfigParamsToDto(IdentificationConfigParams params);

    LargeModelFilterConfigParamsDTO largeModelFilterConfigToDTo(LargeModelFilterConfigParams params);

    List<LargeModelFilterConfigParamsDTO> largeModelFilterConfigToDTo(List<LargeModelFilterConfigParams> params);

    /**
     * 语义配置参数
     *
     * @param params
     * @return
     */
    SynergyConfigParamsDTO synergyConfigParamsToDto(SynergyConfigParams params);

    /**
     * ai内部参数配置
     *
     * @param params
     * @return
     */
    AbilityAdvancedInternalConfigParamsDTO advancedInternalConfigParamsToDto(AbilityAdvancedInternalConfigParams params);

    List<AbilityAdvancedInternalConfigParamsDTO> advancedInternalConfigParamsToDto(List<AbilityAdvancedInternalConfigParams> params);

    /**
     * 冷启动配置参数
     *
     * @param params
     * @return
     */
    ColdStartAbilityCallParamsDTO coldStartAbilityCallParamsToDto(ColdStartAbilityCallParams params);

    List<ColdStartAbilityCallParamsDTO> coldStartAbilityCallParamsToDto(List<ColdStartAbilityCallParams> params);

    /**
     * 任务相关配置
     *
     * @param params
     * @return
     */
    AiRuntimeJobConfigParamsDTO aiRuntimeJobConfigParamsToDto(AiRuntimeJobConfigParams params);

    /**
     * 运行时段
     *
     * @param params
     * @return
     */
    JobRuntimeParamsDTO jobRuntimeParamsToDto(JobRuntimeParams params);

    List<JobRuntimeParamsDTO> jobRuntimeParamsToDto(List<JobRuntimeParams> params);

    /**
     * 选择识别区域
     *
     * @param params
     * @return
     */
    AiSelectRegionsParamsDTO aiSelectRegionsParamsToDto(AiSelectRegionsParams params);

    /**
     * 自我学习配置
     *
     * @param params
     * @return
     */
    AbilityOlProcessDTO abilityOlProcessToDto(AbilityOlProcessParams params);

    AiConfigForBizDTO aiConfigForBizParamsToDTO(AiConfigForBizParams params);


    List<AppConfigDTO> modelInitConfigConvet(List<AppConfigDTO> appConfigDTOList);

    AppConfigDTO modelInitConfigConvet(AppConfigDTO appConfigDTOList);
}
