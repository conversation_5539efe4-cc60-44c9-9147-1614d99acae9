package com.vos.kernel.business.service.conversation;

import com.alibaba.spring.core.convert.support.ConversionServiceResolver;
import com.vos.kernel.business.dto.CreateConversationRequest;
import com.vos.kernel.business.dto.CreateConversationResponse;

/**
 * <AUTHOR>
 * @Date 2024/12/10 08:34
 * @DESC: 会话接口
 */
public interface IConversationService {

    /**
     * 会话创建接口
     * @param request
     * @return
     */
    CreateConversationResponse createConversation(CreateConversationRequest request);
}
