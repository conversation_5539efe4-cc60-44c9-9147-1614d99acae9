package com.vos.kernel.business.service.agentChat;

import com.vos.kernel.business.dto.StreamMessageDTO;
import com.vos.kernel.common.enums.ChatEventEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/10 17:45
 * @DESC: 会话处理器
 */
@Slf4j
@Data
public class ConversationProcessor {

    /**
     * 智能体id
     */
    private String agentId;

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * sse流式通道
     */
    private SseEmitter sseEmitter;

    /**
     * 最后消息时间
     */
    private long lastMsgTime = System.currentTimeMillis();

    /**
     * workflow运行id
     */
    private String workflowRid;

    private long timeouts;

    private String chatId;

    /**
     * 流式消息历史，保证顺序
     */
    private List<StreamMessageDTO> messageHistory = new ArrayList<>();

    ConversationProcessor(String agentId, String conversationId, long timeouts) {
        this.agentId = agentId;
        this.conversationId = conversationId;
        this.timeouts = timeouts;
        this.sseEmitter = new SseEmitter(timeouts);
    }

    /**
     * sse消息发送
     *
     * @param message
     * @return
     */
    public void sendMessage(StreamMessageDTO message) {
        // 收集所有流式消息，保证顺序
        messageHistory.add(message);
        try {
            log.info("会话 [{}] 发送消息：{}", chatId, message);
            sseEmitter.send(SseEmitter.event().name(message.getEvent()).data(message.getData(),
                    MediaType.APPLICATION_JSON));
            ChatEventEnum event = ChatEventEnum.fromEvent(message.getEvent());
            if (ChatEventEnum.DONE.equals(event)) {
                log.info("会话 [{}] 完成,complete()", chatId);
                sseEmitter.complete();
            }
        } catch (IOException e) {
            log.error("会话 [{}] SSE 消息发送失败", chatId, e);
        }
    }
}