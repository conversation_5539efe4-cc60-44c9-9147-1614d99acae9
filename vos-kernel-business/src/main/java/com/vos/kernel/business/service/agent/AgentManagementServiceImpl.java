package com.vos.kernel.business.service.agent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vos.kernel.business.config.XxlJobTaskManager;
import com.vos.kernel.business.constants.AgentosConstants;
import com.vos.kernel.business.conver.agent.IAgentManagementMapper;
import com.vos.kernel.business.dto.agent.*;
import com.vos.kernel.business.entity.AgentAuthorizeEntity;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.entity.CronTriggerInfoEntity;
import com.vos.kernel.business.entity.EventTriggerInfoEntity;
import com.vos.kernel.business.enums.StatusEnum;
import com.vos.kernel.business.mapper.AgentAuthorizeMapper;
import com.vos.kernel.business.mapper.AgentInfoMapper;
import com.vos.kernel.business.mapper.EventTriggerInfoMapper;
import com.vos.kernel.business.service.batisplus.IAgentAuthorizeService;
import com.vos.kernel.business.service.conversation.IConversationService;
import com.vos.kernel.common.LinkerStorageService;
import com.vos.kernel.common.property.FileInfo;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.linker.basic.exception.BusinessException;

import javax.annotation.Resource;
import java.io.IOException;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

import org.apache.commons.io.IOUtils;

import com.vos.kernel.business.mapper.CronTriggerInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AgentManagementServiceImpl implements IAgentManagementService {

    private static final String AGENT_CONFIG_FILE = "agent/agent_config.json";
    private static final String WORKFLOWS_DIR = "agent/workflows/";
    private static final String S3_AGENT_CONFIG_STORAGE_PATH = "agent/config/";

    private static final String CRON_TASK_TYPE_STRING = "scheduled";
    private static final String EVENT_TASK_TYPE_STRING = "event";

    private static final String ZIPPED_FILE_SUFFIX = ".zip";

    @Resource
    private AgentInfoMapper agentInfoMapper;

    @Resource
    private IAgentManagementMapper agentManagementMapper;

    @Resource
    private EventTriggerInfoMapper eventTriggerMapper;

    @Resource
    private IWorkflowRunner workflowRunner;

    @Resource
    private LinkerStorageService linkerStorageService;

    @Resource
    private XxlJobTaskManager xxlJobTaskManager;

    @Resource
    private CronTriggerInfoMapper cronTriggerInfoMapper;

    @Resource
    private AgentAuthorizeMapper agentAuthorizeMapper;

    @Resource
    private IAgentAuthorizeService agentAuthorizeService;

    @Resource
    private IConversationService conversationService;

    /**
     * 分页查询Agent列表
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @Override
    public IPage<AgentInfoDTO> getAgentList(ListInfoQueryDTO queryDTO) {
        Page<AgentInfoEntity> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        IPage<AgentInfoEntity> result = agentInfoMapper.queryAgentInfoPage(
                page,
                queryDTO.getTenantId(),
                queryDTO.getAgentType(),
                queryDTO.getName(),
                queryDTO.getDescription(),
                queryDTO.getRunning(),
                queryDTO.getAll()
        );

        return result.convert(agentManagementMapper::toDTO);
    }

    @Override
    public IPage<AgentInfoContentDTO> getAppAgentList(ListInfoQueryDTO queryDTO) {
        Page<AgentInfoEntity> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        IPage<AgentInfoEntity> result = agentInfoMapper.queryAppAgentInfoPage(
                page,
                queryDTO.getTenantId(),
                queryDTO.getAgentType(),
                queryDTO.getName(),
                queryDTO.getDescription(),
                queryDTO.getRunning(),
                queryDTO.getAll(),
                queryDTO.getIsOfficial(),
                queryDTO.getSortCondition()
        );
        return result.convert(agentManagementMapper::toContentDTO);
    }

    @Override
    public IPage<EventTriggerInfoDTO> getEventTriggerList(ListInfoQueryDTO queryDTO) {
        Page<EventTriggerInfoEntity> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        IPage<EventTriggerInfoEntity> result = eventTriggerMapper.queryTriggerListByAgentId(page, queryDTO.getId());
        return result.convert(agentManagementMapper::toDTO);
    }

    /**
     * 根据智能体ID查询智能体数据
     *
     * @param agentId 智能体ID
     * @return 智能体信息
     */
    @Override
    public AgentInfoEntity getAgentInfo(String agentId) {
        return agentInfoMapper.selectById(agentId);
    }

    @Override
    public AgentDetailInfoDTO getAgentDetail(String tenantId, String agentId) {
        AgentInfoEntity agentInfo = agentInfoMapper.selectOne(new LambdaQueryWrapper<AgentInfoEntity>()
                .eq(AgentInfoEntity::getAgentId, agentId));
        if (agentInfo == null) {
            throw new BusinessException("智能体不存在");
        }

        return agentManagementMapper.toDetailDTO(agentInfo);
    }

    /**
     * 删除智能体
     *
     * @param agentId 智能体ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> deleteAgent(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }

        // 查询智能体信息，获取配置文件路径
        AgentInfoEntity agentInfo = agentInfoMapper.selectById(agentId);
        if (agentInfo != null) {
            try {
                // 删除S3上的配置文件包
                if (StringUtils.isNotBlank(agentInfo.getConfigPath())) {
                    linkerStorageService.delete(agentInfo.getConfigPath());
                }
            } catch (Exception e) {
                log.error("删除智能体配置文件失败: {}", agentId, e);
                // 继续执行数据库删除
            }
        }

        // 删除数据库记录
        agentInfoMapper.deleteById(agentId);

        // 删除定时任务记录
        cronTriggerInfoMapper.deleteByAgentId(agentId);

        // 删除事件触发器记录
        eventTriggerMapper.deleteByAgentId(agentId);

        // 删除会话记录
        List<String> conversationIds = conversationService.deleteConversationsByAgentId(Long.parseLong(agentId));
        return conversationIds;
    }

    /**
     * 批量停止智能体
     *
     * @param operateDTO 操作参数
     * @return 每个智能体的操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OperateResultDTO> stopAgent(BatchOperateDTO operateDTO) {
        if (operateDTO == null || operateDTO.getIds() == null || operateDTO.getIds().isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        List<OperateResultDTO> results = new ArrayList<>();
        for (String agentId : operateDTO.getIds()) {
            try {
                // 查询智能体是否存在
                AgentInfoEntity agentInfo = agentInfoMapper.selectById(agentId);
                if (agentInfo == null) {
                    log.warn("智能体不存在: {}", agentId);
                    results.add(OperateResultDTO.fail(agentId, "智能体不存在"));
                    continue;
                }

                // 检查当前状态是否可以停止
                if (StatusEnum.STOPPED.type.equals(agentInfo.getRunning())) {
                    log.warn("智能体已经处于停止状态: {}", agentId);
                    results.add(OperateResultDTO.success(agentId));
                    continue;
                }

                // 更新智能体状态
                AgentInfoEntity updateEntity = new AgentInfoEntity();
                updateEntity.setAgentId(agentId);
                updateEntity.setRunning(StatusEnum.STOPPED.type);
                updateEntity.setUpdateTime(LocalDateTime.now());

                if (agentInfoMapper.updateById(updateEntity) > 0) {
                    results.add(OperateResultDTO.success(agentId));
                } else {
                    results.add(OperateResultDTO.fail(agentId, "更新智能体状态失败"));
                }
            } catch (Exception e) {
                log.error("停止智能体失败: {}", agentId, e);
                results.add(OperateResultDTO.fail(agentId, "停止智能体失败"));
            }
        }

        return results;
    }

    /**
     * 批量启动智能体
     *
     * @param operateDTO 操作参数
     * @return 每个智能体的操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OperateResultDTO> startAgent(BatchOperateDTO operateDTO) {
        if (operateDTO == null || operateDTO.getIds() == null || operateDTO.getIds().isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        List<OperateResultDTO> results = new ArrayList<>();
        for (String agentId : operateDTO.getIds()) {
            try {
                // 查询智能体是否存在
                AgentInfoEntity agentInfo = agentInfoMapper.selectById(agentId);
                if (agentInfo == null) {
                    log.warn("智能体不存在: {}", agentId);
                    results.add(OperateResultDTO.fail(agentId, "智能体不存在"));
                    continue;
                }

                // 检查当前状态是否可以启动
                if (StatusEnum.RUNNING.type.equals(agentInfo.getRunning())) {
                    log.warn("智能体已经在运行中: {}", agentId);
                    results.add(OperateResultDTO.success(agentId));
                    continue;
                }
                if (StatusEnum.INITIALIZING.type.equals(agentInfo.getRunning())) {
                    log.warn("智能体正在初始化中: {}", agentId);
                    results.add(OperateResultDTO.fail(agentId, "智能体正在初始化中"));
                    continue;
                }
                if (StatusEnum.ERROR.type.equals(agentInfo.getRunning())) {
                    log.warn("智能体处于错误状态，无法启动: {}", agentId);
                    results.add(OperateResultDTO.fail(agentId, "智能体处于错误状态，无法启动"));
                    continue;
                }

                // 更新智能体状态
                AgentInfoEntity updateEntity = new AgentInfoEntity();
                updateEntity.setAgentId(agentId);
                updateEntity.setRunning(StatusEnum.RUNNING.type);
                updateEntity.setUpdateTime(LocalDateTime.now());
                updateEntity.setLastStartTime(LocalDateTime.now());

                if (agentInfoMapper.updateById(updateEntity) > 0) {
                    results.add(OperateResultDTO.success(agentId));
                } else {
                    results.add(OperateResultDTO.fail(agentId, "更新智能体状态失败"));
                }
            } catch (Exception e) {
                log.error("启动智能体失败: {}", agentId, e);
                results.add(OperateResultDTO.fail(agentId, "启动智能体失败"));
            }
        }

        return results;
    }

    @Override
    public Boolean configAgent(AgentConfigRequest agentConfigRequest) {
        AgentInfoEntity agentInfo = agentInfoMapper.selectById(agentConfigRequest.getAgentId());
        if (null == agentInfo) {
            throw new BusinessException("智能体不存在:" + agentConfigRequest.getAgentId());
        }
        agentInfo.setIsOfficial(agentConfigRequest.getIsOfficial());
        agentInfo.setOfficialOrder(agentConfigRequest.getOfficialOrder());
        agentInfo.setVideoUrl(agentConfigRequest.getVideoUrl());
        return agentInfoMapper.updateById(agentInfo) > 0;
    }

    @Override
    public Boolean authorizeAgent(AuthorizeAgentRequest authorizeAgentRequest) {
        AgentInfoEntity agentInfo = agentInfoMapper.selectById(authorizeAgentRequest.getAgentId());
        if (null == agentInfo) {
            throw new BusinessException("智能体不存在:" + authorizeAgentRequest.getAgentId());
        }
        agentInfo.setAuthorizeAll(BooleanUtil.isTrue(authorizeAgentRequest.getAllAuthorize()));
        agentInfoMapper.updateById(agentInfo);
        //删除授权的租户
        deleteAuthorizeTenant(authorizeAgentRequest.getAgentId());
        if (CollectionUtil.isNotEmpty(authorizeAgentRequest.getAuthorizeTenantIds())) {
            //插入授权的租户
            insertAuthorizeTenant(authorizeAgentRequest.getAgentId(), authorizeAgentRequest.getAuthorizeTenantIds());
        }
        return true;
    }

    @Override
    public AuthorizeAgentRequest authorizeAgentGet(String agentId) {
        AuthorizeAgentRequest authorizeAgentRequest = new AuthorizeAgentRequest();
        authorizeAgentRequest.setAgentId(agentId);
        AgentInfoEntity agentInfo = agentInfoMapper.selectById(agentId);
        if (null == agentInfo) {
            throw new BusinessException("智能体不存在:" + agentId);
        }
        authorizeAgentRequest.setAllAuthorize(agentInfo.getAuthorizeAll());
        if (!BooleanUtil.isTrue(agentInfo.getAuthorizeAll())) {
            List<AgentAuthorizeEntity> list = agentAuthorizeService.list(new LambdaQueryWrapper<AgentAuthorizeEntity>()
                    .eq(AgentAuthorizeEntity::getAgentId, agentId));
            if (CollectionUtil.isNotEmpty(list)) {
                List<String> collect = list.stream().map(AgentAuthorizeEntity::getTenantId).collect(Collectors.toList());
                authorizeAgentRequest.setAuthorizeTenantIds(collect);
            }
        }
        return authorizeAgentRequest;
    }

    private void deleteAuthorizeTenant(String agentId) {
        agentAuthorizeMapper.delete(new LambdaQueryWrapper<AgentAuthorizeEntity>()
                .eq(AgentAuthorizeEntity::getAgentId, agentId));
    }

    private void insertAuthorizeTenant(String agentId, List<String> authorizeTenantIds) {
        ArrayList<AgentAuthorizeEntity> agentAuthorizeEntities = new ArrayList<>();

        for (String authorizeTenantId : authorizeTenantIds) {
            AgentAuthorizeEntity agentAuthorizeEntity = new AgentAuthorizeEntity();
            agentAuthorizeEntity.setAgentId(agentId);
            agentAuthorizeEntity.setTenantId(authorizeTenantId);
            agentAuthorizeEntities.add(agentAuthorizeEntity);

        }
        agentAuthorizeService.saveBatch(agentAuthorizeEntities);
    }

    /**
     * 创建智能体
     *
     * @param createDTO 创建参数
     * @return 智能体ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAgent(PackageCreateDTO createDTO) {
        try {
            MultipartFile agentPackage = createDTO.getFile();
            String originalFilename = agentPackage.getOriginalFilename();
            if (originalFilename != null && !originalFilename.endsWith(ZIPPED_FILE_SUFFIX)) {
                log.error("上传的文件格式不正确: {}", originalFilename);
                throw new BusinessException("只支持zip格式的配置包");
            }

            AgentConfig agentConfig = null;
            Map<String, byte[]> workflowFiles = new HashMap<>(16);

            // 读取zip文件到内存
            try (ZipInputStream zipInputStream = new ZipInputStream(agentPackage.getInputStream())) {
                ZipEntry entry;
                while ((entry = zipInputStream.getNextEntry()) != null) {
                    String entryName = entry.getName();

                    if (AGENT_CONFIG_FILE.equals(entryName)) {
                        // 读取配置文件内容
                        String configContent = IOUtils.toString(zipInputStream, StandardCharsets.UTF_8);
                        //改回原有逻辑
                        ObjectMapper mapper = new ObjectMapper();
                        try {
                            agentConfig = mapper.readValue(configContent, AgentConfig.class);
                        } catch (Exception e) {
                            log.error("解析agent_config.json失败: {}", configContent, e);
                            throw new BusinessException("解析配置文件失败: " + e.getMessage());
                        }
                    } else if (entryName.startsWith(WORKFLOWS_DIR) && entryName.endsWith(ZIPPED_FILE_SUFFIX) && !BooleanUtil.isTrue(createDTO.getIsPublish())) {
                        // 读取工作流压缩包内容
                        String workflowName = entryName.substring(WORKFLOWS_DIR.length());
                        if (!workflowName.isEmpty()) {
                            try {
                                workflowFiles.put(workflowName, IOUtils.toByteArray(zipInputStream));
                            } catch (Exception e) {
                                log.error("读取工作流文件失败: {}", workflowName, e);
                                throw new BusinessException("读取工作流文件失败: " + e.getMessage());
                            }
                        }
                    }

                    zipInputStream.closeEntry();
                }
            }

            if (agentConfig == null) {
                log.error("配置包中缺少agent_config.json文件");
                throw new BusinessException("配置包中缺少agent_config.json文件");
            }

            // 验证配置
            try {
                validateAgentConfig(agentConfig);
            } catch (Exception e) {
                log.error("验证配置失败: {}", agentConfig, e);
                throw new BusinessException("验证配置失败: " + e.getMessage());
            }

            // 上传配置压缩包到S3
            FileInfo resourceInfo;
            try {
                resourceInfo = linkerStorageService.of(agentPackage.getInputStream(), "application/zip")
                        .setPath(S3_AGENT_CONFIG_STORAGE_PATH + agentConfig.getAgentId())
                        .upload();
            } catch (Exception e) {
                log.error("上传配置包到S3失败: {}", agentConfig.getAgentId(), e);
                throw new BusinessException("上传配置包失败: " + e.getMessage());
            }

            // 创建智能体实体
            // 查询是否已存在
            AgentInfoEntity existingAgent;
            try {
                existingAgent = agentInfoMapper.selectById(String.valueOf(agentConfig.getAgentId()));
            } catch (Exception e) {
                log.error("查询智能体失败: {}", agentConfig.getAgentId(), e);
                throw new BusinessException("查询智能体失败: " + e.getMessage());
            }

            AgentInfoEntity agentInfo = new AgentInfoEntity();
            agentInfo.setTenantId(createDTO.getHeaderDTO().getTenantId());
            agentInfo.setAgentId(String.valueOf(agentConfig.getAgentId()));
            agentInfo.setAgentName(agentConfig.getAgentName());
            agentInfo.setAgentType(agentConfig.getAgentType());
            agentInfo.setDescription(agentConfig.getDescription());
            agentInfo.setIconPath(agentConfig.getIcon());
            agentInfo.setConfigPath(resourceInfo.getUrlOut());
            agentInfo.setSort(agentConfig.getSort());
            agentInfo.setAgentVersion(agentConfig.getVersion());
            agentInfo.setUpdateTime(LocalDateTime.now());
            agentInfo.setLastStartTime(LocalDateTime.now());
            agentInfo.setRunning(StatusEnum.INITIALIZING.type);
            agentInfo.setDeleted(false);
            agentInfo.setIconLayer(agentConfig.getIconLayer());
            agentInfo.setSourceManufacturer(agentConfig.getSourceManufacturer());
            if (null != agentConfig.getKnowledgeAgentId()) {
                agentInfo.setKnowledgeAgentId(agentConfig.getKnowledgeAgentId());
            }
            if (CollectionUtil.isNotEmpty(agentConfig.getTagList())) {
                agentInfo.setTagList(JSON.toJSONString(agentConfig.getTagList()));
            }
            agentInfo.setOperateType(agentConfig.getOperateType());
//            List<Integer> list = Arrays.asList(3, 5, 6, 7, 8);list.contains(agentConfig.getAgentType()) &&
            if (StrUtil.isNotBlank(agentConfig.getTemplateParams())) {
                agentInfo.setTemplateParams(agentConfig.getTemplateParams());
            }
            agentInfo.setPreChat(agentConfig.getData().getPreChatInfo() == null ? null : JSON.toJSONString(agentConfig.getData().getPreChatInfo()));
            agentInfo.setModel(agentConfig.getData().getModelInfo() == null ? null : JSON.toJSONString(agentConfig.getData().getModelInfo()));
            agentInfo.setWorkflow(agentConfig.getData().getChatInfo().getWorkflowId());
            agentInfo.setWorkflowVersion(agentConfig.getData().getChatInfo().getWorkflowVersion());
            agentInfo.setSuggest(agentConfig.getData().getSuggestReplyInfo() == null ? null : JSON.toJSONString(agentConfig.getData().getSuggestReplyInfo()));
            agentInfo.setTriggers(agentConfig.getData().getTriggers() == null ? null : JSON.toJSONString(agentConfig.getData().getTriggers()));
            AgentConfigDTO agentConfigDTO = new AgentConfigDTO();
            if (agentConfig.getData().getKnowledgeConfig() != null) {
                agentConfigDTO.setKnowledgeConfig(agentConfig.getData().getKnowledgeConfig());
            }
            if (CollectionUtil.isNotEmpty(agentConfig.getData().getQuickInstructionInfos())) {
                agentConfigDTO.setQuickInstructionInfo(agentConfig.getData().getQuickInstructionInfos());
            }
            agentInfo.setAgentConfig(JSON.toJSONString(agentConfigDTO));
            try {
                if (existingAgent == null) {
                    // 新增
                    agentInfo.setCreateTime(LocalDateTime.now());
                    if (agentInfoMapper.insert(agentInfo) <= 0) {
                        log.error("新增智能体失败: {}", agentInfo);
                        throw new BusinessException("新增智能体失败");
                    }
                } else {
                    // 更新
                    if (agentInfoMapper.updateById(agentInfo) <= 0) {
                        log.error("更新智能体失败: {}", agentInfo);
                        throw new BusinessException("更新智能体失败");
                    }
                }
            } catch (Exception e) {
                log.error("保存智能体失败: {}", agentInfo, e);
                throw new BusinessException("保存智能体失败: " + e.getMessage());
            }

            // 注册工作流，上传配置文件到工作流服务
            if (!workflowFiles.isEmpty()) {
                for (Map.Entry<String, byte[]> entry : workflowFiles.entrySet()) {
                    byte[] workflowContent = entry.getValue();
                    try {
                        if (!workflowRunner.uploadWorkflow(workflowContent, createDTO.getHeaderDTO())) {
                            log.error("上传工作流文件失败: {}", entry.getKey());
                            throw new BusinessException("上传工作流文件失败");
                        }
                    } catch (Exception e) {
                        log.error("上传工作流文件失败: {}", entry.getKey(), e);
                        throw new BusinessException("上传工作流文件失败: " + e.getMessage());
                    }
                }
            }

            // 清空定时器信息表
            cronTriggerInfoMapper.deleteByAgentId(agentInfo.getAgentId());
            // 清空事件触发器信息表
            eventTriggerMapper.deleteByAgentId(agentInfo.getAgentId());

            // 注册定时器
            if (agentConfig.getData() != null && agentConfig.getData().getTriggers() != null) {
                for (AgentConfig.Trigger trigger : agentConfig.getData().getTriggers()) {
                    try {
                        // 根据trigger类型进行不同处理
                        log.info("处理trigger配置: {}", JSON.toJSONString(trigger));

                        if (trigger.getTriggerType().equals(CRON_TASK_TYPE_STRING)) {

                            CronTriggerInfoEntity cronTriggerInfo = new CronTriggerInfoEntity();
                            cronTriggerInfo.setAgentId(agentInfo.getAgentId());
                            cronTriggerInfo.setWorkflow(trigger.getAction().getWorkflowAction().getWorkflowId());
                            cronTriggerInfo.setWorkflowVersion(trigger.getAction().getWorkflowAction().getVersion());
                            cronTriggerInfo.setTenantId(createDTO.getHeaderDTO().getTenantId());
                            cronTriggerInfo.setUserCode(createDTO.getHeaderDTO().getUserCode());

                            String params = trigger.getAction().getWorkflowAction().getParams();
                            if (StringUtils.isNotBlank(params)) {
                                try {
                                    JSONArray jsonArray = JSON.parseArray(params);
                                    Map<String, Object> ext = new HashMap<>(16);
                                    for (Object obj : jsonArray) {
                                        JSONObject jsonObj = (JSONObject) obj;
                                        String name = jsonObj.getString("name");
                                        Object value = jsonObj.get("value");
                                        ext.put(name, value);
                                    }
                                    // 将Map转换为JSON字符串
                                    cronTriggerInfo.setWorkflowParams(JSON.toJSONString(ext));
                                } catch (Exception e) {
                                    log.error("解析定时任务参数失败: {}", params, e);
                                    throw new BusinessException("解析定时任务参数失败: " + e.getMessage());
                                }
                            }

                            for (String cronExpr : trigger.getTimeData().getCronExprList()) {
                                // 每次循环创建新对象,避免ID重复
                                CronTriggerInfoEntity newCronTrigger = new CronTriggerInfoEntity();
                                // 复制属性
                                newCronTrigger.setAgentId(cronTriggerInfo.getAgentId());
                                newCronTrigger.setWorkflow(cronTriggerInfo.getWorkflow());
                                newCronTrigger.setWorkflowVersion(cronTriggerInfo.getWorkflowVersion());
                                newCronTrigger.setTenantId(cronTriggerInfo.getTenantId());
                                newCronTrigger.setUserCode(cronTriggerInfo.getUserCode());
                                newCronTrigger.setWorkflowParams(cronTriggerInfo.getWorkflowParams());
                                newCronTrigger.setCron(cronExpr);

                                if (cronTriggerInfoMapper.insert(newCronTrigger) <= 0) {
                                    log.error("插入定时任务失败: {}", newCronTrigger);
                                    throw new BusinessException("插入定时任务失败");
                                }

                                if (!xxlJobTaskManager.tryToCreateXxljobTask(cronExpr, AgentosConstants.ACTOR_HANDLER, null)) {
                                    log.error("注册定时器失败: {}", cronExpr);
                                    throw new BusinessException("注册定时器失败");
                                }
                            }
                        } else if (trigger.getTriggerType().equals(EVENT_TASK_TYPE_STRING)) {
                            // 将事件信息保存到数据库
                            EventTriggerInfoEntity eventTriggerInfo = new EventTriggerInfoEntity();
                            eventTriggerInfo.setTriggerId(trigger.getId());
                            eventTriggerInfo.setTriggerName(trigger.getName());
                            eventTriggerInfo.setToken(trigger.getWebhookData().getBearerToken());
                            eventTriggerInfo.setOutputSchema(trigger.getWebhookData().getOutputSchema());
                            eventTriggerInfo.setUrl(trigger.getWebhookData().getUrl());
                            eventTriggerInfo.setAction(JSON.toJSONString(trigger.getAction()));
                            eventTriggerInfo.setAgentId(agentInfo.getAgentId());

                            if (eventTriggerMapper.insert(eventTriggerInfo) <= 0) {
                                log.error("插入事件触发器失败: {}", eventTriggerInfo);
                                throw new BusinessException("插入事件触发器失败");
                            }
                        }

                    } catch (Exception e) {
                        log.error("处理trigger失败: {}", trigger, e);
                        throw new BusinessException("注册定时器失败: " + e.getMessage());
                    }
                }
            }

            // 更新智能体状态
            try {
                agentInfo.setRunning(StatusEnum.RUNNING.type);
                agentInfo.setUpdateTime(LocalDateTime.now());
                if (agentInfoMapper.updateById(agentInfo) <= 0) {
                    log.error("更新智能体状态失败: {}", agentInfo);
                    throw new BusinessException("更新智能体状态失败");
                }
            } catch (Exception e) {
                log.error("更新智能体状态失败: {}", agentInfo, e);
                throw new BusinessException("更新智能体状态失败: " + e.getMessage());
            }

            return agentInfo.getAgentId();
        } catch (IOException e) {
            log.error("处理智能体配置包失败", e);
            throw new BusinessException("处理智能体配置包失败: " + e.getMessage());
        }
    }

    /**
     * 验证智能体配置
     */
    private void validateAgentConfig(AgentConfig config) {
        if (StringUtils.isBlank(config.getAgentName())) {
            throw new BusinessException("智能体名称不能为空");
        }
        if (config.getAgentType() == null) {
            throw new BusinessException("智能体类型不能为空");
        }
        // 其他验证...
        // TODO 待补充
    }

    @Override
    public void updateAgent(AgentInfoEntity agentInfoEntity) {
        agentInfoMapper.updateById(agentInfoEntity);
    }
}
