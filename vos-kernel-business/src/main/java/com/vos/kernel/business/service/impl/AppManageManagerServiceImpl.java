package com.vos.kernel.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linker.basic.baseclass.BaseResp;
import com.linker.basic.exception.BusinessException;
import com.vos.kernel.business.api.IV3ApiBackService;
import com.vos.kernel.business.config.OmHubProperties;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.entity.LhTaskAppAbilityLimitEntity;
import com.vos.kernel.business.entity.TaskTypeConfigAiEntity;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.entity.UserVo;
import com.vos.kernel.business.mapper.OmPreInstallMapper;
import com.vos.kernel.business.mapper.TaskTypeMapper;
import com.vos.kernel.business.service.AppManageManagerService;
import com.vos.kernel.business.service.AuthenticationManagerService;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.service.IOmhubAsyncService;
import com.vos.kernel.business.service.appconfig.IModelConfigDesignService;
import com.vos.kernel.business.service.batisplus.ILhTaskAppAbilityLimitService;
import com.vos.kernel.business.service.batisplus.ITaskTypeConfigAiService;
import com.vos.kernel.business.service.comparison.IComparisonAbilityService;
import com.vos.kernel.common.LinkerStorageService;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.entity.AbilityInstallResultDTO;
import com.vos.kernel.common.entity.OmInfoGetDTO;
import com.vos.kernel.common.entity.OmInfoUpdateDTO;
import com.vos.kernel.common.redis.contant.RedisConstant;
import com.vos.kernel.common.storage.WebDavCommonConfigProperties;
import com.vos.kernel.common.utils.StringUtils;
import com.vos.kernel.core.api.domain.*;
import com.vos.kernel.core.api.entity.constants.RedisKey;
import com.vos.kernel.core.api.exception.AppManageException;
import com.vos.kernel.core.api.rpc.AppManageRpcService;
import com.vos.kernel.core.api.rpc.IAIAbilityRpcService;
import com.vos.kernel.core.api.vo.AppManageReqVO;
import com.vos.kernel.core.api.vo.AppManageSwitchDTO;
import com.vos.kernel.core.api.vo.CalculateMsgListReq;
import com.vos.kernel.core.api.vo.OnlineInstallReqVO;
import com.vos.task.automated.api.model.dto.AbilityAddWithOutOmDTO;
import com.vos.task.automated.api.model.dto.LicenseDataDto;
import com.vos.task.automated.api.model.dto.OmInfoDTO;
import com.vos.task.automated.api.model.entity.OmPreInstallEntity;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import com.vos.task.automated.api.model.enums.OmInstallStatusEnum;
import com.vos.task.automated.api.service.rpc.IAbilityAutomatedRpcService;
import com.vos.task.manage.api.rpc.TaskAbilityRpcService;
import com.vos.task.poll.api.entity.dto.MessageQueueDto;
import com.vos.task.poll.api.service.rpc.TaskQueueInfoPrcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.object.UpdatableSqlQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.vos.task.automated.api.model.enums.AbilityApiTypeEnum.getAbilityTypeCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17
 * @description: 应用管理实现类
 */
@Slf4j
@Service
public class AppManageManagerServiceImpl implements AppManageManagerService {
    @DubboReference
    private AppManageRpcService appManageRpcService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AuthenticationManagerService authenticationManagerService;

    @Resource
    WebDavCommonConfigProperties webDavCommonConfigProperties;

    @DubboReference
    private TaskQueueInfoPrcService taskQueueInfoPrcService;

    @Value("${omUpload.home}")
    private String omUploadHome;

    @Value("${checkThread.lineCount}")
    private Long lineCount;

    @Resource
    OmHubProperties omHubProperties;

    @DubboReference
    IAbilityAutomatedRpcService abilityAutomatedRpcService;

    @Resource
    IOmhubAsyncService omhubAsyncService;

    @Resource
    TaskTypeMapper taskTypeMapper;

    @Resource
    ICacheService cacheService;

    @Resource
    IComparisonAbilityService comparisonAbilityService;

    @DubboReference
    IAIAbilityRpcService abilityRpcService;

    @Resource
    ILhTaskAppAbilityLimitService lhTaskAppAbilityLimitService;

    @Resource
    IModelConfigDesignService modelConfigDesignService;

    @Resource
    ITaskTypeConfigAiService taskTypeConfigAiService;

    @Resource
    LinkerStorageService linkerStorageService;

    @Resource
    OmPreInstallMapper omPreInstallMapper;

    @DubboReference
    TaskAbilityRpcService taskAbilityRpcService;

    @Resource
    private IV3ApiBackService apiBackService;

    @Resource
    WorkflowProperties workflowProperties;

    @Override
    public BaseResp uploadOMToWebDav(MultipartFile omFile) {

        if (omFile == null) {
            throw new AppManageException("500", "om包不能为空");
        }
        Date date = new Date();
        long time = date.getTime();
        String timestamp = String.valueOf(time);
        String filePath = "";
        try {
            filePath = upload(omFile);
            stringRedisTemplate.opsForValue().set(RedisKey.OM_TIMESTAMP + timestamp, "1");
            // webdav接口上传
            //String name = omFile.getOriginalFilename();
            //InputStream inputStream = omFile.getInputStream();
            //filePath = webDavService.uploadWebDav(webDavVo, "om/" + time + "/" + name, inputStream);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppManageException("500", "上传om包失败");
        }
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("filePath", filePath);
        resultMap.put("timestamp", timestamp);

        return new BaseResp(resultMap);
    }


    String upload(MultipartFile file) throws Exception {
        // 文件名称
        String fileName = file.getOriginalFilename();
        Date date = new Date();
        long time = date.getTime();
        String filePath = webDavCommonConfigProperties.getBase() + "omPacket/" + time + "/" + fileName;
        // 复制文件
        File webDavHomeOm = new File(webDavCommonConfigProperties.getHome() + "omPacket/");
        if (!webDavHomeOm.exists()) {
            webDavHomeOm.mkdir();
        }

        File webDavHomeOmTime = new File(webDavCommonConfigProperties.getHome() + "omPacket/" + time + "/");
        if (!webDavHomeOmTime.exists()) {
            webDavHomeOmTime.mkdir();
        }
        File targetFile = new File(webDavCommonConfigProperties.getHome() + "omPacket/" + time + "/" + fileName);
        FileUtils.writeByteArrayToFile(targetFile, file.getBytes());
        return filePath;
    }

    @Override
    public BaseResp uploadOMToAutomat(MultipartFile omFile) {
        if (omFile == null) {
            throw new AppManageException("上传文件不能为空");
        }

        String fileOriginalFilename = omFile.getOriginalFilename();
        if (!(fileOriginalFilename.toLowerCase().endsWith(".om") || fileOriginalFilename.toLowerCase().endsWith(".lic"))) {
            throw new BusinessException("文件仅支持.om及.lic格式");
        }

        Date date = new Date();
        long time = date.getTime();
        String timestamp = String.valueOf(time);
        String filePath = "";
        String fileName = "";
        try {
            // 文件名称
            fileName = omFile.getOriginalFilename();
            filePath = "/" + time + "/";
            // 复制文件
            File webDavHomeOm = new File(omUploadHome);
            if (!webDavHomeOm.exists()) {
                webDavHomeOm.mkdir();
            }

            File webDavHomeOmTime = new File(omUploadHome + time + "/");
            if (!webDavHomeOmTime.exists()) {
                webDavHomeOmTime.mkdir();
            }

            File targetFile = new File(omUploadHome + time + "/" + fileName);

            omFile.transferTo(targetFile);

            stringRedisTemplate.opsForValue().set(RedisKey.OM_TIMESTAMP + timestamp, "1");

        } catch (Exception e) {
            e.printStackTrace();
            throw new AppManageException("上传文件不能为空");
        }
        Map<String, String> resultMap = new HashMap<>();
        //resultMap.put("filePath", filePath);
        resultMap.put("fileName", filePath + fileName);
        resultMap.put("timestamp", timestamp);
        return new BaseResp(resultMap);
    }

    @Override
    public BaseResp uploadOMToOnline(OnlineInstallReqVO reqVO) {

        Assert.notNull(reqVO.getOmFilePath(), "om包下载链接不能为空");
        Assert.notNull(reqVO.getActivationFilePath(), "激活文件下载链接不能为空");


        Pattern p = Pattern.compile("^([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\\\\\\\\/])+$");

        Matcher m = p.matcher(reqVO.getOmFilePath());
        if (!m.matches()) {
            throw new RuntimeException("om包下载地址格式错误");
        }

        Matcher matcher = p.matcher(reqVO.getActivationFilePath());
        if (!matcher.matches()) {
            throw new RuntimeException("激活文件下载地址格式错误");
        }

        if (!reqVO.getOmFilePath().toLowerCase().endsWith(".om")) {
            throw new BusinessException("om包仅支持.om格式");
        }
        if (!reqVO.getActivationFilePath().toLowerCase().endsWith(".lic")) {
            throw new BusinessException("激活文件仅支持.lic格式");
        }

        Date date = new Date();
        long time = date.getTime();
        String filePath = omUploadHome + time + "/";
        String path = "/" + time + "/";
        String fileName = "";
        String activationName = "";
        //下载激活文件
        try {
            //下载
            fileName = downloadPackage(filePath, reqVO.getOmFilePath());

        } catch (Exception e) {
            throw new BusinessException("om包下载异常");
        }
        //下载激活文件
        try {
            //下载
            activationName = downloadPackage(filePath, reqVO.getActivationFilePath());
        } catch (Exception e) {
            throw new BusinessException("激活文件下载异常");
        }

        Map map = new HashMap();
        //map.put("path", path);

        map.put("fileName", path + fileName);

        map.put("activationName", path + activationName);

        return new BaseResp(map);
    }

    /**
     * 下载文件，打印信息
     *
     * @param path
     * @param uploadUrl
     */
    private String downloadPackage(String path, String uploadUrl) {
        StopWatch sw = new StopWatch("文件下载耗时统计");

        File homeOmTime = new File(path);
        if (!homeOmTime.exists()) {
            homeOmTime.mkdir();
        }
        uploadUrl = URLUtil.decode(uploadUrl);
        log.info("开始下载文件到指定路径：{},下载url:{}", path, uploadUrl);
        sw.start();
        String[] splits = uploadUrl.split("/");
        String outFileName = splits[splits.length - 1];
        long downloadTime = HttpUtil.downloadFile(uploadUrl, path + outFileName);
        sw.stop();
        log.info("文件下载完成，文件大小：{}，耗时：{} ms", downloadTime, sw.getTotalTimeMillis());
        return outFileName;
    }

    @Override
    public String installCallback(JSONObject jsonObject) {
        return appManageRpcService.installCallback(jsonObject);
    }

    @Override
    public BaseResp onlineInstallCallback(JSONObject jsonObject) {
        appManageRpcService.onlineInstallCallback(jsonObject);
        return new BaseResp();
    }

    @Override
    public String enableCallback(JSONObject jsonObject) {
        return appManageRpcService.enableCallback(jsonObject);
    }

    @Override
    public BaseResp updateCallback(JSONObject jsonObject) {
        appManageRpcService.updateCallback(jsonObject);
        return new BaseResp();
    }

    @Override
    public BaseResp offlineActivationCallback(JSONObject jsonObject) {
        appManageRpcService.offlineActivationCallback(jsonObject);
        return new BaseResp();
    }

    /**
     * hub 授权 gpu限制校验
     */
    private void hubGpuCheck() {
        if (BooleanUtil.isTrue(omHubProperties.getOpen())) {
            OmHubLicenceGetDTO licenceInfo = omhubAsyncService.getLicenceInfo();
            int gpuLimit = licenceInfo.getGpuLimit();
            Integer gpuInfo = abilityAutomatedRpcService.getGpuInfo();
            if (gpuInfo != null && gpuInfo > gpuLimit) {
                log.warn("gpu卡数已达上限,上限：{},当前数：{}", gpuLimit, gpuInfo);
                throw new BusinessException("gpu卡数已达上限，无法继续安装");
            }
        }
    }


    @Override
    public BaseResp install(AppManageReqVO reqVO) {
        JSONObject decryptData = (JSONObject) authenticationManagerService.getDecryptData().getData();
        String authCode = authenticationManagerService.getRegistrationCode();
        hubGpuCheck();
        if (reqVO.getFileName().contains("http")) {
            log.info("om包下载地址:{}", reqVO.getFileName());
            try {
                //下载
                String path = new Date().getTime() + "/";
                String filePath = omUploadHome + path;
                String downloadPackage = downloadPackage(filePath, reqVO.getFileName());
                try {
                    linkerStorageService.delete(reqVO.getFileName());
                } catch (Exception e) {
                    log.error("删除om包失败,{}", reqVO.getFileName(), e);
                }

                reqVO.setFileName("/" + path + downloadPackage);
            } catch (Exception e) {
                throw new BusinessException("om包下载异常", e);
            }
            return new BaseResp(appManageRpcService.installWithStrategy(reqVO, decryptData, authCode));
        } else {

            if (StrUtil.isNotBlank(reqVO.getResource()) && "vnet".equals(reqVO.getResource())) {
                return new BaseResp(appManageRpcService.install(reqVO, decryptData, authCode));
            } else {
                return new BaseResp(appManageRpcService.installWithStrategy(reqVO, decryptData, authCode));
            }
        }

    }

    /**
     * 获取算法信息
     *
     * @param taskTypeCode
     * @return
     */
    private TTaskType getTaskType(String taskTypeCode, Boolean needCheckStatus) {
        QueryWrapper<TTaskType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TTaskType::getTaskTypeCode, taskTypeCode).eq(TTaskType::getIsdel, 0).last(" limit 1");
        TTaskType tTaskType = taskTypeMapper.selectOne(queryWrapper);
        if (null == tTaskType) {
            throw new BusinessException("参数错误，未找到对应的算法模型");
        }
        if (!AbilityApiTypeEnum.COMPARE.getCodeInteger().equals(tTaskType.getAbilityEnum())) {
            throw new BusinessException("非比对算法不可更新");
        }
        if (BooleanUtil.isTrue(needCheckStatus)) {
            Integer status = tTaskType.getStatus();
            if (!OmInstallStatusEnum.INACTIVE.getStatus().equals(status) && !OmInstallStatusEnum.OFFLINE.getStatus().equals(status)) {
                throw new BusinessException("算法模型当前状态不可更新");
            }
        }
        return tTaskType;
    }


    /**
     * 获取om包信息
     *
     * @param omAppId
     * @return
     */
    private OmPreInstallEntity getOmPreInfo(String omAppId) {
        QueryWrapper<OmPreInstallEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OmPreInstallEntity::getOmAppId, omAppId).eq(OmPreInstallEntity::getIsDeleted, 0).last(" limit 1");
        OmPreInstallEntity omPreInstallEntity = omPreInstallMapper.selectOne(queryWrapper);
        if (null == omPreInstallEntity) {
            throw new BusinessException("参数错误，未找到对应的模型包信息");
        }
        return omPreInstallEntity;
    }

    @Override
    public OmInfoDTO getOmInfo(OmInfoGetDTO omInfoGet) {

        TTaskType taskType = getTaskType(omInfoGet.getTaskTypeCode(), true);
        //更改pre表数据
        OmPreInstallEntity omPreInfo = getOmPreInfo(taskType.getAbility());
//        String context = omPreInfo.getContext();
//        JSONObject jsonObject = JSONObject.parseObject(context);
//        String apiBefore = jsonObject.getString("abilityApiType");

        if (omInfoGet.getFileName().contains("http")) {
            log.info("om包下载地址:{}", omInfoGet.getFileName());
            //下载
            String path = new Date().getTime() + "/";
            try {
                String downloadPackage = downloadPackage(omUploadHome + path, omInfoGet.getFileName());
                try {
                    linkerStorageService.delete(omInfoGet.getFileName());
                } catch (Exception e) {
                    log.error("删除om包失败,{}", omInfoGet.getFileName(), e);
                }
                omInfoGet.setFileName("/" + path + downloadPackage);
            } catch (Exception e) {
                throw new BusinessException("om包下载异常", e);
            }
        }
        if (StrUtil.isBlank(omInfoGet.getFileName())) {
            throw new BusinessException("om下载地址不能为空");
        }
        omInfoGet.setAbilityEnum(taskType.getAbilityEnum());
        return appManageRpcService.getOmInfo(omInfoGet);
    }

    @Override
    public Boolean omInfoUpdate(OmInfoUpdateDTO omInfo) {
        TTaskType taskType = getTaskType(omInfo.getTaskTypeCode(), true);
        omInfo.setOmAppId(taskType.getAbility());

        Boolean aBoolean = abilityAutomatedRpcService.updateOmAbilityInfo(omInfo);
        //更改应用名称
        if (aBoolean && StrUtil.isNotBlank(omInfo.getAppName()) && !taskType.getName().equals(omInfo.getAppName())) {
            taskType.setName(omInfo.getAppName());
            taskTypeMapper.updateById(taskType);
        }
        return aBoolean;

    }


    /**
     * 生成linkerHeader
     *
     * @return
     */
    public String generateLinkerHeader() {
        String tenantId = DubboDataContext.getAuthIdHolder();
        String userCode = DubboDataContext.getUserHolder();
        UserContext userInfo = new UserContext();
        UserVo userVo = new UserVo();
        userVo.setUserCode(userCode);
        userInfo.setUser(userVo);
        UserContext.TenantInfoDTO tenantInfoDTO = new UserContext.TenantInfoDTO();
        tenantInfoDTO.setTenantId(tenantId);
        userInfo.setTenantInfoDTO(tenantInfoDTO);
        //添加统一
        try {
            return URLEncoder.encode(com.alibaba.fastjson2.JSON.toJSONString(userInfo), "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("生成linkerHeader失败", e);
            throw new BusinessException("生成linkerHeader失败");
        }
    }


    @Override
    public Boolean syncPlatformModel(SyncPlatformModelRequest syncPlatformModelRequest) {
        QueryWrapper<TTaskType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TTaskType::getTaskTypeCode, syncPlatformModelRequest.getTaskTypeCode()).eq(TTaskType::getIsdel, 0).last(" limit 1");
        TTaskType tTaskType = taskTypeMapper.selectOne(queryWrapper);
        if (null == tTaskType) {
            throw new BusinessException("参数错误，未找到对应的算法模型");
        }
        Integer operateStatus = syncPlatformModelRequest.getStatus();
        Integer modelStatus = tTaskType.getModelStatus();
        if (modelStatus.equals(operateStatus)) {
            throw new BusinessException("状态一致无需同步");
        }
        Integer status = tTaskType.getStatus();

        if (operateStatus == 1) {
            //上架
            if (!OmInstallStatusEnum.SUCCESS.getStatus().equals(status)) {
                throw new BusinessException("算法模型当前状态不可上架");
            }
            ModelOperationRequest modelOperationRequest = new ModelOperationRequest();
            modelOperationRequest.setModelName(tTaskType.getName());
            modelOperationRequest.setModelId(BooleanUtil.isTrue(tTaskType.getIsRegister()) ? StrUtil.subBefore(tTaskType.getActionId(), "-", true) : tTaskType.getActionId());
            modelOperationRequest.setModelManufacturer(tTaskType.getModelManufacturer());
            modelOperationRequest.setModelFeatureLabels(StrUtil.isNotBlank(tTaskType.getModelFeatureLabels()) ? StrUtil.split(tTaskType.getModelFeatureLabels(), ";") : new ArrayList<>());
            modelOperationRequest.setModelType(getAbilityTypeCode(tTaskType.getAbilityEnum()));
            modelOperationRequest.setOdModelLabels(syncPlatformModelRequest.getDdModelLabels());
            modelOperationRequest.setAppDescription(tTaskType.getAppDescription());
            modelOperationRequest.setStatus(1);

            com.linker.core.base.baseclass.BaseResp<Boolean> resp = apiBackService.syncPlatformModel(workflowProperties.getEndpoint() + workflowProperties.getSyncModelRouter(),
                    generateLinkerHeader(),
                    modelOperationRequest);
            if (!resp.isSuccess()) {
                throw new BusinessException("上架模型失败,resp " + resp);
            }
        } else {
            //下架
            ModelOperationRequest modelOperationRequest = new ModelOperationRequest();
            modelOperationRequest.setModelId(BooleanUtil.isTrue(tTaskType.getIsRegister()) ? StrUtil.subBefore(tTaskType.getActionId(), "-", true) : tTaskType.getActionId());
            modelOperationRequest.setStatus(0);
            com.linker.core.base.baseclass.BaseResp<Boolean> resp = apiBackService.syncPlatformModel(workflowProperties.getEndpoint() + workflowProperties.getSyncModelRouter(),
                    generateLinkerHeader(),
                    modelOperationRequest);
            if (!resp.isSuccess()) {
                throw new BusinessException("下架模型失败,resp " + resp);
            }
        }
        tTaskType.setModelStatus(operateStatus);

        return taskTypeMapper.updateById(tTaskType) > 0;
    }

    @Override
    public Boolean setPlatformModelLabel(PlatformModelLabelRequest platformModelLabelRequest) {
        QueryWrapper<TTaskType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TTaskType::getTaskTypeCode, platformModelLabelRequest.getTaskTypeCode()).eq(TTaskType::getIsdel, 0).last(" limit 1");
        TTaskType tTaskType = taskTypeMapper.selectOne(queryWrapper);
        if (null == tTaskType) {
            throw new BusinessException("参数错误，未找到对应的算法模型");
        }
        if (StrUtil.isBlank(platformModelLabelRequest.getModelFeatureLabels()) && StrUtil.isBlank(platformModelLabelRequest.getModelManufacturer())) {
            throw new BusinessException("参数错误，标签不能为空");
        }
        tTaskType.setModelFeatureLabels(platformModelLabelRequest.getModelFeatureLabels());
        tTaskType.setModelManufacturer(platformModelLabelRequest.getModelManufacturer());
        return taskTypeMapper.updateById(tTaskType) > 0;
    }

    @Override
    public String getPlatformModelLabel(String taskTypeCode) {
        QueryWrapper<TTaskType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TTaskType::getTaskTypeCode, taskTypeCode).eq(TTaskType::getIsdel, 0).last(" limit 1");
        TTaskType tTaskType = taskTypeMapper.selectOne(queryWrapper);
        if (null == tTaskType) {
            throw new BusinessException("参数错误，未找到对应的算法模型");
        }
        return tTaskType.getModelFeatureLabels();
    }

    @Override
    public List<String> getOdModelLabel(String taskTypeCode) {
        QueryWrapper<TTaskType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TTaskType::getTaskTypeCode, taskTypeCode).eq(TTaskType::getIsdel, 0).last(" limit 1");
        TTaskType tTaskType = taskTypeMapper.selectOne(queryWrapper);
        if (null == tTaskType) {
            throw new BusinessException("参数错误，未找到对应的算法模型");
        }
        List<AppConfigDTO> modelInitConfig = modelConfigDesignService.getModelInitConfig(tTaskType.getId());
        ArrayList<String> labels = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(modelInitConfig)) {
            for (AppConfigDTO a : modelInitConfig) {
                if (CollectionUtil.isNotEmpty(a.getContains())) {
                    for (AppConfigDTO.Contains b : a.getContains()) {
                        if (CollectionUtil.isNotEmpty(b.getContains())) {
                            for (AppConfigDTO.SecondContains c : b.getContains()) {
                                if (StrUtil.isNotBlank(c.getLabel())) {
                                    labels.add(c.getLabel());
                                }
                            }
                        }
                    }
                }
            }
        }
        return labels;
    }

    @Override
    public BaseResp installV3() {
        JSONObject decryptData = (JSONObject) authenticationManagerService.getDecryptData().getData();
        String authCode = authenticationManagerService.getRegistrationCode();
        hubGpuCheck();
        return new BaseResp(appManageRpcService.installV3(decryptData, authCode));
    }

    @Override
    public BaseResp installV35(AppManageReqVO reqVO) {
        JSONObject decryptData = (JSONObject) authenticationManagerService.getDecryptData().getData();
        String authCode = authenticationManagerService.getRegistrationCode();
        hubGpuCheck();
        return new BaseResp(appManageRpcService.installV35(reqVO, decryptData, authCode));
    }

    @Override
    public BaseResp uninstall(AppManageReqVO reqVO) {
        String taskTypeCode = reqVO.getTaskTypeCode();
        if (org.apache.commons.lang3.StringUtils.isEmpty(taskTypeCode)) {
            throw new AppManageException("500", "应用唯一值不能为空");
        }
        TTaskType taskType = taskTypeMapper.selectOne(Wrappers.<TTaskType>lambdaQuery().eq(TTaskType::getTaskTypeCode, taskTypeCode).eq(TTaskType::getIsdel, false));
        if (taskType == null) {
            throw new AppManageException("500", "未匹配到应用");
        }
        appManageRpcService.uninstall(reqVO);
        //比对算法删库
        if (AbilityApiTypeEnum.COMPARE.getCodeInteger().equals(taskType.getAbilityEnum()) && StrUtil.isNotBlank(taskType.getMatchUploadUrl())) {
            TaskTypeEntity taskTypeEntity = new TaskTypeEntity();
            taskTypeEntity.setTaskTypeCode(taskType.getTaskTypeCode());
            taskTypeEntity.setMatchUploadUrl(taskType.getMatchUploadUrl());
            comparisonAbilityService.uninstallComparison(taskTypeEntity);
        }

        //删除缓存
        String authIdHolder = DubboDataContext.getAuthIdHolder();
        cacheService.deleteActionIdByCode(authIdHolder);
        cacheService.deleteActionIdByUser(authIdHolder);
        cacheService.deleteUserAbility(authIdHolder + "#" + taskType.getActionId());
        cacheService.deleteAbilityEntityCache(authIdHolder + "#" + taskType.getActionId());
        taskAbilityRpcService.deleteAbilityEntityCache(authIdHolder + "#" + taskType.getActionId());
        cacheService.deleteUserV3Ability(authIdHolder + "#" + taskType.getActionId());
        cacheService.deleteUserV35Ability(authIdHolder + "#" + taskType.getActionId());
        if (taskType.getModelStatus() == 1) {
            //需要下架
            String actionId = BooleanUtil.isTrue(taskType.getIsRegister()) ? StrUtil.subBefore(taskType.getActionId(), "-", true) : taskType.getActionId();
            downPlateModel(actionId);
        }

        return new BaseResp();
    }

    /**
     * 下架
     *
     * @param actionId
     */
    private void downPlateModel(String actionId) {
        ModelOperationRequest modelOperationRequest = new ModelOperationRequest();
        modelOperationRequest.setModelId(actionId);
        modelOperationRequest.setStatus(0);
        com.linker.core.base.baseclass.BaseResp<Boolean> resp = apiBackService.syncPlatformModel(workflowProperties.getEndpoint() + workflowProperties.getSyncModelRouter(),
                generateLinkerHeader(),
                modelOperationRequest);
        log.info("下架模型,resp{} ", resp);
    }

    @Override
    public BaseResp update(AppManageReqVO reqVO) {
        String authCode = authenticationManagerService.getRegistrationCode();
        return new BaseResp(appManageRpcService.update(reqVO, authCode));
    }

    @Override
    public BaseResp rollback(AppManageReqVO reqVO) {
        String authCode = authenticationManagerService.getRegistrationCode();
        appManageRpcService.rollback(reqVO, authCode);
        return new BaseResp();
    }


    // 因为现在回调接口没有实际调用，所以在回退版本里面暂时不写逻辑了，不过预留接口
    @Override
    public BaseResp rollbackCallback(JSONObject jsonObject) {
        return null;
    }

    @Override
    public BaseResp determine(String timestamp) {
        appManageRpcService.determine(timestamp);
        return new BaseResp();
    }


    @Override
    public BaseResp cancel(String timestamp) {
        appManageRpcService.cancel(timestamp);
        return new BaseResp();
    }

    @Override
    public BaseResp getAbnormalCount(String orgId) {
        return new BaseResp(appManageRpcService.getAbnormalCount(orgId));
    }

    public boolean isExistTask(String taskTypeCode) {
        return appManageRpcService.isExistTask(taskTypeCode);
    }

    @Override
    public BaseResp enable(AppManageReqVO reqVO) {
        String enable = appManageRpcService.enable(reqVO);
        //删除缓存
        String authIdHolder = DubboDataContext.getAuthIdHolder();
        cacheService.deleteActionIdByCode(authIdHolder);
        cacheService.deleteActionIdByUser(authIdHolder);
        if (StrUtil.isNotBlank(reqVO.getActionId())) {
            cacheService.deleteUserAbility(authIdHolder + "#" + reqVO.getActionId());
            cacheService.deleteAbilityEntityCache(authIdHolder + "#" + reqVO.getActionId());
            taskAbilityRpcService.deleteAbilityEntityCache(authIdHolder + "#" + reqVO.getActionId());
            cacheService.deleteUserV3Ability(authIdHolder + "#" + reqVO.getActionId());
            cacheService.deleteUserV35Ability(authIdHolder + "#" + reqVO.getActionId());
        }
        return new BaseResp(enable);
    }

    @Override
    public boolean switchApp(AppManageSwitchDTO switchDTO) {
        TaskTypeEntity model = taskTypeMapper.getActionId(switchDTO.getTaskTypeCode());
        String actionId = model.getActionId();
        boolean b = appManageRpcService.switchApp(switchDTO);
        //删除缓存
        String authIdHolder = DubboDataContext.getAuthIdHolder();
        cacheService.deleteActionIdByCode(authIdHolder);
        cacheService.deleteActionIdByUser(authIdHolder);
        if (StrUtil.isNotBlank(actionId)) {
            cacheService.deleteUserAbility(authIdHolder + "#" + actionId);
            cacheService.deleteAbilityEntityCache(authIdHolder + "#" + actionId);
            taskAbilityRpcService.deleteAbilityEntityCache(authIdHolder + "#" + actionId);
            cacheService.deleteAbilityEntityCache(authIdHolder + "#" + StrUtil.subBefore(actionId, "-", true));
            taskAbilityRpcService.deleteAbilityEntityCache(authIdHolder + "#" + StrUtil.subBefore(actionId, "-", true));
        }
        if (switchDTO.getEnable() == 0 && model.getModelStatus() == 1) {
            //需要下架
            downPlateModel(BooleanUtil.isTrue(model.getIsRegister()) ? StrUtil.subBefore(model.getActionId(), "-", true) : model.getActionId());
        }
        return b;
    }

    @Override
    public BaseResp calculateMsgList(CalculateMsgListReq req) {
        return new BaseResp(appManageRpcService.calculateMsgList(req));
    }

    @Override
    public BaseResp saveCalculateMsg(TTaskTypeOrgSetting req) {
        return new BaseResp(appManageRpcService.saveCalculateMsg(req));
    }

    @Override
    public BaseResp syncInstall(AbilityAddWithOutOmDTO abilityAdd) {
        String authIdHolder = DubboDataContext.getAuthIdHolder();
        //一键注册校验系统是否激活
        BaseResp baseResp = authenticationManagerService.getDecryptData();

        if (!StringUtils.equals(baseResp.getCode(), "0")) {
            return new BaseResp("请先激活当前系统");
        }
        TTaskType tTaskType = appManageRpcService.syncInstall(abilityAdd);

        cacheService.deleteActionIdByCode(authIdHolder);
        cacheService.deleteAbilityEntityCache(authIdHolder + "#" + tTaskType.getActionId());
        taskAbilityRpcService.deleteAbilityEntityCache(authIdHolder + "#" + tTaskType.getActionId());
        cacheService.deleteActionIdByUser(authIdHolder);
        cacheService.deleteUserAbility(authIdHolder + "#" + tTaskType.getActionId());
        return new BaseResp(tTaskType);
    }

    @Override
    public BaseResp onlineInstall(AppManageReqVO reqVO) {
        JSONObject decryptData = (JSONObject) authenticationManagerService.getDecryptData().getData();
        String authCode = authenticationManagerService.getRegistrationCode();
        return new BaseResp(appManageRpcService.onlineInstall(reqVO, decryptData, authCode));
    }

    @Override
    public BaseResp offlineInstall(AppManageReqVO reqVO) {
        JSONObject decryptData = (JSONObject) authenticationManagerService.getDecryptData().getData();
        String authCode = authenticationManagerService.getRegistrationCode();
        return new BaseResp(/*appManageRpcService.offlineInstall(reqVO, decryptData, authCode)*/);
    }

    @Override
    public BaseResp offlineActivation(AppManageReqVO reqVO) {
        String authCode = authenticationManagerService.getRegistrationCode();
        return new BaseResp(appManageRpcService.offlineActivation(reqVO, authCode));
    }

    @Override
    public BaseResp blockUpdate() throws Exception {

        List<MessageQueueDto> queueInfo = taskQueueInfoPrcService.getQueueInfo(1);

        if (CollectionUtil.isNotEmpty(queueInfo)) {
            List<Integer> ids = new ArrayList();
            for (MessageQueueDto messageQueueDto : queueInfo) {
                if (messageQueueDto.getLineCount() > lineCount) {
                    ids.add(messageQueueDto.getQueueId());
                }
            }
            return new BaseResp(appManageRpcService.blockUpdate(ids));
        }

        return new BaseResp();
    }

    @Override
    public Boolean syncInstallUpdate(RegisterAbilityUpdateRequest request, TaskTypeEntity userAbility) {

        TTaskType tTaskType = taskTypeMapper.selectById(userAbility.getId());
        if (tTaskType == null) {
            throw new BusinessException("未匹配到应用");
        }
        boolean flag = false;
        if (StrUtil.isNotBlank(request.getAbilityName())) {
            tTaskType.setName(request.getAbilityName());
            flag = true;
        }
        if (StrUtil.isNotBlank(request.getAppDescription())) {
            tTaskType.setAppDescription(request.getAppDescription());
            flag = true;
        }
        if (StrUtil.isNotBlank(request.getAbilityRemark())) {
            tTaskType.setRemark(request.getAbilityRemark());
            flag = true;
        }
        //更新算法信息数据
        if (flag) {
            taskTypeMapper.updateById(tTaskType);
        }

        if (StrUtil.isNotBlank(request.getApplicationJson())) {
            //获取原始配置信息
            List<AppConfigDTO> modelInitConfig = modelConfigDesignService.getModelInitConfig(userAbility.getId());
            if (CollectionUtil.isNotEmpty(modelInitConfig)) {
                if (!JSON.toJSONString(modelInitConfig).equals(request.getApplicationJson())) {
                    TaskTypeConfigAiEntity taskTypeConfigAiEntity = getTaskTypeConfigAiEntity(userAbility);
                    taskTypeConfigAiEntity.setConfig(request.getApplicationJson());
                    taskTypeConfigAiService.updateById(taskTypeConfigAiEntity);
                }
            } else {
                TaskTypeConfigAiEntity taskTypeConfigAiEntity = getTaskTypeConfigAiEntity(userAbility);
                taskTypeConfigAiEntity.setConfig(request.getApplicationJson());
                taskTypeConfigAiService.updateById(taskTypeConfigAiEntity);
            }
            modelConfigDesignService.deleteModelInitConfigCache(userAbility.getId().toString());
        }

        //更新并发数
        if (request.getAbilityConcurrent() != null) {
            AbilityEntity abilityEntity = abilityRpcService.getAbilityEntity(tTaskType.getAbility());
            if (abilityEntity != null) {
                //获取limit表；更新数据
                LambdaUpdateWrapper<LhTaskAppAbilityLimitEntity> update = new LambdaUpdateWrapper<>();
                update.set(LhTaskAppAbilityLimitEntity::getConcurrentLimit, request.getAbilityConcurrent());
                update.eq(LhTaskAppAbilityLimitEntity::getAbilityId, abilityEntity.getOperatorId());
                lhTaskAppAbilityLimitService.update(null, update);

                //更新limit redis数据
                stringRedisTemplate.delete(RedisConstant.TASK_ABILITY_LIMIT_KEY + abilityEntity.getOperatorId());
            }
        }
        return true;
    }

    /**
     * 获取配置信息
     *
     * @param taskTypeEntity
     * @return
     */
    private TaskTypeConfigAiEntity getTaskTypeConfigAiEntity(TaskTypeEntity taskTypeEntity) {
        QueryWrapper<TaskTypeConfigAiEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(TaskTypeConfigAiEntity::getConfig).eq(TaskTypeConfigAiEntity::getTaskTypeId, taskTypeEntity.getId()).last(" limit 1");
        return taskTypeConfigAiService.getOne(queryWrapper);
    }
}
