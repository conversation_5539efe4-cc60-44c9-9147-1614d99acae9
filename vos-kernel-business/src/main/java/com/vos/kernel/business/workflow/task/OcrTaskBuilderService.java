package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.MultiModelEmbeddingRequest;
import com.linker.omos.client.domain.request.OcrRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class OcrTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Resource
    Validator validator;

    public OcrTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.OCR.getCode());
    }


    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        OcrRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, OcrRequest.class);
        } catch (Exception e) {
            throw new BusinessException("ocr调用参数解析失败");
        }
        abilityRequest.setModel(taskAddRequest.getModel());

        Set<ConstraintViolation<OcrRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        String inputType = abilityRequest.getInputType();
        if (!"base64".equals(inputType) && !"image_url".equals(inputType)) {
            throw new BusinessException("输入类型只能是base64|image_url的一种");
        }

        TaskTypeAndAbility taskAbility = getTaskAbility(abilityRequest.getModel(), workflowTaskTypeEnum);
        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.OCR.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setSubList(buildOcrTaskInfo(abilityRequest, taskAbility, taskAdd.getAppSourceId()));

        //缓存元数据
        ModelMetaInfo embeddingMetaInfo = new ModelMetaInfo(taskAddRequest.getCallbackUrl(), DubboDataContext.getAuthIdHolder(), taskAbility.getActionId(), new Date().getTime());
        embeddingMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        embeddingMetaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(embeddingMetaInfo));
        return taskAdd;
    }

    /**
     * 构造文本向量化参数
     *
     * @param ocrRequest
     * @param abilityEntity
     * @return
     */
    private List<TaskSubDTO> buildOcrTaskInfo(OcrRequest ocrRequest, TaskTypeAndAbility abilityEntity, String appSourceId) {
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(ocrRequest.getTaskOrder(), ocrRequest.getVideoClusterSource(), abilityEntity);
        //base64图片处理
        String s = handleImage(ocrRequest.getInputType(), ocrRequest.getInput(), appSourceId);
        ocrRequest.setInput(s);
        taskSubDTO.setRequestJson(buildRequestStr(ocrRequest));
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }

    /**
     * 构造算法请求参数
     *
     * @param ocrRequest
     * @return
     */
    private String buildRequestStr(OcrRequest ocrRequest) {
        List<AiExecuteImageParamDTO> src = new ArrayList<>();
        String eventTime = DateUtil.getSimpleYMDHMS(LocalDateTime.now());
        String videoValidation = String.valueOf(IdWorker.nextId());
        String taskId = String.valueOf(IdWorker.nextId());
        AiExecuteImageParamDTO aiExecuteImageParamDTO = new AiExecuteImageParamDTO();
        aiExecuteImageParamDTO.setImageId("0");
        aiExecuteImageParamDTO.setVideoId("ocr");
        aiExecuteImageParamDTO.setSrcType(EmbeddingInputEnum.matchApiType(ocrRequest.getInputType()));
        aiExecuteImageParamDTO.setData(ocrRequest.getInput());
        aiExecuteImageParamDTO.setEventTime(eventTime);
        aiExecuteImageParamDTO.setTaskId(taskId);
        aiExecuteImageParamDTO.setOrgId(DubboDataContext.getAuthIdHolder());
        aiExecuteImageParamDTO.setVideoValidation(videoValidation);
        src.add(aiExecuteImageParamDTO);

        return buildModelRequestStr(src, taskId);
    }


}
