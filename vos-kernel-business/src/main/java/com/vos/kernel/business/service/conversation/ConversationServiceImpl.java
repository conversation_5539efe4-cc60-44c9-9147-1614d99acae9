package com.vos.kernel.business.service.conversation;

import com.alibaba.fastjson2.JSONObject;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.dto.CreateConversationRequest;
import com.vos.kernel.business.dto.CreateConversationResponse;
import com.vos.kernel.business.dto.agent.AgentConfig;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.enums.StatusEnum;
import com.vos.kernel.business.service.agent.IAgentManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2024/12/10 08:35
 * @DESC: 聊天会话实现类
 */
@Service
public class ConversationServiceImpl implements IConversationService {

    @Autowired
    private IAgentManagementService agentManagementService;

    @Override
    public CreateConversationResponse createConversation(CreateConversationRequest createConversationRequest) {

        String agentId = createConversationRequest.getAgentId();
        AgentInfoEntity agentInfoEntity = agentManagementService.getAgentInfo(agentId);
        if (agentInfoEntity == null) {
            throw new BusinessException("智能体不存在|" + agentId);
        }
        if (StatusEnum.RUNNING.type != agentInfoEntity.getRunning()) {
            throw new BusinessException("智能体非运行状态|" + agentId);
        }
        CreateConversationResponse resp = new CreateConversationResponse();
        resp.setConversationId(UUID.randomUUID().toString().replaceAll("-", ""));
        resp.setCreateTime(System.currentTimeMillis());
        resp.setAgentName(agentInfoEntity.getAgentName());
        resp.setIcon(agentInfoEntity.getIconPath());
        resp.setDescription(agentInfoEntity.getDescription());
        String preChat = agentInfoEntity.getPreChat();
        if (preChat != null) {
            AgentConfig.PreChatInfo preChatObj = JSONObject.parseObject(preChat, AgentConfig.PreChatInfo.class);
            resp.setWelcome(preChatObj.getWelcomeMessage());
            List<AgentConfig.PresetQuestion> presetQuestions = preChatObj.getPresetQuestions();
            if (presetQuestions != null) {
                for (AgentConfig.PresetQuestion question : presetQuestions) {
                    if (!question.getEnable()) {
                        continue;
                    }
                    resp.getPresetQuestions().add(question.getQuestion());
                }
            }
        }
        return resp;
    }
}
