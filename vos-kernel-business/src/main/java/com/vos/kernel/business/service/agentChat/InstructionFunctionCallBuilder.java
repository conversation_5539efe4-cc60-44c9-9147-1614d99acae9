package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.agent.AgentConfigDTO;
import com.vos.kernel.business.dto.agent.KnowledgeConfig;
import com.vos.kernel.business.dto.agent.QuickInstructionInfo;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.common.entity.InstructionFunctionCallRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年06月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class InstructionFunctionCallBuilder {


    /**
     * 快速指令 function call
     *
     * @param streamChatRequest
     * @param agentInfoEntity
     */
    public void buildTemplateParams(boolean isDebug, StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity,
                                    Map<String, Object> context, Map<String, Object> entryInput) {
        Map<String, Object> metaData = streamChatRequest.getMetaData();

        if (isDebug) {
            Map<String, String> extraParams = streamChatRequest.getExtraParams();
            if (extraParams != null) {
                String knowledgeConfig = extraParams.get("knowledgeConfig");
                //将云盘配置放入上下文
                if (StrUtil.isNotBlank(knowledgeConfig)) {
                    context.put("knowledgeConfig", JSON.parseObject(knowledgeConfig, KnowledgeConfig.class));
                }
                //哨兵智能体任务ID
                String taskIdList = extraParams.get("taskIdList");
                if (StrUtil.isNotBlank(taskIdList)) {
                    context.put("taskIdList", JSON.parseArray(taskIdList, String.class));
                }
            }
            if (MapUtil.isNotEmpty(metaData) && metaData.containsKey("skill")) {
                Object skill = metaData.get("skill");
                metaData.remove("skill");
                if (null != skill) {
                    InstructionFunctionCallRequest instructionFunctionCallRequest = JSON.parseObject(JSON.toJSONString(skill), InstructionFunctionCallRequest.class);
                    Map<String, Object> skillParams = instructionFunctionCallRequest.getSkillParams();
                    if (MapUtil.isNotEmpty(skillParams)) {
                        entryInput.putAll(skillParams);
                    }
                }
            }

        } else {
            //是否为skill技能调用
            if (MapUtil.isNotEmpty(metaData) && metaData.containsKey("skill") && StrUtil.isNotBlank(agentInfoEntity.getAgentConfig())) {
                Object skill = metaData.get("skill");
                if (null != skill) {
                    String jsonString = JSON.toJSONString(skill);
                    log.info("skill:{}", jsonString);
                    AgentConfigDTO agentConfig = JSON.parseObject(agentInfoEntity.getAgentConfig(), AgentConfigDTO.class);
                    InstructionFunctionCallRequest instructionFunctionCallRequest = JSON.parseObject(jsonString, InstructionFunctionCallRequest.class);

                    if (StrUtil.isBlank(instructionFunctionCallRequest.getWorkflowId())) {
                        throw new BusinessException("技能对应的workflowId为空");
                    }
                    if (StrUtil.isBlank(instructionFunctionCallRequest.getWorkflowVersion())) {
                        throw new BusinessException("技能对应的workflow版本为空");
                    }
                    List<QuickInstructionInfo> quickInstructionInfo = agentConfig.getQuickInstructionInfo();
                    if (CollectionUtil.isEmpty(quickInstructionInfo)) {
                        throw new BusinessException("当前智能体不存在快速指令配置");
                    }
                    Map<String, Object> skillParams = instructionFunctionCallRequest.getSkillParams();
                    if (MapUtil.isEmpty(skillParams)) {
                        throw new BusinessException("技能参数信息为空");
                    }
                    Optional<QuickInstructionInfo> instructionInfoConfig = quickInstructionInfo.stream().filter(t -> instructionFunctionCallRequest.getWorkflowId().equals(t.getWorkflowId()))
                            .filter(i -> instructionFunctionCallRequest.getWorkflowVersion().equals(i.getWorkflowVersion().toString()))
                            .findFirst();
                    if (!instructionInfoConfig.isPresent()) {
                        throw new BusinessException("当前智能体不存在参数快速指令配置," + instructionFunctionCallRequest.getWorkflowId() + "," + instructionFunctionCallRequest.getWorkflowVersion());
                    }
                    QuickInstructionInfo quickInstructionInfoConf = instructionInfoConfig.get();
                    //校验参数
                    //替换参数
                    agentInfoEntity.setWorkflow(quickInstructionInfoConf.getWorkflowId());
                    agentInfoEntity.setWorkflowVersion(instructionFunctionCallRequest.getWorkflowVersion());
                    //将云盘配置放入上下文
                    if (null != agentConfig.getKnowledgeConfig()) {
                        context.put("knowledgeConfig", agentConfig.getKnowledgeConfig());
                    }
                    //哨兵智能体任务ID
                    if (CollectionUtil.isNotEmpty(agentConfig.getSentinelTaskIds())) {
                        context.put("taskIdList", agentConfig.getSentinelTaskIds());
                    }
                    //替换指令参数
                    entryInput.putAll(skillParams);
                    metaData.remove("skill");
                }
            }
        }

    }
}
