package com.vos.kernel.business.controller.v2;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.linker.basic.exception.BusinessException;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.conver.AiConfigConvertMapper;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.service.appconfig.IModelConfigDesignService;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年07月24日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/v2")
@Api(value = "对外配置接口v2相关")
public class ApiConfigController {

    @Resource
    ICacheService cacheService;


    @Resource
    IModelConfigDesignService modelConfigDesignService;

    /**
     * 获取模型配置
     *
     * @param modelConfigGetRequest
     * @return AppConfigShowResponse  AppConfigResponse
     */
    @Token
    @PostMapping("/images/config/get")
    public Object getModelConfig(@RequestBody @Valid ModelConfigGetRequest modelConfigGetRequest) {
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + modelConfigGetRequest.getModel());
        if (null == userAbility) {
            throw new BusinessException("租户无此算法权限");
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (StrUtil.isBlank(modelConfigGetRequest.getConfigCode())) {
            //获取原始配置信息
            List<AppConfigDTO> modelInitConfig = modelConfigDesignService.getModelInitConfig(userAbility.getId());
            stopWatch.stop();
            if (CollectionUtil.isEmpty(modelInitConfig)) {
                if (BooleanUtil.isTrue(modelConfigGetRequest.getForShow())) {
                    AppConfigShowResponse appConfigShowResponse = new AppConfigShowResponse();
                    appConfigShowResponse.setModel(modelConfigGetRequest.getModel());
                    appConfigShowResponse.setData(new ArrayList<>());
                    appConfigShowResponse.setCode(200);
                    appConfigShowResponse.setTook(stopWatch.getTotalTimeMillis());
                } else {
                    AppConfigResponse appConfigResponse = new AppConfigResponse();
                    appConfigResponse.setModel(modelConfigGetRequest.getModel());
                    appConfigResponse.setCode(200);
                    appConfigResponse.setTook(stopWatch.getTotalTimeMillis());
                    AppConfigItemResponse appConfigItemResponse = new AppConfigItemResponse();
                    appConfigItemResponse.setConfigCode("default");
                    appConfigItemResponse.setAppConfig(new ArrayList<>());
                    appConfigResponse.setData(appConfigItemResponse);
                    return appConfigResponse;
                }
//                return buildAppConfigResponse(modelConfigGetRequest.getModel(), stopWatch.getTotalTimeMillis(), null, "该算法无高级配置参数");
            }
            if (BooleanUtil.isTrue(modelConfigGetRequest.getForShow())) {
                //前端展示用
                return buildAppConfigShowResponse(modelConfigGetRequest.getModel(), "", "", stopWatch.getTotalTimeMillis(), modelInitConfig);
            } else {
                //对接接口用
                return buildAppConfigResponse(modelConfigGetRequest.getModel(), stopWatch.getTotalTimeMillis(), modelInitConfig, null);
            }
        } else {
            String configCode = modelConfigGetRequest.getConfigCode();
            ModelCustomConfigDTO modelCustomConfig = modelConfigDesignService.getModelCustomConfigWithName(configCode);
            if (modelCustomConfig == null || CollectionUtil.isEmpty(modelCustomConfig.getAppConfig())) {
                return buildAppConfigResponse(modelConfigGetRequest.getModel(), stopWatch.getTotalTimeMillis(), null, configCode + ",未查询到订制配置");
            }
            stopWatch.stop();
            //获取指定配置信息
            if (BooleanUtil.isTrue(modelConfigGetRequest.getForShow())) {
                //前端展示用
                List<AppConfigDTO> modelInitConfig = modelConfigDesignService.getModelInitConfig(userAbility.getId());
                List<AppConfigDTO> appConfig = JSONArray.parseArray(JSON.toJSONString(modelInitConfig), AppConfigDTO.class);
                //替换订制数据
                replaceAppConfig(appConfig, modelCustomConfig.getAppConfig());
                return buildAppConfigShowResponse(modelConfigGetRequest.getModel(), modelCustomConfig.getConfigName(), modelCustomConfig.getConfigRemark(), stopWatch.getTotalTimeMillis(), appConfig);
            } else {
                //对接接口用
                AppConfigResponse appConfigResponse = new AppConfigResponse();
                appConfigResponse.setModel(modelConfigGetRequest.getModel());
                appConfigResponse.setCode(200);
                appConfigResponse.setTook(stopWatch.getTotalTimeMillis());
                appConfigResponse.setData(new AppConfigItemResponse(modelConfigGetRequest.getModel(), modelCustomConfig.getConfigName(), modelCustomConfig.getConfigRemark(), modelCustomConfig.getAppConfig()));
                return appConfigResponse;
            }
        }
    }


    /**
     * 创建配置
     *
     * @param appConfigCreateRequest
     * @return AppConfigCreateUpdateResponse
     */
    @Token
    @PostMapping("/images/config/create")
    public AppConfigCreateUpdateResponse createModelConfig(@RequestBody @Valid AppConfigCreateRequest appConfigCreateRequest) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + appConfigCreateRequest.getModel());
        if (null == userAbility) {
            throw new BusinessException("租户无此算法权限");
        }
        AppConfigCreateUpdateResponse appConfigCreateUpdateResponse = new AppConfigCreateUpdateResponse();
        appConfigCreateUpdateResponse.setModel(appConfigCreateRequest.getModel());
        List<AppConfigDTO> modelInitConfig = modelConfigDesignService.getModelInitConfig(userAbility.getId());
        if (CollectionUtil.isEmpty(modelInitConfig)) {
            stopWatch.stop();
            appConfigCreateUpdateResponse.setTook(stopWatch.getTotalTimeMillis());
            appConfigCreateUpdateResponse.setCode(50);
            appConfigCreateUpdateResponse.setError("该算法无高级配置不可创建");
            return appConfigCreateUpdateResponse;
        }

        try {
            List<AppConfigThirdContainsResponse> appConfigThirdContainsResponses = parseAppConfig(modelInitConfig);
            List<String> keys = appConfigThirdContainsResponses.stream().map(AppConfigThirdContainsResponse::getConfigKey).collect(Collectors.toList());
            List<String> createKeys = appConfigCreateRequest.getAppConfig().stream().map(AppConfigCreateUpdateItemRequest::getConfigKey).collect(Collectors.toList());
            List<String> sub = CollectionUtil.subtractToList(createKeys, keys);
            if (CollectionUtil.isNotEmpty(sub)) {
                throw new BusinessException("存在非法configKey");
            }
            String modelConfig = modelConfigDesignService.createModelConfig(appConfigCreateRequest, userAbility, appConfigThirdContainsResponses);
            appConfigCreateUpdateResponse.setCode(200);
            appConfigCreateUpdateResponse.setData(new AppConfigListItem(modelConfig));
        } catch (Exception e) {
            appConfigCreateUpdateResponse.setCode(50);
            appConfigCreateUpdateResponse.setError(e.getMessage());
        }
        stopWatch.stop();
        appConfigCreateUpdateResponse.setTook(stopWatch.getTotalTimeMillis());

        return appConfigCreateUpdateResponse;

    }

    /**
     * 更新配置
     *
     * @param appConfigUpdateRequest
     * @return AppConfigCreateUpdateResponse
     */
    @Token
    @PostMapping("/images/config/update")
    public AppConfigCreateUpdateResponse updateModelConfig(@RequestBody @Valid AppConfigUpdateRequest appConfigUpdateRequest) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String model = appConfigUpdateRequest.getModel();
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + model);
        if (null == userAbility) {
            throw new BusinessException("租户无此算法权限");
        }
        AppConfigCreateUpdateResponse appConfigCreateUpdateResponse = new AppConfigCreateUpdateResponse();
        appConfigCreateUpdateResponse.setModel(model);
        String configCode = appConfigUpdateRequest.getConfigCode();

        List<AppConfigThirdContainsResponse> modelCustomConfig = modelConfigDesignService.getModelCustomConfig(configCode);
        if (CollectionUtil.isEmpty(modelCustomConfig)) {
            stopWatch.stop();
            appConfigCreateUpdateResponse.setTook(stopWatch.getTotalTimeMillis());
            appConfigCreateUpdateResponse.setCode(50);
            appConfigCreateUpdateResponse.setError(configCode + ",未找到对应的配置不可更新");
            return appConfigCreateUpdateResponse;
        }
        try {
            List<String> keys = modelCustomConfig.stream().map(AppConfigThirdContainsResponse::getConfigKey).distinct().collect(Collectors.toList());
            List<String> updateKeys = appConfigUpdateRequest.getAppConfig().stream().map(AppConfigCreateUpdateItemRequest::getConfigKey).collect(Collectors.toList());
            List<String> sub = CollectionUtil.subtractToList(updateKeys, keys);
            if (CollectionUtil.isNotEmpty(sub)) {
                throw new BusinessException("存在非法configKey,不可编辑");
            }
            String modelConfig = modelConfigDesignService.updateModelConfig(appConfigUpdateRequest, userAbility, modelCustomConfig);
            modelConfigDesignService.deleteModelCustomConfigCache(appConfigUpdateRequest.getConfigCode());
            modelConfigDesignService.deleteModelCustomConfigNameCache(appConfigUpdateRequest.getConfigCode());
            appConfigCreateUpdateResponse.setCode(200);
            appConfigCreateUpdateResponse.setData(new AppConfigListItem(modelConfig));
        } catch (Exception e) {
            appConfigCreateUpdateResponse.setCode(50);
            appConfigCreateUpdateResponse.setError(e.getMessage());
        }
        stopWatch.stop();
        appConfigCreateUpdateResponse.setTook(stopWatch.getTotalTimeMillis());

        return appConfigCreateUpdateResponse;

    }

    /**
     * 配置列表
     *
     * @param appConfigListRequest
     * @return AppConfigListResponse
     */
    @Token
    @PostMapping("/images/config/getList")
    public AppConfigListResponse updateModelConfig(@RequestBody @Valid AppConfigListRequest appConfigListRequest) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AppConfigListResponse appConfigListResponse = new AppConfigListResponse();
        appConfigListResponse.setModel(appConfigListRequest.getModel());
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + appConfigListRequest.getModel());
        if (null == userAbility) {
            throw new BusinessException("租户无此算法权限");
        }
        appConfigListResponse.setData(modelConfigDesignService.getCustomAppConfigList(userAbility, appConfigListRequest));
        stopWatch.stop();
        appConfigListResponse.setTook(stopWatch.getTotalTimeMillis());
        appConfigListResponse.setCode(200);

        return appConfigListResponse;

    }

    /**
     * 配置删除
     *
     * @param appConfigDeleteRequest
     * @return AppConfigDeleteResponse
     */
    @Token
    @PostMapping("/images/config/delete")
    public AppConfigDeleteResponse deleteModelConfig(@RequestBody @Valid AppConfigDeleteRequest appConfigDeleteRequest) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AppConfigDeleteResponse appConfigDeleteResponse = new AppConfigDeleteResponse();
        appConfigDeleteResponse.setModel(appConfigDeleteRequest.getModel());
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + appConfigDeleteRequest.getModel());
        if (null == userAbility) {
            throw new BusinessException("租户无此算法权限");
        }
        //是否存在
        String configCode = appConfigDeleteRequest.getConfigCode();
        List<AppConfigThirdContainsResponse> modelCustomConfig = modelConfigDesignService.getModelCustomConfig(configCode);
        if (CollectionUtil.isEmpty(modelCustomConfig)) {
            stopWatch.stop();
            appConfigDeleteResponse.setTook(stopWatch.getTotalTimeMillis());
            appConfigDeleteResponse.setCode(50);
            appConfigDeleteResponse.setError(configCode + ",未找到对应的配置不可删除");
            return appConfigDeleteResponse;
        }
        appConfigDeleteResponse.setData(modelConfigDesignService.deleteModelCustomConfig(configCode));
        stopWatch.stop();
        appConfigDeleteResponse.setTook(stopWatch.getTotalTimeMillis());
        appConfigDeleteResponse.setCode(200);
        //删除缓存
        modelConfigDesignService.deleteModelCustomConfigCache(configCode);
        modelConfigDesignService.deleteModelCustomConfigNameCache(configCode);
        return appConfigDeleteResponse;

    }

    /**
     * 将订制的数据返回给前端展示
     *
     * @param modelInitConfig
     * @param modelCustomConfig
     */
    private void replaceAppConfig(List<AppConfigDTO> modelInitConfig, List<AppConfigThirdContainsResponse> modelCustomConfig) {

        Map<String, AppConfigThirdContainsResponse> customeConfigMap = modelCustomConfig.stream().collect(Collectors.toMap(AppConfigThirdContainsResponse::getConfigKey, Function.identity()));

        for (AppConfigDTO a : modelInitConfig) {
            List<AppConfigDTO.Contains> contains = a.getContains();
            if (CollectionUtil.isNotEmpty(contains)) {
                for (AppConfigDTO.Contains c : contains) {
                    List<AppConfigDTO.SecondContains> contains1 = c.getContains();
                    if (CollectionUtil.isNotEmpty(contains1)) {
                        for (AppConfigDTO.SecondContains s : contains1) {
                            List<AppConfigDTO.ThirdContains> contains2 = s.getContains();
                            if (CollectionUtil.isNotEmpty(contains2)) {
                                for (AppConfigDTO.ThirdContains t : contains2) {
                                    if (customeConfigMap.containsKey(t.getKey())) {
                                        AppConfigThirdContainsResponse appConfigThirdContainsResponse = customeConfigMap.get(t.getKey());
                                        t.setValue(appConfigThirdContainsResponse.getConfigValue());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 显示数据
     *
     * @param model
     * @param took
     * @param modelInitConfig
     * @return
     */
    private AppConfigShowResponse buildAppConfigShowResponse(String model, String configName, String configRemark, long took, List<AppConfigDTO> modelInitConfig) {
        AppConfigShowResponse appConfigShowResponse = new AppConfigShowResponse();
        appConfigShowResponse.setModel(model);
        appConfigShowResponse.setConfigName(configName);
        appConfigShowResponse.setConfigRemark(configRemark);
        appConfigShowResponse.setData(modelInitConfig);
        appConfigShowResponse.setCode(200);
        appConfigShowResponse.setTook(took);
        return appConfigShowResponse;
    }

    /**
     * 构造返回数据
     *
     * @param model
     * @param took
     * @param modelInitConfig
     * @return
     */
    private AppConfigResponse buildAppConfigResponse(String model, long took, List<AppConfigDTO> modelInitConfig, String error) {
        AppConfigItemResponse data = null;
        if (CollectionUtil.isNotEmpty(modelInitConfig)) {
            AppConfigItemResponse appConfigItemResponse = new AppConfigItemResponse();
            appConfigItemResponse.setConfigCode("default");
            appConfigItemResponse.setAppConfig(parseAppConfig(modelInitConfig));
            data = appConfigItemResponse;
        }
        AppConfigResponse appConfigResponse = new AppConfigResponse();
        appConfigResponse.setModel(model);
        appConfigResponse.setCode(modelInitConfig == null ? 50 : 200);
        appConfigResponse.setTook(took);
        appConfigResponse.setData(data);
        appConfigResponse.setError(error);
        return appConfigResponse;
    }


    /**
     * 解析高级配置参数
     *
     * @param modelInitConfig
     * @return
     */
    private List<AppConfigThirdContainsResponse> parseAppConfig(List<AppConfigDTO> modelInitConfig) {
        List<AppConfigThirdContainsResponse> appConfigThirdContainsResponses = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(modelInitConfig)) {
            for (AppConfigDTO a : modelInitConfig) {
                List<AppConfigDTO.Contains> contains = a.getContains();
                if (CollectionUtil.isNotEmpty(contains)) {
                    for (AppConfigDTO.Contains c : contains) {
                        List<AppConfigDTO.SecondContains> contains1 = c.getContains();
                        if (CollectionUtil.isNotEmpty(contains1)) {
                            for (AppConfigDTO.SecondContains s : contains1) {
                                List<AppConfigDTO.ThirdContains> contains2 = s.getContains();
                                if (CollectionUtil.isNotEmpty(contains2)) {
                                    for (AppConfigDTO.ThirdContains t : contains2) {
                                        AppConfigThirdContainsResponse appConfigThirdContainsResponse = new AppConfigThirdContainsResponse();
                                        appConfigThirdContainsResponse.setConfigKey(t.getKey());
                                        appConfigThirdContainsResponse.setConfigType(t.getType());
                                        appConfigThirdContainsResponse.setConfigLabel(t.getLabel());
                                        appConfigThirdContainsResponse.setConfigValue(t.getValue());
                                        appConfigThirdContainsResponse.setControlOptionList(t.getControlOptionlist());
                                        appConfigThirdContainsResponse.setControlValidate(t.getControlValidate());
                                        appConfigThirdContainsResponses.add(appConfigThirdContainsResponse);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return appConfigThirdContainsResponses;
    }

}
