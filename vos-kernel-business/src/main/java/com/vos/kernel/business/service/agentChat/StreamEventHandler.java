package com.vos.kernel.business.service.agentChat;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.omagent.starter.manager.RedisStreamManager;
import com.vos.kernel.business.api.IV3ApiBackService;
import com.vos.kernel.business.constants.AgentosConstants;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.entity.UserVo;
import com.vos.kernel.business.wrapper.ChatDTOWrapper;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.ChatEventEnum;
import com.vos.kernel.business.service.chat.IChatHistoryService;
import com.vos.kernel.business.entity.ChatHistoryEntity;
import com.vos.kernel.business.mapper.ChatHistoryMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.stream.Subscription;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.fastjson2.JSONObject;
import com.vos.kernel.business.dto.Message;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/12/10 14:45
 * @DESC: 聊天时间处理器
 */
@Service
@Slf4j
public class StreamEventHandler {

    @Value("${workflow.endpoint}")
    private String workflowEndpoint;

    @Resource
    private IV3ApiBackService apiBackService;

    @Resource
    private RedisStreamManager redisStreamManager;

    @Value("${event.trigger.inner.endpoint}")
    private String callbackUrl;
    public static final String CHAT_STREAM_SIGN_START = "S:0";

    @Resource
    private IChatHistoryService chatHistoryService;

    @Resource
    private ChatHistoryMapper chatHistoryMapper;

    @Value("${chat.history.page.size:5}")
    private Integer chatHistoryPageSize;

    @Resource
    TemplateChatBuilder templateChatBuilder;

    // 全局线程池，所有会话共用，避免资源浪费
    private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(8);

    /**
     * 发起聊天流程
     *
     * @param streamChatRequest
     */
    public void startChat(StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity, Map<String, Object> context,
                          Map<String, Object> entryInput, ConversationProcessor processor, Boolean isSystem, String streamChatRequestJson) {
        String workflowCode = agentInfoEntity.getWorkflow();
        String workflowVersion = agentInfoEntity.getWorkflowVersion();
        /**
         * created事件处理
         */
        String chatId = processor.getChatId();
        String conversationId = streamChatRequest.getMessage().getConversationId();
        Subscription subscribe = redisStreamManager.subscribe(AgentosConstants.getRedisStreamKey(chatId), chatId, entries -> {
            String payload = entries.getValue().get("content");
            if (payload != null && !payload.equals(CHAT_STREAM_SIGN_START)) {
                StreamMessageDTO streamMessageDTO = JSON.parseObject(payload, StreamMessageDTO.class);
                ChatEventEnum event = ChatEventEnum.fromEvent(streamMessageDTO.getEvent());
                if (ChatEventEnum.MESSAGE_ERROR.equals(event)) {
                    /**
                     * completed事件处理
                     */
                    ChatDTO failedChatDTO = ChatDTOWrapper.converChatDTOWithStreamMessageDTO(ChatEventEnum.CHAT_FAILED,
                            streamMessageDTO);
                    StreamMessageDTO failedStreamMessageDTO = new StreamMessageDTO();
                    failedStreamMessageDTO.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());
                    if (failedChatDTO.getMetaData() == null) {
                        failedChatDTO.setMetaData(new java.util.HashMap<>());
                    }
                    failedChatDTO.getMetaData().put("workflowId", processor.getWorkflowRid());    
                    failedStreamMessageDTO.setData(JSON.toJSONString(failedChatDTO));
                    processor.sendMessage(failedStreamMessageDTO);

                    /**
                     * completed事件处理
                     */
                    ChatDTO doneChatDTO = ChatDTOWrapper.converChatDTOWithStreamMessageDTO(ChatEventEnum.DONE,
                            streamMessageDTO);
                    StreamMessageDTO doneStreamMessageDTO = new StreamMessageDTO();
                    doneStreamMessageDTO.setEvent(ChatEventEnum.DONE.getEvent());
                    doneStreamMessageDTO.setData(JSON.toJSONString(doneChatDTO));
                    processor.sendMessage(doneStreamMessageDTO);
                }
                processor.sendMessage(streamMessageDTO);
            }
        });
        SseEmitter sseEmitter = processor.getSseEmitter();
        // ====== 定时任务统一管理 ======
        ScheduledFuture<?> timeoutTask = SCHEDULER.schedule(() -> {
            try {
                // 发送超时事件
                StreamMessageDTO timeoutMsg = new StreamMessageDTO();
                timeoutMsg.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());
                ChatDTO failedChatDTO = new ChatDTO();
                failedChatDTO.setConversationId(processor.getConversationId());
                failedChatDTO.setChatId(processor.getChatId());
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setCode("408");
                errorInfo.setMessage("抱歉，由于系统异常，暂未获取到回复结果。");
                if (failedChatDTO.getMetaData() == null) {
                    failedChatDTO.setMetaData(new java.util.HashMap<>());
                }
                failedChatDTO.getMetaData().put("workflowId", processor.getWorkflowRid());
                failedChatDTO.setError(errorInfo);
                failedChatDTO.setStatus(ChatEventEnum.CHAT_FAILED.getEvent());
                timeoutMsg.setData(JSON.toJSONString(failedChatDTO));
                processor.sendMessage(timeoutMsg);
                // 发送done事件
                StreamMessageDTO doneMsg = new StreamMessageDTO();
                doneMsg.setEvent(ChatEventEnum.DONE.getEvent());
                ChatDTO doneChatDTO = new ChatDTO();
                doneChatDTO.setConversationId(processor.getConversationId());
                doneChatDTO.setChatId(processor.getChatId());
                doneMsg.setData(JSON.toJSONString(doneChatDTO));
                processor.sendMessage(doneMsg);
                sseEmitter.complete();
            } catch (Exception e) {
                log.warn("提前超时消息发送失败: {}", e.getMessage());
            }
        }, (processor.getTimeouts() - 1000), TimeUnit.MILLISECONDS); // 提前1秒
        ScheduledFuture<?> pingTask = SCHEDULER.scheduleAtFixedRate(() -> {
            try {
                sseEmitter.send(SseEmitter.event().name("ping").data("ping"));
            } catch (Exception e) {
                // 打印ping失败的关键参数，便于排查
                log.debug("Ping消息发送失败: chatId={}, agentId={}, conversationId={}, sseEmitter={}, 异常={}",
                    processor.getChatId(), processor.getAgentId(), processor.getConversationId(), sseEmitter, e.getMessage(), e);
            }
        }, 15, 15, TimeUnit.SECONDS); // 每15秒发一次
        // 连接关闭、异常、超时时都要取消定时任务，避免资源泄漏
        sseEmitter.onCompletion(() -> {
            log.info("SSE连接已关闭: chatId={}, agentId={}, conversationId={}", processor.getChatId(), processor.getAgentId(), processor.getConversationId());
            timeoutTask.cancel(false);
            pingTask.cancel(false);
            // 恢复原有的资源清理和历史保存逻辑
            redisStreamManager.unsubscribe(AgentosConstants.getRedisStreamKey(processor.getChatId()), subscribe);
            sendFailEvent(streamChatRequest, processor.getChatId(), "抱歉，由于系统异常，暂未获取到回复结果。", processor.getWorkflowRid());
            saveStreamHistory(processor, 2, null);
        });

        sseEmitter.onError((e) -> {
            log.info("SSE连接异常: chatId={}, agentId={}, conversationId={}, 异常={}", processor.getChatId(), processor.getAgentId(), processor.getConversationId(), e.getMessage());
            timeoutTask.cancel(false);
            pingTask.cancel(false);
            // 恢复原有的资源清理和历史保存逻辑
            redisStreamManager.unsubscribe(AgentosConstants.getRedisStreamKey(processor.getChatId()), subscribe);
            sendFailEvent(streamChatRequest, processor.getChatId(), "抱歉，由于系统异常，暂未获取到回复结果。", processor.getWorkflowRid());
            saveStreamHistory(processor, 1, "连接异常: " + e.getMessage());
        });

        sseEmitter.onTimeout(() -> {
            log.info("SSE连接超时: chatId={}, agentId={}, conversationId={}", processor.getChatId(), processor.getAgentId(), processor.getConversationId());
            timeoutTask.cancel(false);
            pingTask.cancel(false);
            // 恢复原有的资源清理和历史保存逻辑
            redisStreamManager.unsubscribe(AgentosConstants.getRedisStreamKey(processor.getChatId()), subscribe);
            // 不再尝试发送消息，只做清理和记录
            saveStreamHistory(processor, 1, "连接超时");
        });

        ChatDTO chatDTO = ChatDTOWrapper.converChatDTOWithStreamChatRequest(ChatEventEnum.CREATED, streamChatRequest);
        chatDTO.setChatId(chatId);
        StreamMessageDTO streamMessageDTO = new StreamMessageDTO();
        streamMessageDTO.setEvent(ChatEventEnum.CREATED.getEvent());
        streamMessageDTO.setData(JSON.toJSONString(chatDTO));
        eventHandler(streamMessageDTO);
        WorkflowRequest workflowRequest = new WorkflowRequest();
        workflowRequest.setContext(context);
        String workflowId = null;
        try {
            workflowRequest.setWorkflowUniqueCode(workflowCode);
            workflowRequest.setVersion(workflowVersion);
            boolean systemAgent = agentInfoEntity.getAgentType() != null && templateChatBuilder.isSupport(agentInfoEntity.getAgentType());
            //系统智能体｜授权智能体
            workflowRequest.setSystemSwitch(systemAgent || isSystem);
            String tenantId = DubboDataContext.getAuthIdHolder();
            String userCode = DubboDataContext.getUserHolder();
            ConversationInfo conversationInfo = new ConversationInfo();
            conversationInfo.setConversationId(conversationId);
            conversationInfo.setChatId(chatId);
            conversationInfo.setAgentId(streamChatRequest.getAgentId());

            // 如果传了历史记录, 则不从数据库中读取历史消息
            List<Message.History> history;
            if (streamChatRequest.getMessage() != null && streamChatRequest.getMessage().getHistory() != null && !streamChatRequest.getMessage().getHistory().isEmpty()) {
                history = streamChatRequest.getMessage().getHistory();
            } else {
                // 从数据库中读取历史消息
                Page<ChatHistoryEntity> page = new Page<>(1, chatHistoryPageSize);
                IPage<ChatHistoryEntity> pageResult = chatHistoryMapper.selectPage(page, new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ChatHistoryEntity>()
                        .eq(ChatHistoryEntity::getConversationId, conversationId)
                        .isNotNull(ChatHistoryEntity::getAnswer) // 只选择有答案的完整对话
                        .isNotNull(ChatHistoryEntity::getRawQuestion)
                        .eq(ChatHistoryEntity::getDeleted, 0)
                        .orderByDesc(ChatHistoryEntity::getUpdateTime));

                List<ChatHistoryEntity> historyEntities = pageResult.getRecords();

                history = new ArrayList<>();
                if (historyEntities != null && !historyEntities.isEmpty()) {
                    // 查询结果是倒序的，需要反转以保证时间顺序
                    Collections.reverse(historyEntities);

                    for (ChatHistoryEntity entity : historyEntities) {
                        // 先解析回答，确保有内容
                        Map<String, StringBuilder> sessionContents = new LinkedHashMap<>();
                        try {
                            List<JSONObject> answerEvents = JSON.parseArray(entity.getAnswer(), JSONObject.class);
                            if (answerEvents != null) {
                                for (JSONObject event : answerEvents) {
                                    JSONObject data = event.getJSONObject("data");
                                    if (data != null && "answer".equals(data.getString("type")) && data.getString("content") != null) {
                                        String sessionId = data.getString("session");
                                        if (sessionId != null) {
                                            sessionContents.computeIfAbsent(sessionId, k -> new StringBuilder()).append(data.getString("content"));
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing answer json from chat history for chatId={}: {}", entity.getChatId(), entity.getAnswer(), e);
                        }

                        boolean hasAnswerContent = false;
                        for (StringBuilder sb : sessionContents.values()) {
                            if (sb.length() > 0) {
                                hasAnswerContent = true;
                                break;
                            }
                        }

                        // 只有问题和回答都存在时，才加入历史记录
                        if (hasAnswerContent) {
                            // 用户问题
                            Message.History userMessage = new Message.History();
                            userMessage.setRole("user");
                            userMessage.setContent(entity.getRawQuestion());
                            history.add(userMessage);

                            // 机器人回答
                            for (StringBuilder assistantResponse : sessionContents.values()) {
                                if (assistantResponse.length() > 0) {
                                    Message.History assistantMessage = new Message.History();
                                    assistantMessage.setRole("assistant");
                                    assistantMessage.setContent(assistantResponse.toString());
                                    history.add(assistantMessage);
                                }
                            }
                        }
                    }
                }
            }

            conversationInfo.setHistory(history);
            conversationInfo.setMetaData(streamChatRequest.getMetaData());
            conversationInfo.setData(streamChatRequest.getMessage().getData());
            conversationInfo.setContent(streamChatRequest.getMessage().getContent());
            conversationInfo.setChannel(chatId);
            entryInput.put("message", conversationInfo);
            entryInput.put("meta_data", streamChatRequest.getMetaData());
            entryInput.put("extra_params", streamChatRequest.getExtraParams());
            workflowRequest.setExtraInput(entryInput);
            workflowRequest.setConductorCallbackUrl(callbackUrl + "/api/chat/v1/callback");
            if (streamChatRequest instanceof StreamChatRequestTest) {  //测试模式
                tenantId = ((StreamChatRequestTest) streamChatRequest).getTenantId();
                userCode = ((StreamChatRequestTest) streamChatRequest).getUserCode();
            }
            UserContext userInfo = new UserContext();
            UserVo userVo = new UserVo();
            userVo.setUserCode(userCode);
            userInfo.setUser(userVo);
            UserContext.TenantInfoDTO tenantInfoDTO = new UserContext.TenantInfoDTO();
            tenantInfoDTO.setTenantId(tenantId);
            userInfo.setTenantInfoDTO(tenantInfoDTO);
            //添加统一
            String json = URLEncoder.encode(JSON.toJSONString(userInfo), "utf-8");

            BaseResp<String> resp = apiBackService.workflowRequestGateWay(workflowEndpoint + "/provideWorkflow/commonRun",
                    json,
                    workflowRequest);
            // 保存会话历史记录（创建时）
            ChatHistoryEntity historyEntity = new ChatHistoryEntity();
            historyEntity.setChatId(processor.getChatId());
            historyEntity.setConversationId(conversationId);
            historyEntity.setAgentId(Long.parseLong(streamChatRequest.getAgentId()));
            historyEntity.setQuestion(streamChatRequestJson);
            historyEntity.setLiked(0);
            historyEntity.setDeleted(0);
            historyEntity.setStatus(1); // 进行中
            // 可补充 creatorId, userId, tenantId 等
            historyEntity.setCreatorId(DubboDataContext.getAuthIdHolder());
            historyEntity.setUserId(DubboDataContext.getUserHolder());
            historyEntity.setUpdateId(DubboDataContext.getAuthIdHolder());
            historyEntity.setUpdateTime(LocalDateTime.now());
            historyEntity.setCreateTime(LocalDateTime.now());
            historyEntity.setTenantId(DubboDataContext.getAuthIdHolder());
            historyEntity.setRawQuestion(streamChatRequest.getMessage() != null ? streamChatRequest.getMessage().getContent() : null);

            if (resp.isSuccess()) {
                workflowId = resp.getData();  //workflow运行id
                processor.setWorkflowRid(workflowId);
                historyEntity.setWorkflowId(workflowId);
                chatHistoryService.saveHistory(historyEntity);
            } else {
                historyEntity.setStatus(2); // 失败
                chatHistoryService.saveHistory(historyEntity);
                throw new BusinessException("workflow request error,resp " + resp + " ,request " + workflowRequest + " ,userInfo " + JSON.toJSONString(userInfo));
            }
            log.info("workflow request success,with request {}，resp {}", workflowRequest, resp);
        } catch (Exception e) {
            sendFailEvent(streamChatRequest, chatId, "workflowId: " + workflowId + ", error: " + e.getMessage(), workflowId);
            log.error("request workflow error,rquest {}", workflowRequest, e);
            return;
        }

        /**
         * process事件处理
         */
        ChatDTO processChatDTO = ChatDTOWrapper.converChatDTOWithStreamChatRequest(ChatEventEnum.PROCESS,
                streamChatRequest);
        processChatDTO.setChatId(chatId);
        StreamMessageDTO processStreamMessageDTO = new StreamMessageDTO();
        processStreamMessageDTO.setEvent(ChatEventEnum.PROCESS.getEvent());
        Map<String, Object> metaData = new HashMap<>();
        metaData.put("workflowId", workflowId);
        processChatDTO.setMetaData(metaData);
        processStreamMessageDTO.setData(JSON.toJSONString(processChatDTO));
        eventHandler(processStreamMessageDTO);
    }

    @Data
    static class ConversationInfo {
        // json 序列化之后要变成: conversation_id
        @JsonProperty("conversation_id")
        private String conversationId;
        private String chatId;
        private String agentId;
        private List<Message.History> history;
        private Map<String, Object> metaData;
        private String content;
        private List<String> data;
        private String channel;
    }

    private void sendFailEvent(StreamChatRequest streamChatRequest, String chatId, String errorMessage, String workflowId) {
        /**
         * failed事件处理
         */
        ChatDTO failedChatDTO = ChatDTOWrapper.converChatDTOWithStreamChatRequest(ChatEventEnum.CHAT_FAILED,
                streamChatRequest);
        failedChatDTO.setChatId(chatId);
        if (failedChatDTO.getMetaData() == null) {
            failedChatDTO.setMetaData(new java.util.HashMap<>());
        }
        failedChatDTO.getMetaData().put("workflowId", workflowId);
        ErrorInfo errorInfo = new ErrorInfo();
        errorInfo.setCode("500");
        errorInfo.setMessage(errorMessage);
        failedChatDTO.setError(errorInfo);
        StreamMessageDTO failedStreamMessageDTO = new StreamMessageDTO();
        failedStreamMessageDTO.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());
        failedStreamMessageDTO.setData(JSON.toJSONString(failedChatDTO));
        eventHandler(failedStreamMessageDTO);

        ChatDTO doneChatDTO = ChatDTOWrapper.converChatDTOWithStreamChatRequest(ChatEventEnum.DONE,
                streamChatRequest);
        doneChatDTO.setChatId(chatId);
        StreamMessageDTO doneStreamMessageDTO = new StreamMessageDTO();
        doneStreamMessageDTO.setEvent(ChatEventEnum.DONE.getEvent());
        doneStreamMessageDTO.setData(JSON.toJSONString(doneChatDTO));
        eventHandler(doneStreamMessageDTO);
    }

    /**
     * 流式消息处理
     *
     * @param streamMessageDTO
     */
    public void eventHandler(StreamMessageDTO streamMessageDTO) {
        String event = streamMessageDTO.getEvent();
        String data = streamMessageDTO.getData();
        ChatBaseDTO chatBaseDTO;
        if (ChatEventEnum.isMessageEvent(event)) {
            chatBaseDTO = JSON.parseObject(data, ChatDTO.class);
        } else if (ChatEventEnum.isChatEvent(event)) {
            chatBaseDTO = JSON.parseObject(data, ChatMessageDTO.class);
        } else {
            log.error("event {} is not supported", event);
            return;
        }
        String chatId = chatBaseDTO.getChatId();
        redisStreamManager.sendToStream(AgentosConstants.getRedisStreamKey(chatId), JSON.toJSONString(streamMessageDTO));
    }

    /**
     * 保存流式消息历史到会话历史表
     *
     * @param processor 会话处理器
     * @param status    状态
     * @param reason    原因
     */
    private void saveStreamHistory(ConversationProcessor processor, Integer status, String reason) {
        try {
            List<StreamMessageDTO> historyList = processor.getMessageHistory();
            if (status == 1 && reason != null) { // status 1 for failure
                StreamMessageDTO failMessage = new StreamMessageDTO();
                failMessage.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());

                ChatDTO failedChatDTO = new ChatDTO();
                failedChatDTO.setConversationId(processor.getConversationId());
                failedChatDTO.setChatId(processor.getChatId());
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setCode("500"); // Generic error code
                errorInfo.setMessage(reason);
                if (failedChatDTO.getMetaData() == null) {
                    failedChatDTO.setMetaData(new java.util.HashMap<>());
                }
                failedChatDTO.getMetaData().put("workflowId", processor.getWorkflowRid());            
                failedChatDTO.setError(errorInfo);
                failedChatDTO.setStatus(ChatEventEnum.CHAT_FAILED.getEvent());

                failMessage.setData(JSON.toJSONString(failedChatDTO));
                historyList.add(failMessage);
            }

            if (historyList == null || historyList.isEmpty()) {
                log.info("No stream history to save for chatId={}", processor.getChatId());
                return;
            }

            // 按顺序序列化为 JSON
            String answerJson = aggregateHistoryMessages(historyList);
            // 更新历史记录
            ChatHistoryEntity update = new ChatHistoryEntity();
            update.setChatId(processor.getChatId());
            update.setAnswer(answerJson);
            update.setStatus(status);
            chatHistoryService.saveHistory(update);
            log.info("Stream chat history saved for chatId={}", processor.getChatId());
        } catch (Exception e) {
            log.error("Failed to save stream chat history for chatId={}", processor.getChatId(), e);
        }
    }

    /**
     * 聚合流式消息，按类型聚合、去冗余、内容拼接，保证顺序，返回最终 JSON 字符串
     *
     * @param messageHistory 原始流式消息列表
     * @return 聚合后的 JSON 字符串
     */
    public static String aggregateHistoryMessages(List<StreamMessageDTO> messageHistory) {
        if (messageHistory == null || messageHistory.isEmpty())
            return "[]";
        List<Object> result = new ArrayList<>();
        Map<String, JSONObject> aggMap = new HashMap<>();
        for (StreamMessageDTO msg : messageHistory) {
            String event = msg.getEvent();
            JSONObject data = null;
            try {
                data = JSON.parseObject(msg.getData());
            } catch (Exception e) {
                log.error("Failed to parse message data for event={}", event, e);
            }
            if (ChatEventEnum.MESSAGE_DELTA.getEvent().equals(event) || ChatEventEnum.MESSAGE_COMPLETED.getEvent().equals(event)) {
                if (data == null)
                    continue;
                String key = data.getString("type") + "_" + data.getString("session");
                JSONObject agg = aggMap.get(key);
                if (agg == null) {
                    agg = new JSONObject();
                    // 新建data对象，存放除event外的所有字段
                    JSONObject aggData = new JSONObject();
                    aggData.put("type", data.getString("type"));
                    aggData.put("session", data.getString("session"));
                    aggData.put("createTime", data.getLong("createTime"));
                    aggData.put("content", "");
                    agg.put("data", aggData);
                    result.add(agg);
                    aggMap.put(key, agg);
                }
                agg.put("event", event);
                // 拼接内容，更新聚合对象
                JSONObject aggData = agg.getJSONObject("data");
                aggData.put("content", aggData.getString("content") + data.getString("content"));
                if (data.containsKey("endTime"))
                    aggData.put("endTime", data.getLong("endTime"));
                if (data.containsKey("tokens"))
                    aggData.put("tokens", data.getLong("tokens"));
                if (data.containsKey("costTime"))
                    aggData.put("costTime", data.getLong("costTime"));
                if (data.containsKey("isFinish"))
                    aggData.put("isFinish", data.getBoolean("isFinish"));
                if (ChatEventEnum.MESSAGE_COMPLETED.getEvent().equals(event)) {
                    aggMap.remove(key);
                }
            } else {
                // 其它事件直接顺序加入
                JSONObject simple = new JSONObject();
                simple.put("event", event);
                if (data != null && !data.isEmpty())
                    simple.put("data", data);
                result.add(simple);
            }
        }
        return JSON.toJSONString(result);
    }
}
