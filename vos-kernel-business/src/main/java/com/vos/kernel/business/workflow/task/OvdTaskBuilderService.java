package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.AttrDetRequest;
import com.linker.omos.client.domain.request.ModelItemDTO;
import com.linker.omos.client.domain.request.OvdItemRequest;
import com.linker.omos.client.domain.request.OvdModelRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.OvdModelMetaInfo;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class OvdTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Resource
    Validator validator;

    @Value("${ability.v3Threshold:0.7}")
    private Float v3Threshold;

    public OvdTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.V2_02.getCode());
    }

    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        OvdModelRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, OvdModelRequest.class);
        } catch (Exception e) {
            throw new BusinessException("ovd调用参数解析失败");
        }
        abilityRequest.setModel(taskAddRequest.getModel());
        Set<ConstraintViolation<OvdModelRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        String inputType = abilityRequest.getInputType();
        if (!"base64".equals(inputType) && !"image_url".equals(inputType)) {
            throw new BusinessException("输入类型只能是base64|image_url的一种");
        }
        if (abilityRequest.getTasks().size() != 1) {
            throw new BusinessException("ovd调用详细参数解析失败");
        }
        //校验参数
        paramsCheck(abilityRequest.getTasks());

        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow {},下发{}任务,conductorTaskId:{},参数：{}", taskAddRequest.getWorkflowInstanceId(), taskAddRequest.getTaskName(), taskAddRequest.getConductorTaskId(), params);
        }
        //多任务合并
        OvdItemRequest ovdItemRequest = buildOvd(abilityRequest.getTasks());

        TaskTypeAndAbility taskAbility = getTaskAbility(taskAddRequest.getTaskName(), workflowTaskTypeEnum);

        //构造下发数据
        String callbackUrl = taskAddRequest.getCallbackUrl();
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.OVD.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        //构造基础任务信息
        taskAdd.setSubList(buildTaskInfo(abilityRequest, ovdItemRequest, taskAbility, taskAdd.getAppSourceId()));

        //缓存元数据
        String image = BooleanUtil.isTrue(refluxProperties.getOpenV3Reflux()) ? abilityRequest.getInput() : "";
        OvdModelMetaInfo ovdModelMetaInfo = new OvdModelMetaInfo(taskAbility.getAbilityName(), image, abilityRequest.getTasks());
        ovdModelMetaInfo.setModel(taskAbility.getActionId());
        ovdModelMetaInfo.setCallBackUrl(callbackUrl);
        ovdModelMetaInfo.setOrgCode(DubboDataContext.getAuthIdHolder());
        ovdModelMetaInfo.setEventTime(new Date().getTime());
        ovdModelMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        ovdModelMetaInfo.setWorkflow(true);
        ovdModelMetaInfo.setWorkflowInstanceId(taskAddRequest.getWorkflowInstanceId());
        taskAdd.setMetaInfo(JSON.toJSONString(ovdModelMetaInfo));

        return taskAdd;
    }


    /**
     * 多任务合并
     *
     * @param tasks
     * @return
     */
    private OvdItemRequest buildOvd(List<OvdItemRequest> tasks) {

        OvdItemRequest ovdItemRequest = new OvdItemRequest();
        for (OvdItemRequest o : tasks) {
            if (StrUtil.isNotBlank(o.getConf())) {
                ovdItemRequest.setConf(o.getConf());
            }
        }
        ovdItemRequest.setLabel(tasks.stream().map(OvdItemRequest::getLabel).collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(ovdItemRequest.getConf())) {
            ovdItemRequest.setConf(v3Threshold.toString());
        }
        return ovdItemRequest;
    }

    /**
     * 构造比对算法参数
     *
     * @param
     * @param ability
     * @return
     */
    private List<TaskSubDTO> buildTaskInfo(OvdModelRequest abilityRequest, OvdItemRequest ovdItemRequest, TaskTypeAndAbility ability, String appSourceId) {

        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(abilityRequest.getTaskOrder(), abilityRequest.getVideoClusterSource(), ability);
        //base64图片处理
        String s = handleImage(abilityRequest.getInputType(), abilityRequest.getInput(), appSourceId);
        abilityRequest.setInput(s);
        taskSubDTO.setRequestJson(buildRequestStr(abilityRequest, ovdItemRequest));
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }


    /**
     * 构造算法请求参数
     *
     * @param abilityRequest
     * @return
     */
    private String buildRequestStr(OvdModelRequest abilityRequest, OvdItemRequest ovdItemRequest) {

        String eventTime = DateUtil.getSimpleYMDHMS(LocalDateTime.now());
        String taskId = String.valueOf(IdWorker.nextId());
        AiExecuteImageParamDTO aiExecuteImageParamDTO = new AiExecuteImageParamDTO();
        aiExecuteImageParamDTO.setImageId("0");
        aiExecuteImageParamDTO.setVideoId("ovd");
        aiExecuteImageParamDTO.setSrcType(EmbeddingInputEnum.matchApiType(abilityRequest.getInputType()));
        aiExecuteImageParamDTO.setData(abilityRequest.getInput());
        aiExecuteImageParamDTO.setEventTime(eventTime);
        aiExecuteImageParamDTO.setTaskId(taskId);
        aiExecuteImageParamDTO.setOrgId(DubboDataContext.getAuthIdHolder());
        aiExecuteImageParamDTO.setVideoValidation(String.valueOf(IdWorker.nextId()));

        //高级配置参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("displayAllBboxes", false);
        jsonObject.put("boxEnlargeSize", 1);
        JSONObject codeJsonObject = new JSONObject();
        codeJsonObject.put("includeClasses", ListUtil.empty());
        codeJsonObject.put("type", "");
        jsonObject.put("coldActivation", codeJsonObject);

        //OVD参数
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("threshold", Double.parseDouble(ovdItemRequest.getConf()));
        jsonObject1.put("labels", StrUtil.splitToArray(ovdItemRequest.getLabel(), ","));
        jsonObject.put("ommodelv3", jsonObject1);
        aiExecuteImageParamDTO.setKwargs(jsonObject);
        return buildModelRequestStr(ListUtil.of(aiExecuteImageParamDTO), taskId);
    }

}
