package com.vos.kernel.business.api;

import com.alibaba.fastjson.JSONObject;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.log.LogStrategy;
import com.github.lianjiatech.retrofit.spring.boot.log.Logging;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.omos.client.domain.response.*;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.core.api.dto.ApiBackDTO;
import com.vos.kernel.core.api.dto.ApiOutReturnDTO;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * api输出接口定义
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/30
 * @description: com.vos.kernel.core.service.api
 */
@RetrofitClient(baseUrl = "http://apiBack", sourceOkHttpClient = "asyncBackOkHttpClient")
@Logging(logStrategy = LogStrategy.BASIC)
public interface IV3ApiBackService {

    /**
     * 请求api输出接口
     *
     * @param url
     * @param apiBack
     * @return
     */
    @POST
    ApiOutReturnDTO apiBackRequest(@Url String url, @Body V3MultiTaskCallBackDTO apiBack);


    /**
     * v35回调
     *
     * @param url
     * @param apiBackDTO
     * @return
     */
    @POST
    ApiOutReturnDTO v35ApiBackRequest(@Url String url, @Body ApiBackDTO apiBackDTO);

    /**
     * 向量化异步返回
     *
     * @param url
     * @param embeddingResponse
     * @return
     */
    @POST
    ApiOutReturnDTO embeddingApiBackRequest(@Url String url, @Body EmbeddingResponse embeddingResponse);

    /**
     * ocr异步返回
     *
     * @param url
     * @param ocrResponse
     * @return
     */
    @POST
    ApiOutReturnDTO ocrApiBackRequest(@Url String url, @Body OcrResponse ocrResponse);


    /**
     * comparsion异步返回
     *
     * @param url
     * @param comparisonResponse
     * @return
     */
    @POST
    ApiOutReturnDTO comparsionApiBackRequest(@Url String url, @Body ComparisonResponse comparisonResponse);

    /**
     * comparsion异步返回
     *
     * @param url
     * @param odResponse
     * @return
     */
    @POST
    ApiOutReturnDTO odApiBackRequest(@Url String url, @Body OdResponse odResponse);

    /**
     * comparsion异步返回
     *
     * @param url
     * @param ovdResponse
     * @return
     */
    @POST
    ApiOutReturnDTO ovdApiBackRequest(@Url String url, @Body OvdResponse ovdResponse);

    /**
     * comparsion异步返回
     *
     * @param url
     * @param odResponse
     * @return
     */
    @POST
    ApiOutReturnDTO attrDetApiBackRequest(@Url String url, @Body AttrDetResponse odResponse);

    /**
     * ocr异步返回
     *
     * @param url
     * @param ocrResponse
     * @return
     */
    @POST
    ApiOutReturnDTO rankApiBackRequest(@Url String url, @Body ReRankResponse ocrResponse);

    /**
     * 请求workflow接口
     *
     * @param url
     * @param tenantId
     * @param userCode
     * @param workflowRequest
     * @return
     */
    @POST
    BaseResp<String> workflowRequest(@Url String url, @Header("tenantId") String tenantId,
                                     @Header("userCode") String userCode, @Body WorkflowRequest workflowRequest);


    @POST
    BaseResp<String> workflowRequestGateWay(@Url String url, @Header("x-linker-rpc-userinfo") String tenantId, @Body WorkflowRequest workflowRequest);


    /**
     * 同步平台模型
     *
     * @param url
     * @param userInfo
     * @param modelOperationRequest
     * @return
     */
    @POST
    BaseResp<Boolean> syncPlatformModel(@Url String url, @Header("x-linker-rpc-userinfo") String userInfo, @Body ModelOperationRequest modelOperationRequest);


    /**
     * 删除编排工作流
     *
     * @param url
     * @param userInfo
     * @param workflowDeleteRequest
     * @return
     */
    @POST
    BaseResp<Boolean> deleteWorkflow(@Url String url, @Header("x-linker-rpc-userinfo") String userInfo, @Body WorkflowDeleteRequest workflowDeleteRequest);


    /**
     * 删除构建智能体
     *
     * @param url
     * @param userInfo
     * @param agentDeleteRequest
     * @return
     */
    @POST
    BaseResp<Boolean> deleteAgent(@Url String url, @Header("x-linker-rpc-userinfo") String userInfo, @Body AgentDeleteRequest agentDeleteRequest);
    /**
     * ocr异步返回
     *
     * @param url
     * @param jsonObject
     * @return
     */
    @POST
    ApiOutReturnDTO throughApiBackRequest(@Url String url, @Body String jsonObject);
}
