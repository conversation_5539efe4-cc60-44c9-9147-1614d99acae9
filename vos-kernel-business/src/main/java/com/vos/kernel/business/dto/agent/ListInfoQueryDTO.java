package com.vos.kernel.business.dto.agent;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import lombok.Data;

@Data
public class ListInfoQueryDTO {

    /**
     * 查询ID
     */
    private String id;

    /**
     * 查询名称，支持模糊查询
     */
    private String name;

    /**
     * 查询描述，支持模糊查询
     */
    private String description;

    /**
     * 全文搜索，支持在名称和描述中模糊查询
     */
    private String all;

    /**
     * 查询状态
     * 0-停止
     * 1-运行中
     */
    private Integer running;

    public void setStatus(Integer status) {
        this.running = status;
    }

    /**
     * 当前页码，从1开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer size = 10;

    /**
     * 租户ID
     */
    private String tenantId;

    private String tag;
}