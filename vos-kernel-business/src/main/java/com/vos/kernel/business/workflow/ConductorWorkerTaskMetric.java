package com.vos.kernel.business.workflow;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年06月03日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class ConductorWorkerTaskMetric implements Serializable {
    /**
     * 唯一标识
     */
    private String queueKey;

    /**
     * 执行成功耗时
     */
    private Long rt;

    /**
     * 执行时间
     */
    private LocalDateTime callTime;
}
