package com.vos.kernel.business.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linker.core.base.baseclass.BaseDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/9 14:13
 * @DESC: 会话创建响应
 */
@Data
public class CreateConversationResponse extends BaseDTO {

    /**
     * 会话ID
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 会话创建时间时间戳
     */
    @JsonProperty("create_time")
    private Long createTime;

    /**
     * 欢迎语
     */
    private String welcome;

    /**
     * 预置问题
     */
    @JsonProperty("preset_questions")
    private List<String> presetQuestions = new ArrayList<>();

    /**
     * 智能体名称
     */
    @JsonProperty("agent_name")
    private String agentName;

    /**
     * 智能体图标
     */
    private String icon;

    /**
     * 智能体描述
     */
    private String description;
}
