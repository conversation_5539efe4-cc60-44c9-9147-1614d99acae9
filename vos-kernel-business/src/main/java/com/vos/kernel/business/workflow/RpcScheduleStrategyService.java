package com.vos.kernel.business.workflow;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.core.utils.cnsign.SM4Util;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;
import com.vos.kernel.business.api.WorkflowApiService;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.config.WorkflowTaskPollException;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.workflow.*;
import com.vos.task.manage.api.rpc.IAiAbilityCallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月26日
 * @version: 1.0
 * @description: 非os管控编排任务下发
 */
@Slf4j
@Service
public class RpcScheduleStrategyService implements ScheduleStrategyService {

    @Resource
    TaskBuilderHolder taskBuilderHolder;

    @DubboReference
    IAiAbilityCallService aiAbilityCallService;

    @Resource
    WorkflowApiService workflowApiService;

    @Resource
    WorkflowProperties workflowProperties;

    public static final List<String> IGNORE = Collections.singletonList("process_output");

    /**
     * 算法类型
     */
    protected List<String> type = WorkflowTaskTypeEnum.getOsUnControlCodes();

    @PostConstruct
    private void init() {
        for (String code : type) {
            taskBuilderHolder.registeStrategyHandler(code, this);
        }
    }


    @Override
    public TaskAddResponse addTask(TaskAddRequest taskAddRequest) {
        if (StrUtil.isBlank(taskAddRequest.getCallbackUrl())) {
            throw new WorkflowTaskException(TaskRespCodeEnum.BAD_REQUEST, TaskRespCodeEnum.BAD_REQUEST.getMessage() + ":callbackUrl");
        }
        if (log.isTraceEnabled()) {
            String queueKey = StrUtil.isBlank(taskAddRequest.getDomain()) ? taskAddRequest.getTaskName() : taskAddRequest.getTaskName() + "_" + taskAddRequest.getDomain();
            log.trace("workflow自定义类型「{}」下发任务，domain:{},参数：{}", queueKey, taskAddRequest.getDomain(), taskAddRequest.getParameters().length() < 218 ? taskAddRequest.getParameters()
                    : StrUtil.subWithLength(taskAddRequest.getParameters(), 0, 218));
        }
        String appSourceId = aiAbilityCallService.addWorkflowWorkerTask(taskAddRequest);
        TaskAddResponse taskAddResponse = new TaskAddResponse();
        taskAddResponse.setSourceId(appSourceId);
        return taskAddResponse;
    }

    /**
     * 批量拉取任务
     *
     * @param taskPollRequest
     * @return
     */
    public List<TaskMessage> batchPoll(TaskPollRequest taskPollRequest) {

        StopWatch stopWatch = new StopWatch();
        if (BooleanUtil.isTrue(taskPollRequest.getIsProd())) {
            String appKeyHolder = DubboDataContext.getAppKeyHolder();
            List<String> ignore = workflowProperties.getIgnore();
            boolean contains = CollectionUtil.contains(ignore, taskPollRequest.getTaskDefName());
            String trim = StrUtil.trim(workflowProperties.getDomain());
            taskPollRequest.setDomain(BooleanUtil.isTrue(taskPollRequest.getIsPublic()) || contains ? trim : trim + "_" + appKeyHolder);
        } else {
            String domain = taskPollRequest.getDomain();
            if (StrUtil.isBlank(domain)) {
                throw new WorkflowTaskPollException(TaskRespCodeEnum.BAD_REQUEST, "自定义节点开发模式下需要设置TOKEN环境变量");
            }
            //增加开发模式
            if (domain.startsWith("dev")) {
                taskPollRequest.setDomain(domain);
            } else {
                String originalData = SM4Util.decryptDataECB(domain);
                if (StrUtil.isBlank(originalData)) {
                    throw new WorkflowTaskPollException(TaskRespCodeEnum.BAD_REQUEST, "自定义节点开发模式下TOKEN环境变量解密失败");
                }
                if (!originalData.startsWith("ide")) {
                    throw new WorkflowTaskPollException(TaskRespCodeEnum.BAD_REQUEST, "自定义节点开发模式下TOKEN环境变量格式错误");
                }
                taskPollRequest.setDomain(originalData);
            }
        }
        if (log.isTraceEnabled()) {
            log.trace("taskPollRequest:{}", JSON.toJSONString(taskPollRequest));
        }
        stopWatch.start("poll");
        List<TaskMessage> taskMessages = aiAbilityCallService.batchPoll(taskPollRequest);
        stopWatch.stop();
        if (CollectionUtil.isNotEmpty(taskMessages)) {
            stopWatch.start("conduct");
            //更改任务状态
            List<WorkflowCallBackContent> workflowCallBackContents = new ArrayList<>();
            String callBackUrl = "";
            for (TaskMessage taskMessage : taskMessages) {
                if (StrUtil.isNotBlank(taskMessage.getCallbackUrl())) {
                    callBackUrl = taskMessage.getCallbackUrl();
                }
                WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
                if (StrUtil.isNotBlank(taskMessage.getBizMeta()) && taskMessage.getBizMeta().contains("{")) {
                    workflowCallBackContent.setTransmissionParams(JSONObject.parseObject(taskMessage.getBizMeta()));
                }
                WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
                workflowCallBackContentItem.setSourceId(taskMessage.getId());
                workflowCallBackContentItem.setWorkerId(taskPollRequest.getWorkerId());
                workflowCallBackContentItem.setStatus(WorkflowStatusEnum.IN_PROGRESS);
                workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
                workflowCallBackContents.add(workflowCallBackContent);
            }
            callBackUrl = StrUtil.isNotBlank(callBackUrl) ? callBackUrl : workflowProperties.getEndpoint();
            workflowApiService.batchRequestWorkflowEngine(callBackUrl, workflowCallBackContents);
            stopWatch.stop();
        }
        if (log.isDebugEnabled() && stopWatch.getTotalTimeMillis() > 500) {
            log.debug("workflow自定义类型「{}」拉取任务，耗时：{}", taskPollRequest.getDomain(), stopWatch.prettyPrint());
        }
        return taskMessages;
    }


    /**
     * 更新任务状态
     *
     * @param taskUpdateRequest
     * @return
     */
    public Boolean updateTaskStatus(TaskUpdateRequest taskUpdateRequest) {
        WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
        if (StrUtil.isNotBlank(taskUpdateRequest.getBizMeta()) && taskUpdateRequest.getBizMeta().contains("{")) {
            workflowCallBackContent.setTransmissionParams(JSONObject.parseObject(taskUpdateRequest.getBizMeta()));
        }
        WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
        workflowCallBackContentItem.setSourceId(taskUpdateRequest.getTaskId());
        workflowCallBackContentItem.setStatus(taskUpdateRequest.getStatus());
        workflowCallBackContentItem.setReasonForIncompletion(taskUpdateRequest.getReasonForIncompletion());
        workflowCallBackContentItem.setWorkerId(taskUpdateRequest.getWorkerId());
        workflowCallBackContentItem.setOutputData(taskUpdateRequest.getOutputData());
        workflowCallBackContentItem.setLogs(taskUpdateRequest.getLogs());

        workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
        workflowApiService.batchRequestWorkflowEngine(StrUtil.isNotBlank(taskUpdateRequest.getCallbackUrl()) ? taskUpdateRequest.getCallbackUrl() : workflowProperties.getEndpoint(), ListUtil.of(workflowCallBackContent));
        return aiAbilityCallService.updateTaskStatus(taskUpdateRequest);
    }
}
