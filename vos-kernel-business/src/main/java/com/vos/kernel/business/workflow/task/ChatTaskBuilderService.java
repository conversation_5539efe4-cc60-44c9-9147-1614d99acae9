package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.CommonConstant;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.chat.AgentChatMeta;
import com.linker.omos.client.domain.chat.ChatMessage;
import com.linker.omos.client.domain.chat.ChatWorkflowRequest;
import com.linker.omos.client.domain.chat.OpenAiRequestDTO;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.ChatModelMetaInfo;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ChatTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Value("${task.expired:1}")
    private Integer expired;

    public ChatTaskBuilderService() {
        this.type = Arrays.asList(WorkflowTaskTypeEnum.LLM.getCode(), WorkflowTaskTypeEnum.V3_CHAT.getCode());
    }

    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        OpenAiRequestDTO openAiRequest;
        if (StrUtil.isNotBlank(taskAddRequest.getModel())) {
            String parameters = taskAddRequest.getParameters();
            try {
                openAiRequest = JSON.parseObject(parameters, OpenAiRequestDTO.class);
            } catch (Exception e) {
                throw new BusinessException("open ai 参数解析失败");
            }
            openAiRequest.setModel(taskAddRequest.getModel());
        } else {
            try {
                ChatWorkflowRequest chatWorkflowRequestDTO = JSON.parseObject(taskAddRequest.getParameters(), ChatWorkflowRequest.class);
                openAiRequest = chatWorkflowRequestDTO.getParameter();
                openAiRequest.setModel(chatWorkflowRequestDTO.getModel());
                AgentChatMeta agentChatMeta = new AgentChatMeta();
                agentChatMeta.setConversationId(chatWorkflowRequestDTO.getChannel());
                openAiRequest.setConversationInfo(agentChatMeta);
                if (BooleanUtil.isTrue(chatWorkflowRequestDTO.getIsFunctionCall())) {
                    // 函数调用,非流式调用
                    openAiRequest.setStream(false);
                }
            } catch (Exception e) {
                throw new BusinessException("open ai 参数解析失败");
            }
        }

        TaskTypeAndAbility taskAbility = getTaskAbility(openAiRequest.getModel(), workflowTaskTypeEnum);
        if (StrUtil.isBlank(taskAbility.getChatModelName())) {
            throw new BusinessException("open chat model 为空，联系管理员");
        }
        openAiRequest.setModel(taskAbility.getChatModelName());
        openAiRequest.setAppSourceId(String.valueOf(IdWorker.nextId()));
        //校验格式
        checkChatMessages(openAiRequest.getMessages());
        chatBase64Handle(openAiRequest);
        AgentChatMeta conversationInfo = openAiRequest.getConversationInfo();
//        if (BooleanUtil.isTrue(openAiRequest.getStream()) && (conversationInfo == null || StrUtil.isBlank(conversationInfo.getConversationId()))) {
//            throw new BusinessException("流式输出，agent会话元数据&conversationId 不能为空");
//        }
        //组装数据
        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(openAiRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        TaskAddDTO taskAdd = new TaskAddDTO();
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(BooleanUtil.isTrue(openAiRequest.getStream()) ? RpcCallBackInfoEnum.CHAT_STREAM.getKey() : RpcCallBackInfoEnum.CHAT.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(openAiRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        taskAdd.setSubList(buildTaskInfo(openAiRequest, taskAbility, taskAbility.getChatApiKey()));
        //缓存元数据
        ChatModelMetaInfo metaInfo = new ChatModelMetaInfo(conversationInfo == null ? "" : conversationInfo.getChatId(), conversationInfo == null ? "" : conversationInfo.getConversationId(), conversationInfo == null ? "" : conversationInfo.getAgentId());
        metaInfo.setCallBackUrl(taskAddRequest.getCallbackUrl());
        metaInfo.setOrgCode(DubboDataContext.getAuthIdHolder());
        metaInfo.setModel(taskAbility.getActionId());
        metaInfo.setEventTime(new Date().getTime());
        metaInfo.setBizMeta(taskAddRequest.getBizMeta());
        metaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(metaInfo));
        return taskAdd;
    }


    /**
     * 构造下发任务
     *
     * @param aiRequest
     * @return
     */
    public List<TaskSubDTO> buildTaskInfo(OpenAiRequestDTO aiRequest, TaskTypeAndAbility abilityEntity, String apiKey) {
        List<TaskSubDTO> tasks = new ArrayList<>();
        String subSnowId = String.valueOf(IdWorker.nextId());
        String businessId = String.valueOf(IdWorker.nextId());
        TaskSubDTO aiJobDetail = new TaskSubDTO();
        aiJobDetail.setBusinessId(businessId);
        aiJobDetail.setTaskOrder(aiRequest.getTaskOrder() == null ? 0 : aiRequest.getTaskOrder());
        aiJobDetail.setIsFirst(1);
        aiJobDetail.setIsLast(1);
        aiJobDetail.setIsJump(0);
        aiJobDetail.setVideoId(DubboDataContext.getAuthIdHolder());
        aiJobDetail.setSubSnowId(subSnowId);
        //构造额外信息
        aiJobDetail.setAbilityId(abilityEntity.getOperatorId());
        aiJobDetail.setOmAppId(abilityEntity.getAbility());
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("taskOrder");
        filter.getExcludes().add("appSourceId");
        filter.getExcludes().add("callbackUrl");
        filter.getExcludes().add("conversationInfo");
        filter.getExcludes().add("videoClusterSource");
        aiJobDetail.setRequestJson(JSON.toJSONString(aiRequest, filter));
        if (StrUtil.isNotBlank(apiKey)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("apiKey", apiKey);
            aiJobDetail.setExtend(jsonObject);
        }
        tasks.add(aiJobDetail);
        return tasks;
    }


    /**
     * 验证交互数据
     *
     * @param messages
     */
    public void checkChatMessages(List<ChatMessage> messages) {
        if (CollectionUtil.isNotEmpty(messages) && messages.size() > 1) {
            List<ChatMessage> collect = messages.stream().filter(t -> "user".equals(t.getRole()) || "assistant".equals(t.getRole())).collect(Collectors.toList());
            int i = 1;
            for (ChatMessage c : collect) {
                String role = c.getRole();
                if (i % 2 == 0) {
                    if (!"assistant".equals(role)) {
                        throw new BusinessException("messages 参数有误");
                    }
                } else {
                    if (!"user".equals(role)) {
                        throw new BusinessException("messages 参数有误");
                    }
                }
                i++;
            }
        }
    }

    /**
     * chat base64处理
     *
     * @param aiRequest
     */
    public void chatBase64Handle(OpenAiRequestDTO aiRequest) {
        ChatMessage chatMessage = CollectionUtil.getLast(aiRequest.getMessages());
        Object content = chatMessage.getContent();
        if (content instanceof ArrayList || content instanceof JSONArray) {
            JSONArray objects = JSONArray.parseArray(JSON.toJSONString(content));
            for (int i = 0; i < objects.size(); i++) {
                JSONObject jsonObject = objects.getJSONObject(i);
                String type = jsonObject.getString("type");
                if ("image_url".equals(type)) {
                    try {
                        String string = jsonObject.getJSONObject("image_url").getString("url");
                        if (!string.contains(CommonConstant.HTTP)) {
                            String imageCacheKey = aiRequest.getAppSourceId() + "chatBase64";
                            stringRedisTemplate.opsForValue().set(imageCacheKey, string, Duration.ofMinutes(expired));
                            jsonObject.getJSONObject("image_url").put("url", imageCacheKey);
                        }
                    } catch (Exception e) {
                    }
                }
            }
            chatMessage.setContent(objects);
        }
    }

}
