package com.vos.kernel.business.dto.agent;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import org.springframework.beans.BeanUtils;

@Data
public class CamelCaseWorkflowInfoDTO implements Serializable {
    private String workflowId;
    private String tenantId;
    private String iconPath;
    private String workflowName;
    private String workflowVersion;
    private String description;
    
    /**
     * agent排序字段，值越小，排序越靠前
     */
    private Integer sort;
    
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime lastStartTime;
    
    /**
     * 0:停止，1:运行
     */
    private Integer running;
    
    private String tag;
    
    /**
     * 节点数量
     */
    private Integer nodeNum;

    // 转换方法
    public static CamelCaseWorkflowInfoDTO from(WorkflowInfoDTO source) {
        CamelCaseWorkflowInfoDTO target = new CamelCaseWorkflowInfoDTO();
        BeanUtils.copyProperties(source, target);
        return target;
    }
} 