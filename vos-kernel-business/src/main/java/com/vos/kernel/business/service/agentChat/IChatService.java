package com.vos.kernel.business.service.agentChat;

import com.vos.kernel.business.dto.StreamChatRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @Date 2024/12/10 14:37
 * @DESC: 聊天接口
 */
public interface IChatService {

    /**
     * 流式接口
     *
     * @param streamChatRequest
     * @return
     */
    SseEmitter stream(StreamChatRequest streamChatRequest);

    /**
     * 补充问题接口
     *
     * @param streamChatRequest
     * @return
     */
    void askComplete(StreamChatRequest streamChatRequest);
}
