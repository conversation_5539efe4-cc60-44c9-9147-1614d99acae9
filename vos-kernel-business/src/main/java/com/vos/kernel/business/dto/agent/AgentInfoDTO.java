package com.vos.kernel.business.dto.agent;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AgentInfoDTO implements Serializable {
    @JsonProperty("agent_id")
    private String agentId;

    @JsonProperty("tenant_id")
    private String tenantId;

    /**
     * 智能体模式,1:基于工作流的智能体，2:交互式智能体
     */
    @JsonProperty("agent_type")
    private Integer agentType;

    @JsonProperty("icon_path")
    private String iconPath;

    @JsonProperty("agent_name")
    private String agentName;

    @JsonProperty("agent_version")
    private String agentVersion;

    private String description;

    /**
     * agent排序字段，值越小，排序越靠前
     */
    private Integer sort;

    @JsonProperty("create_time")
    private LocalDateTime createTime;

    @JsonProperty("update_time")
    private LocalDateTime updateTime;

    @JsonProperty("last_start_time")
    private LocalDateTime lastStartTime;

    /**
     * 0:停止，1:运行
     */
    private Integer running;
}
