package com.vos.kernel.business.service.rank;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.linker.omos.client.domain.response.ReRankAbilityResponse;
import com.linker.omos.client.domain.response.ReRankItemResponse;
import com.linker.omos.client.domain.response.ReRankResponse;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.linker.omos.client.domain.workflow.callback.TaskCallExceptionDTO;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;
import com.vos.kernel.business.config.DisruptorProperties;
import com.vos.kernel.business.disruptor.EventListener;
import com.vos.kernel.business.disruptor.ParallelQueueHandler;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.business.dto.MqConstants;
import com.vos.kernel.business.dto.RankAsyncBack;
import com.vos.kernel.business.service.SyncResult;
import com.vos.kernel.business.service.common.SyncWaitResultService;
import com.vos.kernel.common.constant.RedisKey;
import com.vos.kernel.common.dubbo.IReRankBusinessCallBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.support.MessageBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年10月28日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@DubboService
@Resource
public class ReRankBusinessCallBackServiceImpl implements IReRankBusinessCallBackService {

    @Resource
    SyncWaitResultService syncWaitResultService;

    @Resource
    IReRankService reRankService;

    @Resource
    RocketMQTemplate rocketMqTemplate;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "reRank-queue-";

    @Resource
    DisruptorProperties disruptorProperties;

    /**
     * disruptor处理器
     */
    private ParallelQueueHandler<TaskCallBackDto> parallelQueueHandler;

    /**
     * 构造器
     */
    public ReRankBusinessCallBackServiceImpl() {
        ParallelQueueHandler.Builder<TaskCallBackDto> builder = new ParallelQueueHandler.Builder<TaskCallBackDto>()
                .setThreads(Runtime.getRuntime().availableProcessors())
                .setProducerType(ProducerType.MULTI)
                .setNamePrefix(THREAD_NAME_PREFIX)
                .setWaitStrategy(new BlockingWaitStrategy());

        BatchEventListenerProcessor batchEventListenerProcessor = new BatchEventListenerProcessor();
        builder.setListener(batchEventListenerProcessor);
        this.parallelQueueHandler = builder.build();
    }

    @PostConstruct
    public void init() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.start();
        }
    }

    @PreDestroy
    public void destroy() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.shutDown();
        }
    }


    /**
     * 批量事件监听
     */
    public class BatchEventListenerProcessor implements EventListener<TaskCallBackDto> {

        @Override
        public void onEvent(TaskCallBackDto event) {
            if (null != event) {
                reRankCallBackDo(event);
            }
        }

        @Override
        public void onException(Throwable ex, long sequence, TaskCallBackDto event) {
            log.error("reRank 请求写回失败，request:{},errMsg:{} ", event, ex.getMessage(), ex);
            reRankCallBackDo(event);
        }
    }

    @Override
    public void reRankCallBack(TaskCallBackDto taskCallBackDto) {
        if (BooleanUtil.isTrue(disruptorProperties.getOpenOcr())) {
            if (this.parallelQueueHandler != null) {
                this.parallelQueueHandler.add(taskCallBackDto);
            } else {
                log.error("reRank回调异常，parallelQueueHandler未初始化");
            }
        } else {
            reRankCallBackDo(taskCallBackDto);
        }
    }


    /**
     * reRank回调
     *
     * @param taskCallBackDto
     */
    public void reRankCallBackDo(TaskCallBackDto taskCallBackDto) {
        String appSourceId = taskCallBackDto.getAppSourceId();
        TaskCallExceptionDTO exception = taskCallBackDto.getException();
        log.info("appSourceId：{},reRank接收回调,是否异常：{}", appSourceId, exception);
        //是否为异步请求
        ModelMetaInfo metaInfo = JSON.parseObject(taskCallBackDto.getMetaInfo(), ModelMetaInfo.class);
        if (metaInfo == null) {
            log.warn("reRank回调超时！appSourceId：{}", appSourceId);
            return;
        }

        ReRankResponse reRankResponse = new ReRankResponse();
        reRankResponse.setModel(metaInfo.getModel());
        reRankResponse.setTook(new Date().getTime() - metaInfo.getEventTime());
        reRankResponse.setSourceId(appSourceId);
        //是否异常
        if (exception != null) {
            reRankResponse.setError(exception.getMessage());
            reRankResponse.setCode(Integer.valueOf(exception.getCode()));
        } else {
            reRankResponse.setCode(200);
            ReRankAbilityResponse response = JSONArray.parseObject(JSON.toJSONString(taskCallBackDto.getRequestParseResult()), ReRankAbilityResponse.class);
            List<BigDecimal> features = response.getFeatures();
            List<ReRankItemResponse> data = new ArrayList<>();
            int i = 0;
            for (BigDecimal feature : features) {
                ReRankItemResponse reRankItemResponse = new ReRankItemResponse();
                reRankItemResponse.setIndex(i);
                reRankItemResponse.setScore(feature);
                data.add(reRankItemResponse);
                i++;
            }

            reRankResponse.setData(data);
        }
        String callBackUrl = metaInfo.getCallBackUrl();
        if (StrUtil.isNotBlank(callBackUrl)) {
            //异步回调
            rocketMqTemplate.syncSend(MqConstants.RANK_CALLBACK_TOPIC, MessageBuilder.withPayload(new RankAsyncBack(callBackUrl, reRankResponse)).build());

        } else {
            //同步返回数据
            SyncResult syncFuture = syncWaitResultService.getSyncFuture(RedisKey.DATA_TO_RANK + appSourceId);
            if (null != syncFuture) {
                syncFuture.setSyncResult(appSourceId, reRankResponse);
            }
        }

    }
}
