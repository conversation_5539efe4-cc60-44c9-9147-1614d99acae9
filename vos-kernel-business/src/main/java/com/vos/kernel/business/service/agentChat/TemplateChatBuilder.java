package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.conver.AiConfigConvertMapper;
import com.vos.kernel.business.dto.ModelConfigDTO;
import com.vos.kernel.business.dto.ModelConfigWorkflowDTO;
import com.vos.kernel.business.dto.ModelConfigurationDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年04月25日
 * @version: 1.0
 * @description: TODO
 */
@Service
@Slf4j
public class TemplateChatBuilder implements ITemplateChatBuilderService {

    @Resource
    AiConfigConvertMapper aiConfigConvertMapper;

    @Value("${event.trigger.outer.endpoint:http://localhost:8081}")
    private String eventTriggerOuterEndpoint;

    @Resource
    WorkflowProperties workflowProperties;

    /**
     * 模版解析器映射
     */
    private Map<Integer, ITemplateChatBuilderService> templateChatBuilderMap = new HashMap<>(32);

    @PostConstruct
    public void init() {
        //注册模版解析器
        registerTemplateChatBuilder(3, this);
    }

    /**
     * 注册模版解析器
     *
     * @param templateType
     * @param templateChatBuilder
     */
    public void registerTemplateChatBuilder(Integer templateType, ITemplateChatBuilderService templateChatBuilder) {
        this.templateChatBuilderMap.put(templateType, templateChatBuilder);
    }


    /**
     * 是否支持模版解析
     *
     * @param templateType
     * @return
     */
    public boolean isSupport(Integer templateType) {
        return templateChatBuilderMap.containsKey(templateType);
    }


    /**
     * 获取模板构建器
     *
     * @param templateType
     * @return
     */
    private ITemplateChatBuilderService getTemplateChatBuilder(Integer templateType) {
        return templateChatBuilderMap.get(templateType);
    }


    @Override
    public void buildTemplateParams(StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity) {
        if (StrUtil.isBlank(agentInfoEntity.getTemplateParams())) {
            throw new BusinessException("智能体模板参数为空");
        }
        Integer agentType = agentInfoEntity.getAgentType();
        ITemplateChatBuilderService templateChatBuilder = getTemplateChatBuilder(agentType);
        if (null != templateChatBuilder) {
            templateChatBuilder.buildTemplateParams(streamChatRequest, agentInfoEntity);
            return;
        }
//        if (6 == agentType) {
//            knowledgeAgentTemplateBuilder.buildTemplateParams(streamChatRequest, agentInfoEntity);
//            return;
//        }
//        if (7 == agentType) {
//            questionBankAgentTemplateBuilder.buildTemplateParams(streamChatRequest, agentInfoEntity);
//            return;
//        }
        if (streamChatRequest.getExtraParams() == null) {
            streamChatRequest.setExtraParams(MapUtil.newHashMap());
        }
        Map<String, String> extraParams = streamChatRequest.getExtraParams();
        extraParams.put("operateType", agentInfoEntity.getOperateType().toString());
        //解析
        ModelConfigurationDTO modelConfiguration = JSON.parseObject(agentInfoEntity.getTemplateParams(), ModelConfigurationDTO.class);
        if (streamChatRequest.getMetaData() == null) {
            streamChatRequest.setMetaData(MapUtil.newHashMap());
        }
        Map<String, Object> metaData = streamChatRequest.getMetaData();
        if (MapUtil.isEmpty(metaData)) {
            if (null != streamChatRequest.getMessage() && CollectionUtil.isNotEmpty(streamChatRequest.getMessage().getData())) {
                metaData.put("image_url", CollectionUtil.getFirst(streamChatRequest.getMessage().getData()));
            }
        }
        if (!metaData.containsKey("ident_area")) {
            metaData.put("ident_area", new JSONArray());
        }
        if (!metaData.containsKey("include_area")) {
            metaData.put("include_area", true);
        }
        if (!metaData.containsKey("image_id")) {
            metaData.put("image_id", "aaas");
        }
        buildTemplateParamsInner(agentInfoEntity.getOperateType(), modelConfiguration, metaData);
    }

    @Override
    public void buildTemplateParamsInner(Integer templateType, ModelConfigurationDTO modelConfiguration, Map<String, Object> metaData) {
        String llmEventDesc = modelConfiguration.getLlmEventDesc();
        metaData.put("prompt", StrUtil.isNotBlank(llmEventDesc) ? llmEventDesc : "");
        metaData.put("warning", StrUtil.isNotBlank(modelConfiguration.getWarningConf()) ? modelConfiguration.getWarningConf() : "");

        if (1 == templateType) {
            metaData.put("model_list", new ArrayList<>());
        } else if (2 == templateType || 3 == templateType) {
            List<ModelConfigDTO> odModelConf = modelConfiguration.getOdModelConf();
            if (CollectionUtil.isEmpty(odModelConf)) {
                throw new BusinessException("模型配置为空");
            }
            metaData.put("model_list", fillModelConfiguration(odModelConf));
        } else if (4 == templateType) {
            List<ModelConfigDTO> odModelConf = modelConfiguration.getOdModelConf();
            if (CollectionUtil.isEmpty(odModelConf)) {
                throw new BusinessException("模型配置为空");
            }
            List<ModelConfigWorkflowDTO> modelConfigWorkflow = fillModelConfiguration(odModelConf);
            List<ModelConfigDTO> filterModelConf = modelConfiguration.getFilterModelConf();
            if (CollectionUtil.isNotEmpty(filterModelConf)) {
                modelConfigWorkflow.addAll(fillModelConfiguration(filterModelConf));
            }
            metaData.put("model_list", modelConfigWorkflow);
        }

    }


    /**
     * 填充模型配置
     *
     * @param odModelConf
     */
    private List<ModelConfigWorkflowDTO> fillModelConfiguration(List<ModelConfigDTO> odModelConf) {

        ArrayList<ModelConfigWorkflowDTO> modelConfigWorkflow = new ArrayList<>();

        for (ModelConfigDTO modelConfigDTO : odModelConf) {
            ModelConfigWorkflowDTO modelConfigWorkflowDTO = aiConfigConvertMapper.modelConfigConvert(modelConfigDTO);

            modelConfigWorkflowDTO.setToken(workflowProperties.getOsToken());
            String router = "/v2/images/detection/od";
            if ("ovd".equals(modelConfigDTO.getType())) {
                router = "/v2/images/detection/ovd";
            }
            if (AbilityApiTypeEnum.OD.getCode().equals(modelConfigDTO.getType())) {
                //od算法设置默认配置code
                modelConfigWorkflowDTO.setDefault_config_code(modelConfigDTO.getModelId() + "-default-conf");
            }
            modelConfigWorkflowDTO.setModel_id(modelConfigDTO.getModelId());
            modelConfigWorkflowDTO.setUrl(eventTriggerOuterEndpoint + router);
            modelConfigWorkflow.add(modelConfigWorkflowDTO);
        }
        return modelConfigWorkflow;
    }


}
