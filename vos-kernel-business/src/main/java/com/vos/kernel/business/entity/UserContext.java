package com.vos.kernel.business.entity;

import com.linker.user.api.dto.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月04日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserContext implements Serializable {

    /**
     * 用户基础信息
     */
    private UserVo user;

    /**
     * 是否有权限标识，结合functionPoint注解使用
     */
    private boolean hasPermission = true;

    /**
     * 租户信息
     */
    private TenantInfoDTO tenantInfoDTO;

    @Data
    public static class TenantInfoDTO{

        private String tenantId;
        private String tenantName;

        private String appKey;

        private String appSecret;
        /**
         * 角色信息
         */
        private List<UserInfo.RoleInfoDTO> roles;
    }

    @Data
    public static class RoleInfoDTO{
        private Long roleId;
        private String roleCode;
        private String roleName;
        private Integer roleType;  //角色类型 1：超级管理员 2：组管理员 3：普通角色
    }
}
