package com.vos.kernel.business.workflow.task;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.ThroughRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class CpuThroughTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {


    public CpuThroughTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.THROUGH.getCode());
    }


    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        ThroughRequest abilityRequest = new ThroughRequest();

        abilityRequest.setModel(taskAddRequest.getTaskName());
        abilityRequest.setRequestJson(parameters);
        TaskTypeAndAbility taskAbility = getTaskAbility(abilityRequest.getModel(), workflowTaskTypeEnum);
        if (log.isDebugEnabled()) {
            log.info("workflow common类型「{}」下发任务,参数：{}", taskAddRequest.getTaskName(), taskAddRequest.getParameters().length() < 218 ? taskAddRequest.getParameters()
                    : StrUtil.subWithLength(taskAddRequest.getParameters(), 0, 218));
        }
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.THROUGH.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setSubList(buildCpuTaskInfo(abilityRequest, taskAbility));

        //缓存元数据
        ModelMetaInfo embeddingMetaInfo = new ModelMetaInfo(taskAddRequest.getCallbackUrl(), DubboDataContext.getAuthIdHolder(), taskAbility.getActionId(), new Date().getTime());
        embeddingMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        embeddingMetaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(embeddingMetaInfo));
        return taskAdd;
    }

    /**
     * 构造文本向量化参数
     *
     * @param throughRequest
     * @param abilityEntity
     * @return
     */
    private List<TaskSubDTO> buildCpuTaskInfo(ThroughRequest throughRequest, TaskTypeAndAbility abilityEntity) {
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(throughRequest.getTaskOrder(), throughRequest.getVideoClusterSource(), abilityEntity);
        taskSubDTO.setRequestJson(throughRequest.getRequestJson());
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }

}
