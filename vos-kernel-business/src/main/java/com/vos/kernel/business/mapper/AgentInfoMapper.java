package com.vos.kernel.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vos.kernel.business.entity.AgentInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * agent信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Mapper
public interface AgentInfoMapper extends BaseMapper<AgentInfoEntity> {

    /**
     * 分页查询Agent信息
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param agentName 智能体名称
     * @param description 描述
     * @param running 运行状态
     * @return 分页结果
     */
    IPage<AgentInfoEntity> queryAgentInfoPage(
            Page<AgentInfoEntity> page,
            @Param("tenantId") String tenantId,
            @Param("agentName") String agentName,
            @Param("description") String description,
            @Param("running") Integer running,
            @Param("all") String all
    );
}
