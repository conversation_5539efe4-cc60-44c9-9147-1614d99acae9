package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.AttrDetRequest;
import com.linker.omos.client.domain.request.MultiModelEmbeddingRequest;
import com.linker.omos.client.domain.request.OcrRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.EmbeddingMetaInfo;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class EmbeddingTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Value("${ability.embedding.base64Size:5}")
    private Integer base64Size;

    @Value("${task.expired:1}")
    private Integer expired;

    @Resource
    Validator validator;

    public EmbeddingTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.V1.getCode());
    }


    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        MultiModelEmbeddingRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, MultiModelEmbeddingRequest.class);
        } catch (Exception e) {
            throw new BusinessException("embedding调用参数解析失败");
        }
        abilityRequest.setModel(taskAddRequest.getModel());
        Set<ConstraintViolation<MultiModelEmbeddingRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        String inputType = abilityRequest.getInputType();
        if (!"text".equals(inputType) && !"base64".equals(inputType) && !"image_url".equals(inputType)) {
            throw new BusinessException("输入类型只能是text|base64|image_url的一种");
        }


        TaskTypeAndAbility taskAbility = getTaskAbility(abilityRequest.getModel(), workflowTaskTypeEnum);
        //base64图片特殊处理
        if (inputType.equals(EmbeddingInputEnum.BASE64.getCode())) {
            if (abilityRequest.getInputs().size() > base64Size) {
                throw new com.linker.core.base.exception.BusinessException("base64图片向量化批次不能大于:" + base64Size);
            }
        }
        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.MULTI_EMBEDDING.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setSubList(buildEmbeddingTaskInfo(abilityRequest, taskAbility, taskAdd.getAppSourceId()));

        //缓存元数据
        EmbeddingMetaInfo embeddingMetaInfo = new EmbeddingMetaInfo(taskAddRequest.getCallbackUrl(), DubboDataContext.getAuthIdHolder(), taskAbility.getActionId(), new Date().getTime());
        embeddingMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        embeddingMetaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(embeddingMetaInfo));
        return taskAdd;
    }

    /**
     * 构造文本向量化参数
     *
     * @param embeddingRequest
     * @param abilityEntity
     * @return
     */
    private List<TaskSubDTO> buildEmbeddingTaskInfo(MultiModelEmbeddingRequest embeddingRequest, TaskTypeAndAbility abilityEntity, String appSourceId) {
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(embeddingRequest.getTaskOrder(), embeddingRequest.getVideoClusterSource(), abilityEntity);

        //图片base64处理
        if (embeddingRequest.getInputType().equals(EmbeddingInputEnum.BASE64.getCode())) {
            embeddingRequest.setInputs(base64Embedding(embeddingRequest.getInputs(), appSourceId));
        }
        taskSubDTO.setRequestJson(buildRequestStr(embeddingRequest.getInputs(), embeddingRequest.getInputType()));
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }

    /**
     * 构造算法请求参数
     *
     * @param inputs
     * @param inputType
     * @return
     */
    private String buildRequestStr(List<String> inputs, String inputType) {
        List<AiExecuteImageParamDTO> src = new ArrayList<>();
        int i = 0;
        String eventTime = DateUtil.getSimpleYMDHMS(LocalDateTime.now());
        String videoValidation = String.valueOf(IdWorker.nextId());
        String taskId = String.valueOf(IdWorker.nextId());
        for (String s : inputs) {
            AiExecuteImageParamDTO aiExecuteImageParamDTO = new AiExecuteImageParamDTO();
            aiExecuteImageParamDTO.setImageId(String.valueOf(i));
            aiExecuteImageParamDTO.setVideoId("vector");
            aiExecuteImageParamDTO.setSrcType(EmbeddingInputEnum.matchApiType(inputType));
            aiExecuteImageParamDTO.setData(s);
            aiExecuteImageParamDTO.setEventTime(eventTime);
            aiExecuteImageParamDTO.setTaskId(taskId);
            aiExecuteImageParamDTO.setOrgId(DubboDataContext.getAuthIdHolder());
            aiExecuteImageParamDTO.setVideoValidation(videoValidation);
            src.add(aiExecuteImageParamDTO);
            i++;
        }
        return buildModelRequestStr(src, taskId);
    }

    /**
     * base64图片特殊处理
     *
     * @param inputs
     * @return
     */
    private List<String> base64Embedding(List<String> inputs, String appSourceId) {
        int i = 0;
        List<String> base64CacheKeyList = new ArrayList<>();
        for (String s : inputs) {
            String imageId = appSourceId + "_" + i;
            String redisKey = "task:invented:img_" + imageId;
            Duration duration = Duration.ofMinutes(expired);
            stringRedisTemplate.opsForValue().set(redisKey, s, duration);
            base64CacheKeyList.add(imageId);
            i++;
        }
        return base64CacheKeyList;
    }

}
