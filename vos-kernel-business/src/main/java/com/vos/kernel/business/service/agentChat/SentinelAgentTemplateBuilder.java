package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.vos.kernel.business.dto.ModelConfigurationDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.agent.AgentConfigDTO;
import com.vos.kernel.business.dto.agent.QuestionBankAgentTemplateDTO;
import com.vos.kernel.business.dto.agent.SentinelAgentDataInfo;
import com.vos.kernel.business.entity.AgentInfoEntity;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年06月05日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class SentinelAgentTemplateBuilder implements ITemplateChatBuilderService {

    @Resource
    TemplateChatBuilder templateChatBuilder;


    private final static Integer TEMPLATE_TYPE = 8;

    @PostConstruct
    public void init() {
        templateChatBuilder.registerTemplateChatBuilder(TEMPLATE_TYPE, this);
    }

    @Override
    public void buildTemplateParamsInner(Integer templateType, ModelConfigurationDTO modelConfiguration, Map<String, Object> metaData) {

    }

    @Override
    public void buildTemplateParams(StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity) {
        String templateParams = agentInfoEntity.getTemplateParams();
        if (streamChatRequest.getMetaData() == null) {
            streamChatRequest.setMetaData(MapUtil.newHashMap());
        }
        SentinelAgentDataInfo sentinelAgentDataInfo = JSON.parseObject(templateParams, SentinelAgentDataInfo.class);
        //培训智能体独有字段
        String agentConfig = agentInfoEntity.getAgentConfig();
        if (StrUtil.isNotBlank(agentConfig)) {
            AgentConfigDTO agentConfigDTO = JSON.parseObject(agentConfig, AgentConfigDTO.class);
            List<SentinelAgentDataInfo.TaskInfo> taskInfos = sentinelAgentDataInfo.getTaskInfos();

            if (CollectionUtil.isNotEmpty(taskInfos)) {
                List<String> sentinelTaskIds = taskInfos.stream().map(SentinelAgentDataInfo.TaskInfo::getTaskId).collect(Collectors.toList());
                agentConfigDTO.setSentinelTaskIds(sentinelTaskIds);
                agentInfoEntity.setAgentConfig(JSON.toJSONString(agentConfigDTO));
            }
        }
    }
}
