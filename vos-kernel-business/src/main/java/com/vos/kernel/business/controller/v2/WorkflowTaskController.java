package com.vos.kernel.business.controller.v2;

import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.dto.PlatformModelLabelRequest;
import com.vos.kernel.business.dto.SyncPlatformModelRequest;
import com.vos.kernel.business.service.AppManageManagerService;
import com.vos.kernel.business.workflow.RpcScheduleStrategyService;
import com.vos.kernel.business.workflow.ScheduleStrategyProxy;
import com.vos.kernel.common.entity.R;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 * @version: 1.0
 * @description: TODO
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1")
@Slf4j
@Api(value = "workflow管理")
public class WorkflowTaskController {

    @Resource
    ScheduleStrategyProxy scheduleStrategyProxy;

    @Resource
    RpcScheduleStrategyService rpcScheduleStrategyService;

    @Resource
    AppManageManagerService appManageService;

    /**
     * 新增任务
     *
     * @param taskAddRequest 任务
     * @return R 通用返回体
     */
    @PostMapping("/add")
    @Token
    public R<TaskAddResponse> save(@RequestBody @Valid TaskAddRequest taskAddRequest) {

        return R.ok(scheduleStrategyProxy.addTask(taskAddRequest));
    }


    /**
     * 批量拉取任务
     *
     * @param taskPollRequest 任务
     * @return R 通用返回体
     */
    @PostMapping("/batchPoll")
    @Token
    public R<List<TaskMessage>> batchPoll(@RequestBody @Valid TaskPollRequest taskPollRequest) {

        return R.ok(rpcScheduleStrategyService.batchPoll(taskPollRequest));
    }


    /**
     * 更新任务状态
     *
     * @param taskUpdateRequest 任务
     * @return R 通用返回体
     */
    @PostMapping("/updateTaskStatus")
    @Token
    public R<Boolean> updateTaskStatus(@RequestBody @Valid TaskUpdateRequest taskUpdateRequest) {

        return R.ok(rpcScheduleStrategyService.updateTaskStatus(taskUpdateRequest));
    }

    /**
     * 同步平台模型
     *
     * @param syncPlatformModelRequest
     * @return R 通用返回体
     */
    @PostMapping("/syncPlatformModel")
    @Token
    public R<Boolean> syncPlatformModel(@RequestBody @Valid SyncPlatformModelRequest syncPlatformModelRequest) {

        return R.ok(appManageService.syncPlatformModel(syncPlatformModelRequest));
    }

    /**
     * 设置平台模型标签
     *
     * @param platformModelLabelRequest
     * @return R 通用返回体
     */
    @PostMapping("/setPlatformModelLabel")
    @Token
    public R<Boolean> setPlatformModelLabel(@RequestBody @Valid PlatformModelLabelRequest platformModelLabelRequest) {

        return R.ok(appManageService.setPlatformModelLabel(platformModelLabelRequest));
    }

    /**
     * 获取平台模型标签
     *
     * @param taskTypeCode
     * @return R 通用返回体
     */
    @GetMapping("/getPlatformModelLabel")
    @Token
    public R<String> getPlatformModelLabel(@RequestParam("taskTypeCode") String taskTypeCode) {

        return R.ok(appManageService.getPlatformModelLabel(taskTypeCode));
    }

    /**
     * 获取目标模型识别标签
     *
     * @param taskTypeCode
     * @return R 通用返回体
     */
    @GetMapping("/getOdModelLabel")
    @Token
    public R<List<String>> getOdModelLabel(@RequestParam("taskTypeCode") String taskTypeCode) {

        return R.ok(appManageService.getOdModelLabel(taskTypeCode));
    }

}
