package com.vos.kernel.business.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.vos.kernel.business.dto.ComparisonGroupResponse;
import com.vos.kernel.business.dto.ComparisonMatchDTO;
import com.vos.kernel.business.vo.TypeInventedSub;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.core.api.domain.AbilityEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.DataRefluxDTO;
import com.vos.kernel.data.api.model.req.SaveInventedSub;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/6
 * @description: com.vos.kernel.business.service
 */
public interface ICacheService {

    /**
     * 获取
     *
     * @param orgCodeActionId
     * @return
     */
    TaskTypeEntity getUserV3Ability(String orgCodeActionId);

    /**
     * 获取
     *
     * @param abilityId
     * @return
     */
    TaskTypeEntity getAbilityByTaskAbilityId(String abilityId);


    /**
     * 获取chat回流缓存数据
     *
     * @param appSourceId
     * @return
     */
    DataRefluxDTO getChatDataReflux(String appSourceId);

    /**
     * 设置chat回流缓存数据
     *
     * @param appSourceId
     */
    void setChatDataReflux(String appSourceId, DataRefluxDTO dataReflux, Integer expireLong);

    /**
     * 删除缓存
     *
     * @param orgCodeActionId
     */
    void deleteUserV3Ability(String orgCodeActionId);

    /**
     * v35
     *
     * @param orgCodeActionId
     * @return
     */
    TaskTypeEntity getUserV35Ability(String orgCodeActionId);

    /**
     * 删除缓存
     *
     * @param orgCodeActionId
     */
    void deleteUserV35Ability(String orgCodeActionId);

    List<TypeInventedSub> getActionIdByCode(String orgCode);

    /**
     * 删除缓存
     *
     * @param orgCode
     */
    void deleteActionIdByCode(String orgCode);

    List<SaveInventedSub> getActionIdByUser(String orgCode);

    /**
     * 删除缓存
     *
     * @param orgCodeActionId
     */
    void deleteActionIdByUser(String orgCodeActionId);


    /**
     * 获取
     *
     * @param orgCodeActionId
     * @return
     */
    TaskTypeEntity getUserAbility(String orgCodeActionId);

    /**
     * 删除缓存
     *
     * @param orgCodeActionId
     */
    void deleteUserAbility(String orgCodeActionId);

    @Cached(name = "cache:userAbilityIdCache", expire = 300, cacheType = CacheType.BOTH)
    Long getAbilityId(String taskTypeCode);

    TaskTypeEntity getUserDynamicAbility(String operator, String actionId, String type);


    /**
     * 获取算法下的所有分组
     *
     * @param taskTypeCodeAndBizType
     * @return
     */
    List<ComparisonGroupResponse> getAbilityComparisonGroup(String taskTypeCodeAndBizType);


    /**
     * 删除缓存
     *
     * @param taskTypeCodeAndBizType
     */
    void deleteAbilityComparisonGroupCache(String taskTypeCodeAndBizType);


    /**
     * 获取匹配数据
     *
     * @param matchId
     * @return
     */
    ComparisonMatchDTO getAbilityComparisonMatch(String matchId);

    /**
     * 匹配最大值
     *
     * @return
     */
    long getAbilityComparisonMatchMaxId();

    /**
     * 删除缓存
     *
     * @param
     */
    void deleteAbilityComparisonMatchMaxId();


    /**
     * 获取应用信息
     *
     * @param orgCodeActionId
     * @return
     */
    TaskTypeAndAbility getAbilityEntityCache(String orgCodeActionId);

    /**
     * 删除缓存
     * @param orgCodeActionId
     */
    void deleteAbilityEntityCache(String orgCodeActionId);
}
