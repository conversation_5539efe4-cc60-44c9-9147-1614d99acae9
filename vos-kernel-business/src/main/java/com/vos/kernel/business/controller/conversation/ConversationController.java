package com.vos.kernel.business.controller.conversation;

import com.linker.core.base.baseclass.BaseResp;
import com.vos.kernel.business.dto.CreateConversationRequest;
import com.vos.kernel.business.dto.CreateConversationResponse;
import com.vos.kernel.business.service.conversation.IConversationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/12/9 11:03
 * @DESC: 会话对外接口
 */
@RestController
@RequestMapping("api/conversation")
public class ConversationController {

    @Autowired
    private IConversationService conversationService;

    @RequestMapping("/v1/create")
    public BaseResp<CreateConversationResponse> createConversation(@RequestBody CreateConversationRequest createConversationRequest) {
        return new BaseResp<>(conversationService.createConversation(createConversationRequest));
    }
}
