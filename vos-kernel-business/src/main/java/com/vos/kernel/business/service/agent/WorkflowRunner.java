package com.vos.kernel.business.service.agent;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSON;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.dto.agent.WorkflowRequestHeaderDTO;
import com.vos.kernel.business.dto.agent.WorkflowTriggerParamDTO;
import com.vos.kernel.business.dto.agent.WorkflowTriggerResultDTO;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.entity.UserVo;
import com.vos.kernel.business.mq.HttpEventListener;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class WorkflowRunner implements IWorkflowRunner {

    @Resource
    WorkflowProperties workflowProperties;

    private OkHttpClient workflowOkHttp;

    @PostConstruct
    public void init() {
        workflowOkHttp = new OkHttpClient().newBuilder()
                .readTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                .connectTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                .writeTimeout(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(workflowProperties.getMaxIdleConnections(), workflowProperties.getKeepAliveDuration(), TimeUnit.SECONDS))
                .retryOnConnectionFailure(true)
                .eventListenerFactory(HttpEventListener.FACTORY)
                .build();
        workflowOkHttp.dispatcher().setMaxRequests(workflowProperties.getMaxRequests());
        workflowOkHttp.dispatcher().setMaxRequestsPerHost(workflowProperties.getMaxRequestsPerHost());
    }

    /**
     * 构建请求头
     * @param headerDTO 请求头
     * @return 请求头构建器
     */
    private Request.Builder headersBuilder(WorkflowRequestHeaderDTO headerDTO) {
        UserContext userInfo = headerDTO.getUserInfo()==null ?new UserContext():headerDTO.getUserInfo();

        Request.Builder requestBuilder = new Request.Builder();
        if (headerDTO.getTenantId() != null) {
            UserContext.TenantInfoDTO tenantInfoDTO = new UserContext.TenantInfoDTO();
            tenantInfoDTO.setTenantId(headerDTO.getTenantId());
            userInfo.setTenantInfoDTO(tenantInfoDTO);
            requestBuilder.addHeader("tenantId", headerDTO.getTenantId());
        }
        if (headerDTO.getUserCode() != null) {
            UserVo userVo = new UserVo();
            userVo.setUserCode(headerDTO.getUserCode());
            userInfo.setUser(userVo);
            requestBuilder.addHeader("userCode", headerDTO.getUserCode());
        }
        try {
            //添加统一
            String json =  URLEncoder.encode(JSON.toJSONString(userInfo), "utf-8");
            requestBuilder.addHeader("x-linker-rpc-userinfo", json);
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("添加x-linker头异常",e);
        }
        return requestBuilder;
    }

    /**
     * 上传工作流文件
     * @param workflowConfigFile 工作流文件
     * @param headerDTO 请求头
     * @return 上传结果
     */
    @Override
    public Boolean uploadWorkflow(byte[] workflowConfigFile, WorkflowRequestHeaderDTO headerDTO) {
        try {
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", "workflow.zip",
                            RequestBody.create(workflowConfigFile, MediaType.parse("application/octet-stream")))
                    .build();

            Request.Builder requestBuilder = headersBuilder(headerDTO)
                    .url(workflowProperties.getEndpoint() + "/provideWorkflow/importPackage")
                    .post(requestBody);

            Request request = requestBuilder.build();

            try (Response response = workflowOkHttp.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    return true;
                }
                log.error("上传工作流文件失败, 状态码: {}", response.code());
            }
        } catch (IOException e) {
            log.error("上传工作流文件失败: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 执行工作流
     * @param request 工作流参数
     * @return 工作流执行结果
     */
    @Override
    public String runWorkflow(WorkflowTriggerParamDTO request) {
        // 创建请求
        Request httpRequest = headersBuilder(request.getHeaderDTO())
                .url(workflowProperties.getEndpoint() + "/provideWorkflow/commonRun")
                .post(RequestBody.create(JSON.toJSONString(request), MediaType.parse("application/json")))
                .build();

        log.info("工作流执行参数：{}", JSON.toJSONString(request));

        // 执行工作流
        try (Response response = workflowOkHttp.newCall(httpRequest).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("工作流执行返回结果：{}", responseBody);
                WorkflowTriggerResultDTO result = JSON.parseObject(responseBody, WorkflowTriggerResultDTO.class);
                if (result.getSuccess()) {
                    String conductorRunCode = result.getData();
                    log.info("工作流执行成功，conductorRunCode：{}", conductorRunCode);
                    return conductorRunCode;
                } else {
                    log.error("工作流执行失败：{}", result.getMessage());
                }
            } else {
                log.error("工作流执行结果失败：{}{}", response.code(), response.message());
            }
        } catch (IOException e) {
            log.error("工作流执行异常失败：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 更新工作流限流配置
     * @param workflowUniqueCode 工作流唯一标识
     * @param qps QPS限制，null表示不限流
     * @param tenantId 租户ID
     * @return 更新是否成功
     */
    public boolean updateWorkflowConfig(String workflowUniqueCode, Integer qps, String tenantId) {
        try {
            log.info("调用底层接口更新工作流限流配置, workflowUniqueCode: {}, qps: {}, tenantId: {}", 
                workflowUniqueCode, qps, tenantId);
                
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("workflowUniqueCode", workflowUniqueCode);
            params.put("qps", qps);
            
            // 构建请求header
            WorkflowRequestHeaderDTO headerDTO = new WorkflowRequestHeaderDTO();
            headerDTO.setTenantId(tenantId);
            
            // 创建请求
            Request httpRequest = headersBuilder(headerDTO)
                    .url(workflowProperties.getEndpoint() + "/provideWorkflow/updateWorkflowQps")
                    .post(RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json")))
                    .build();

            // 执行请求
            try (Response response = workflowOkHttp.newCall(httpRequest).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("更新工作流限流配置返回结果：{}", responseBody);
                    return true;
                } else {
                    log.error("更新工作流限流配置失败：{}{}", response.code(), response.message());
                }
            }
        } catch (Exception e) {
            log.error("调用底层接口更新工作流限流配置失败", e);
        }
        return false;
    }

    /**
     * 更新工作流状态
     * @param workflowUniqueCode 工作流唯一标识
     * @param workflowStatus 工作流状态，0:启用, 1:停用
     * @param tenantId 租户ID
     * @return 更新是否成功
     */
    public boolean updateWorkflowStatus(String workflowUniqueCode, Integer workflowStatus, String tenantId) {
        try {
            log.info("调用底层接口更新工作流状态, workflowUniqueCode: {}, workflowStatus: {}, tenantId: {}", 
                workflowUniqueCode, workflowStatus, tenantId);
                
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("workflowUniqueCode", workflowUniqueCode);
            params.put("workflowStatus", workflowStatus);
            
            // 构建请求header
            WorkflowRequestHeaderDTO headerDTO = new WorkflowRequestHeaderDTO();
            headerDTO.setTenantId(tenantId);
            
            // 创建请求
            Request httpRequest = headersBuilder(headerDTO)
                    .url(workflowProperties.getEndpoint() + "/provideWorkflow/updateWorkflow")
                    .post(RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json")))
                    .build();

            // 执行请求
            try (Response response = workflowOkHttp.newCall(httpRequest).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("更新工作流状态返回结果：{}", responseBody);
                    return true;
                } else {
                    log.error("更新工作流状态失败：{}{}", response.code(), response.message());
                }
            }
        } catch (Exception e) {
            log.error("调用底层接口更新工作流状态失败", e);
        }
        return false;
    }
}
