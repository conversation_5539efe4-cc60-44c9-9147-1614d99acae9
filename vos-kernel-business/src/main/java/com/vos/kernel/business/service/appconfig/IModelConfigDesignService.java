package com.vos.kernel.business.service.appconfig;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年07月25日
 * @version: 1.0
 * @description: TODO
 */
public interface IModelConfigDesignService {

    /**
     * 获取模型初始配置信息
     *
     * @param taskTypeId
     * @return
     */
    List<AppConfigDTO> getModelInitConfig(Integer taskTypeId);

    /**
     * 删除模型初始配置信息缓存
     *
     * @param taskTypeId
     */
    void deleteModelInitConfigCache(String taskTypeId);


    /**
     * 获取定制configCode
     *
     * @param configCode
     * @return
     */
    List<AppConfigThirdContainsResponse> getModelCustomConfig(String configCode);

    /**
     * 获取定制configCode
     *
     * @param configCode
     * @return
     */
    ModelCustomConfigDTO getModelCustomConfigWithName(String configCode);


    /**
     * 删除定制configCode缓存
     *
     * @param configCode
     */
    void deleteModelCustomConfigCache(String configCode);

    /**
     * 删除定制configCode缓存
     *
     * @param configCode
     */
    void deleteModelCustomConfigNameCache(String configCode);

    /**
     * 创建模型配置
     *
     * @param appConfigCreateRequest
     * @param userAbility
     * @return
     */
    String createModelConfig(AppConfigCreateRequest appConfigCreateRequest, TaskTypeEntity userAbility, List<AppConfigThirdContainsResponse> originConfig);

    /**
     * 更新模型配置
     *
     * @param appConfigUpdateRequest
     * @param userAbility
     * @return
     */
    String updateModelConfig(AppConfigUpdateRequest appConfigUpdateRequest, TaskTypeEntity userAbility, List<AppConfigThirdContainsResponse> modelCustomConfig);

    /**
     * 获取所有的自定义配置列表
     *
     * @param userAbility
     * @return
     */
    Page<AppConfigListItem> getCustomAppConfigList(TaskTypeEntity userAbility, AppConfigListRequest appConfigListRequest);


    /**
     * 删除指定自定义配置
     *
     * @param configCode
     * @return
     */
    Boolean deleteModelCustomConfig(String configCode);
}
