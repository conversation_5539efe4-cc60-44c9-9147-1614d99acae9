package com.vos.kernel.business.workflow.task;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.CommonConstant;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.OdAndAttrSingleRequest;
import com.linker.omos.client.domain.request.OvdItemRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.config.RefluxProperties;
import com.vos.kernel.business.dto.AppConfigThirdContainsResponse;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.service.appconfig.IModelConfigDesignService;
import com.vos.kernel.business.service.embedding.ModelCommonService;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.CallBackTypeEnum;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.common.workflow.TaskBuilderHolder;
import com.vos.kernel.common.workflow.TaskRespCodeEnum;
import com.vos.kernel.common.workflow.WorkflowTaskException;
import com.vos.kernel.core.api.domain.AbilityEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public abstract class TaskAddBaseBuilder extends ModelCommonService implements TaskAddBuilderService {

    /**
     * 算法类型
     */
    protected List<String> type;

    @Resource
    TaskBuilderHolder taskBuilderHolder;

    @Resource
    ICacheService cacheService;

    @Resource
    RefluxProperties refluxProperties;

    @Resource
    IModelConfigDesignService modelConfigDesignService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Value("${task.expired:1}")
    private Integer expired;

    @Value("${v3.taskNum:100}")
    private Integer taskNum;

    @Value("${v3.contentLength:512}")
    private Integer contentLength;

    @Value("${v3.labelNum:100}")
    private Integer labelNum;


    @PostConstruct
    private void init() {
        for (String s : type) {
            taskBuilderHolder.registeTaskHandler(s, this);
        }
    }


    /**
     * 获取任务能力并进行基础校验
     *
     * @param modelCode
     * @param taskTypeEnum
     * @return
     */
    protected TaskTypeAndAbility getTaskAbility(String modelCode, WorkflowTaskTypeEnum taskTypeEnum) {
        TaskTypeAndAbility userAbility = cacheService.getAbilityEntityCache(DubboDataContext.getAuthIdHolder() + "#" + modelCode);
        if (null == userAbility) {
            throw new WorkflowTaskException(TaskRespCodeEnum.UNAVAILABLE_EXCEPTION, TaskRespCodeEnum.UNAVAILABLE_EXCEPTION.getMessage() + ":" + modelCode);
        }
        if (userAbility.getStatus() != 0) {
            throw new WorkflowTaskException(TaskRespCodeEnum.STOP_EXCEPTION);
        }
        if (WorkflowTaskTypeEnum.V3_CHAT.equals(taskTypeEnum) || WorkflowTaskTypeEnum.LLM.equals(taskTypeEnum)) {
            if (!WorkflowTaskTypeEnum.V3_CHAT.getCodeInteger().equals(userAbility.getAbilityEnum()) && !WorkflowTaskTypeEnum.LLM.getCodeInteger().equals(userAbility.getAbilityEnum())) {
                throw new WorkflowTaskException("算法类型不匹配不可调用");
            }
        } else {
            if (!taskTypeEnum.getCodeInteger().equals(userAbility.getAbilityEnum())) {
                throw new WorkflowTaskException("算法类型不匹配不可调用");
            }
        }
        return userAbility;
    }


    /**
     * 获取配置信息
     *
     * @param configCode
     * @return
     */
    protected List<AppConfigThirdContainsResponse> getConfigList(String configCode) {
        //获取配置信息
        List<AppConfigThirdContainsResponse> modelCustomConfig = new ArrayList<>();
        if (StrUtil.isNotBlank(configCode)) {
            modelCustomConfig = modelConfigDesignService.getModelCustomConfig(configCode);
        }

        return modelCustomConfig;
    }


    /**
     * 构造基础子任务
     *
     * @param taskOrder
     * @param ability
     * @return
     */
    protected TaskSubDTO buildBaseSubTaskInfo(Integer taskOrder, String videoClusterSource, TaskTypeAndAbility ability) {
        TaskSubDTO aiJobDetail = buildSubTask(taskOrder, videoClusterSource);

        //构造额外信息
        aiJobDetail.setAbilityId(ability.getOperatorId());
        aiJobDetail.setHzAbilityId(ability.getTbAbilityId().longValue());
        aiJobDetail.setOmAppId(ability.getAbility());
        return aiJobDetail;
    }


    /**
     * 处理图片
     *
     * @param
     */
    protected String handleImage(String inputType, String input, String appSourceId) {
        //base64图片处理
        if (inputType.equals(EmbeddingInputEnum.BASE64.getCode())) {
            String imageId = appSourceId + "_" + 0;
            String redisKey = "task:invented:img_" + imageId;
            Duration duration = Duration.ofMinutes(expired);
            stringRedisTemplate.opsForValue().set(redisKey, input, duration);
            return imageId;
        } else if (inputType.equals(EmbeddingInputEnum.IMAG.getCode())) {
            if (!input.contains(CommonConstant.HTTP)) {
                throw new BusinessException("image_url图片格式不正确");
            }
        }
        return input;
    }


    /**
     * 参数校验
     *
     * @param tasks
     */
    protected void paramsCheck(List<OvdItemRequest> tasks) {

        if (tasks.size() > taskNum) {
            throw new BusinessException("任务数不能超过" + taskNum);
        }
        HashSet<String> taskUniqueIds = new HashSet<>();

        for (OvdItemRequest v : tasks) {
            if (StrUtil.isBlank(v.getUniqueId())) {
                throw new BusinessException("任务唯一标识不能为空");
            }
            taskUniqueIds.add(v.getUniqueId());
            String label = v.getLabel();
            String prompt = v.getPrompt();
            if (StrUtil.isBlank(label) && StrUtil.isBlank(prompt)) {
                throw new BusinessException("目标项与任务描述信息不能都为空");
            }
            if (StrUtil.isNotBlank(v.getPrompt()) && StrUtil.length(prompt) > contentLength) {
                throw new BusinessException("任务描述信息不能超过" + contentLength + "个字符");
            }
            //label校验
            if (StrUtil.isNotBlank(label)) {
                List<String> split = StrUtil.split(label, ',');
                if (split.size() > labelNum) {
                    throw new BusinessException("目标项不能超过" + labelNum);
                }
            }
            //置信度校验
            if (StrUtil.isNotBlank(v.getConf()) && NumberUtil.isNumber(v.getConf())) {
                if (!v.getConf().contains(".")) {
                    throw new BusinessException("置信度值请保留两位小数");
                }
                float threshold = Float.parseFloat(v.getConf());
                if (threshold < 0 || threshold > 1) {
                    throw new BusinessException("置信度取值范围[0,1]");
                }
                if (v.getConf().split("\\.")[1].length() > 2) {
                    throw new BusinessException("置信度值精度最多保留两位小数");
                }
            } else {
                v.setConf(null);
            }
            //逻辑替换
            if (StrUtil.isBlank(label)) {
                v.setLabel(prompt);
                v.setPrompt("");
            }
        }
        if (taskUniqueIds.size() != tasks.size()) {
            throw new BusinessException("任务列表任务唯一值存在重复值");
        }
    }
}

