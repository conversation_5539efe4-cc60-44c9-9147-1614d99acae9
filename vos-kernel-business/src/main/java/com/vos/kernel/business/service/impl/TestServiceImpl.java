package com.vos.kernel.business.service.impl;

import com.vos.kernel.core.api.rpc.ICoreTestRpcService;
import com.vos.kernel.data.api.rpc.IDataTestRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/7
 * @description: com.vos.kernel.business.service.impl
 */
@Slf4j
@Service
public class TestServiceImpl {

    @DubboReference
    ICoreTestRpcService coreTestRpcService;

    @DubboReference
    IDataTestRpcService dataTestRpcService;

    public String testGlobalTransaction() {
        coreTestRpcService.testEvent();
//        String core = coreTestRpcService.testGlobalTransaction();
//        log.info("core 返回：{}", core);
//        String data = dataTestRpcService.testGlobalTransaction();
//        log.info("data 返回：{}", data);
        return "success";
    }
}
