package com.vos.kernel.business.config;

import org.dromara.dynamictp.core.support.ThreadPoolBuilder;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25
 * @description: com.vos.kernel.core.service.config
 */
@Service
@Configuration
public class ThreadPoolConfig {

    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "vectorSearch")
    public DtpExecutor vectorSearchExecutor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("vectorSearchThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("vectorSearchThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }


    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "v1Executor")
    public DtpExecutor v1Executor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("v1ExecutorThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("v1ExecutorThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }

    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "v2Executor")
    public DtpExecutor v2Executor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("v2ExecutorThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("v2ExecutorThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }


    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "v3Executor")
    public DtpExecutor v3Executor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("v3ExecutorThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("v3ExecutorThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }


    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "v35Executor")
    public DtpExecutor v35Executor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("v35ExecutorThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("v35ExecutorThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }


    /**
     * 回流异步处理线程池
     *
     * @return
     */
    @Bean(name = "otherExecutor")
    public DtpExecutor otherExecutor() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("otherExecutorThreadPool")
                .corePoolSize(60)
                .maximumPoolSize(100)
                .queueCapacity(1024)
                .threadFactory("otherExecutorThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }

    /**
     * serving 任务回调处理动态线程池
     *
     * @return
     */
    @Bean(name = "metricThreadPool")
    public DtpExecutor metricThreadPool() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("metricThreadPool")
                .corePoolSize(10)
                .maximumPoolSize(200)
                .queueCapacity(128)
                .threadFactory("metricThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }

    /**
     * 比对图片上传处理动态线程池
     *
     * @return
     */
    @Bean(name = "comparisonUploadThreadPool")
    public DtpExecutor comparisonUploadThreadPool() {
        return ThreadPoolBuilder.newBuilder()
                .threadPoolName("comparisonUploadThreadPool")
                .corePoolSize(16)
                .maximumPoolSize(100)
                .queueCapacity(32)
                .threadFactory("comparisonUploadThread-")
                .rejectedExecutionHandler("CallerRunsPolicy")
                .buildDynamic();
    }
}
