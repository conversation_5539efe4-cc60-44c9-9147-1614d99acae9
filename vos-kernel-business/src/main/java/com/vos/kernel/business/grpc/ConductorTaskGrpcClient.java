package com.vos.kernel.business.grpc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.config.WorkflowTaskExecLog;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.common.grpc.*;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import io.grpc.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年05月26日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class ConductorTaskGrpcClient {

    private ManagedChannel channel;
    private TaskServiceGrpc.TaskServiceBlockingStub stub;

    WorkflowProperties workflowProperties;

    /**
     * 构造函数
     *
     * @param workflowProperties
     */
    public ConductorTaskGrpcClient(WorkflowProperties workflowProperties) {
        if (!BooleanUtil.isTrue(workflowProperties.getEnableConductorGrpc())) {
            return;
        }
        log.info("ConductorTaskGrpcClient init");
        this.workflowProperties = workflowProperties;
        WorkflowProperties.GrpcProperties grpc = workflowProperties.getGrpc();
        this.channel = ManagedChannelBuilder.forAddress(grpc.getHost(), grpc.getPort())
                .usePlaintext()
                .enableRetry()
                .maxRetryAttempts(3)
                .idleTimeout(360, TimeUnit.SECONDS)
                .keepAliveTime(30, TimeUnit.SECONDS)
                .keepAliveTimeout(5, TimeUnit.SECONDS)
                .compressorRegistry(CompressorRegistry.getDefaultInstance())
                .build();
        this.stub = TaskServiceGrpc.newBlockingStub(channel);
    }


    /**
     * 关闭连接
     *
     * @throws InterruptedException
     */
    private void shutdown() throws InterruptedException {
        channel.shutdown().awaitTermination(10, TimeUnit.SECONDS);
    }


    /**
     * 更新任务状态
     *
     * @param workflowCallBackContent
     */
    public void updateTask(Boolean isPull, String taskId, WorkflowCallBackContent workflowCallBackContent) {
        try {
            if (log.isDebugEnabled() && StrUtil.isNotBlank(taskId)) {
                log.debug("{} update Conductor Task {} GRPC调用开始", BooleanUtil.isTrue(isPull) ? "拉取任务后" : "更新任务状态", taskId);
            }
            stub.updateTask(
                    ConductorTaskOperate.UpdateTaskRequest.newBuilder()
                            .setResult(toProto(workflowCallBackContent))
                            .build());
        } catch (Exception e) {
            log.error("{} updateTask Conductor Task {}  GRPC调用失败:", BooleanUtil.isTrue(isPull) ? "拉取任务后" : "更新任务状态", taskId, e);
        }
    }

    /**
     * 转换为proto数据结构
     *
     * @param workflowCallBackContent
     * @return
     */
    private ConductorTaskResult.TaskResult toProto(WorkflowCallBackContent workflowCallBackContent) {
        ConductorTaskResult.TaskResult.Builder builder = ConductorTaskResult.TaskResult.newBuilder();
        String taskId = workflowCallBackContent.getTransmissionParams() == null ? "" : workflowCallBackContent.getTransmissionParams().getString("taskId");
        if (StrUtil.isNotBlank(taskId)) {
            builder.setTaskId(taskId);
        }
        String workflowInstanceId = workflowCallBackContent.getTransmissionParams() == null ? "" : workflowCallBackContent.getTransmissionParams().getString("workflowInstanceId");
        if (StrUtil.isNotBlank(workflowInstanceId)) {
            builder.setWorkflowInstanceId(workflowInstanceId);
        }
        WorkflowCallBackContentItem taskResult = workflowCallBackContent.getTaskResult();
        if (null != taskResult) {
            if (null != taskResult.getStatus()) {
                builder.setStatus(ConductorTaskResult.TaskResult.Status.valueOf(taskResult.getStatus().name()));
            }
            if (StrUtil.isNotBlank(taskResult.getWorkerId())) {
                builder.setWorkerId(taskResult.getWorkerId());
            }

            Map<String, Object> outputDataAsMap = taskResult.getOutputDataAsMap();
            if (null != outputDataAsMap) {
                for (Map.Entry<String, Object> pair : outputDataAsMap.entrySet()) {
                    builder.putOutputData(pair.getKey(), toProto(pair.getValue()));
                }
            }
            if (CollectionUtil.isNotEmpty(taskResult.getLogs())) {
                taskResult.getLogs().forEach(taskExecLog -> builder.addLogs(toProto(taskExecLog, taskId)));
            }
            if (StrUtil.isNotBlank(taskResult.getReasonForIncompletion())) {
                builder.setReasonForIncompletion(taskResult.getReasonForIncompletion());
            }
        }

//        builder.seo(taskResult.getOutputData());
//        if (taskResult.getStartTime() != null && taskResult.getStartTime() > 0) {
//            builder.setStartTime(taskResult.getStartTime());
//        }
        return builder.build();
    }


    /**
     * 批量更新
     *
     * @param workflowCallBackContentList
     */
    public void updateTaskList(List<WorkflowCallBackContent> workflowCallBackContentList) {
        for (WorkflowCallBackContent workflowCallBackContent : workflowCallBackContentList) {
            updateTask(true, null, workflowCallBackContent);
        }
    }


    /**
     * 转换为proto数据结构
     *
     * @param val
     * @return
     */
    public Value toProto(Object val) {
        Value.Builder builder = Value.newBuilder();

        if (val == null) {
            builder.setNullValue(NullValue.NULL_VALUE);
        } else if (val instanceof Boolean) {
            builder.setBoolValue((Boolean) val);
        } else if (val instanceof Double) {
            builder.setNumberValue((Double) val);
        } else if (val instanceof Long) {
            builder.setLongValue((Long) val);
        } else if (val instanceof Integer) {
            builder.setIntValue((Integer) val);
        } else if (val instanceof Float) {
            builder.setFloatValue((Float) val);
        } else if (val instanceof Number) {
            Number number = (Number) val;
            builder.setNumberValue(number.doubleValue());
        } else if (val instanceof String) {
            builder.setStringValue((String) val);
        } else if (val instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) val;
            Struct.Builder struct = Struct.newBuilder();
            for (Map.Entry<String, Object> pair : map.entrySet()) {
                struct.putFields(pair.getKey(), toProto(pair.getValue()));
            }
            builder.setStructValue(struct.build());
        } else if (val instanceof List) {
            ListValue.Builder list = ListValue.newBuilder();
            for (Object obj : (List<Object>) val) {
                list.addValues(toProto(obj));
            }
            builder.setListValue(list.build());
        } else {
            builder.setStringValue(val.toString());
            log.warn("cannot map to Value type: {}", val);
//            throw new ClassCastException("cannot map to Value type: " + val);
        }
        return builder.build();
    }


    /**
     * 转换为proto数据结构
     *
     * @param from
     * @param taskId
     * @return
     */
    public ConductorTaskExecLog.TaskExecLog toProto(WorkflowTaskExecLog from, String taskId) {
        ConductorTaskExecLog.TaskExecLog.Builder to = ConductorTaskExecLog.TaskExecLog.newBuilder();
        if (from.getLog() != null) {
            to.setLog(from.getLog());
        }

        to.setTaskId(taskId);

        to.setCreatedTime(from.getCreatedTime());
        return to.build();
    }
}
