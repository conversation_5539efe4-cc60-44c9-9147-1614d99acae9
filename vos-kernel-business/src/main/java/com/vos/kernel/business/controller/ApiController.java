package com.vos.kernel.business.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.linker.basic.aop.FunctionPoint;
import com.linker.basic.baseclass.BaseResp;
import com.linker.basic.constants.RespCodeEnum;
import com.linker.basic.enums.InterfaceType;
import com.linker.basic.exception.BusinessException;
import com.linker.omos.client.domain.LineCountMessageQueueDTO;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.config.RefluxProperties;
import com.vos.kernel.business.constants.AbilityEnum;
import com.vos.kernel.business.conver.AiConfigConvertMapper;
import com.vos.kernel.business.conver.ApiConvertMapper;
import com.vos.kernel.business.conver.VideoControlConvertMapper;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.entity.vo.OmHubLoginVO;
import com.vos.kernel.business.exception.V2BusinessException;
import com.vos.kernel.business.exception.V2RespCodeEnum;
import com.vos.kernel.business.mapper.TaskTypeMapper;
import com.vos.kernel.business.params.*;
import com.vos.kernel.business.params.AiConfigForBiz.AiConfigForBizDelete;
import com.vos.kernel.business.params.AiConfigForBiz.AiConfigForBizEdit;
import com.vos.kernel.business.params.AiConfigForBiz.AiConfigForBizGet;
import com.vos.kernel.business.params.AiConfigForBiz.AiConfigForBizSave;
import com.vos.kernel.business.params.ComparisonSample.ComparisonSampleCreate;
import com.vos.kernel.business.params.ComparisonSample.ComparisonSampleDelete;
import com.vos.kernel.business.params.ComparisonSample.ComparisonSampleGet;
import com.vos.kernel.business.params.ComparisonSample.ComparisonSampleUpdate;
import com.vos.kernel.business.params.dataInfoVideo.DataInfoVideoAddParam;
import com.vos.kernel.business.params.dataInfoVideo.DataInfoVideoUpdateParam;
import com.vos.kernel.business.params.task.TaskListParam;
import com.vos.kernel.business.params.task.VideoTaskAddParam;
import com.vos.kernel.business.service.*;
import com.vos.kernel.business.utils.AuthorizationUtils;
import com.vos.kernel.business.vo.BasePaginRespVo;
import com.vos.kernel.business.vo.TaskAbilityTrendStatVO;
import com.vos.kernel.business.vo.TypeInventedSub;
import com.vos.kernel.business.vo.external.*;
import com.vos.kernel.common.LinkerStorageService;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.entities.InitMultiPartReq;
import com.vos.kernel.common.entities.InitMultiPartUploadResp;
import com.vos.kernel.common.entities.MultiUploadMergeReq;
import com.vos.kernel.common.entities.MultiUploadMergeResp;
import com.vos.kernel.common.entity.OmInfoGetDTO;
import com.vos.kernel.common.entity.OmInfoUpdateDTO;
import com.vos.kernel.common.entity.TenantStatisticsDTO;
import com.vos.kernel.common.property.LinkerStorageProperties;
import com.vos.kernel.common.utils.BeanCopyUtil;
import com.vos.kernel.common.utils.StringUtils;
import com.vos.kernel.core.api.domain.*;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.entity.constants.RedisKey;
import com.vos.kernel.core.api.exception.AppManageException;
import com.vos.kernel.core.api.rpc.IAiExecutionRpcService;
import com.vos.kernel.core.api.rpc.ICallBackRpcService;
import com.vos.kernel.core.api.rpc.IVideoStreamControlRpcService;
import com.vos.kernel.core.api.vo.*;
import com.vos.kernel.core.api.vo.ComparisonTUser.ComparisonTUserCreate;
import com.vos.kernel.core.api.vo.ComparisonTUser.ComparisonTUserUpdate;
import com.vos.kernel.data.api.model.entity.DataInfoVideo;
import com.vos.kernel.data.api.model.entity.Task;
import com.vos.kernel.data.api.model.entity.VideoTaskSetting;
import com.vos.kernel.data.api.model.enumPackage.CompanyEnum;
import com.vos.kernel.data.api.model.req.SaveBatchLocalVideoReq;
import com.vos.kernel.data.api.model.req.SaveInvented;
import com.vos.kernel.data.api.model.req.StreamUrlReq;
import com.vos.kernel.data.api.model.vo.VideoTaskVo;
import com.vos.task.automated.api.model.dto.*;
import com.vos.task.automated.api.model.dto.k8s.StatisticResourceItemDTO;
import com.vos.task.automated.api.service.rpc.IStatisticRpcService;
import com.vos.task.manage.api.rpc.ILocalTaskQueueInfoRpcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14
 * @description: api配置表 输出管理控制类
 */
@Slf4j
@Validated
@RestController
@Api(value = "对外接口相关")
public class ApiController {

    @Resource
    private IApiService apiService;
    @Autowired
    private TUserManagerService tUserManagerService;
    @Autowired
    ITaskService iTaskService;

    @DubboReference
    IStatisticRpcService statisticRpcService;

    @Resource
    ApiConvertMapper apiConvertMapper;

    @Resource
    private TTaskTypeManagerService taskTypeService;

    @Resource
    private IDataInfoVideoService iDataInfoVideoService;

    @Resource
    IAiConfigManagerService aiConfigManagerService;

    @Resource
    UploadService uploadService;

    @Resource
    ISampIeLibraryService iSampIeLibraryService;
    @Resource
    ISampleLibraryFeedbackService iSampleLibraryFeedbackService;

    @DubboReference
    private ICallBackRpcService callbackService;

    @DubboReference
    private IVideoStreamControlRpcService controlService;

    @Autowired
    private AppManageManagerService appManageService;

    @Autowired
    private TUserManagerService tUserAuthenticationManagerService;

    @Autowired
    private AuthenticationManagerService authenticationManagerService;

    @Resource
    private VideoControlConvertMapper videoControlConvertMapper;

    @Resource
    AiConfigConvertMapper aiConfigConvertMapper;

    @DubboReference
    IAiExecutionRpcService aiExecutionRpcService;

    @DubboReference
    private ILocalTaskQueueInfoRpcService localTaskQueueInfoRpcService;

    @Resource
    private TbApiManagerService tbApiManagerService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    LinkerStorageService linkerStorageService;

    @Resource
    private DynamicTemplateDictService dynamicTemplateDictService;

    @Resource
    TaskTypeMapper taskTypeMapper;

    @Resource
    ICacheService cacheService;

    @Resource
    RefluxProperties refluxProperties;

    @Resource
    LinkerStorageProperties linkerStorageProperties;

    @PostMapping("/v1/query/token/get")
    @ApiOperation(value = "获取token-对外")
    public Object getToken(@RequestBody JSONObject params) {
        return tUserManagerService.getToken(params);
    }

    @PostMapping("/v1/query/token/hub")
    @ApiOperation(value = "获取token-对外-对接hub")
    public BaseResp<OmHubLoginVO> getHubToken(@Validated @RequestBody OmHubLoginQueryDTO params) {
        return new BaseResp<>(tUserManagerService.getHubToken(params));
    }

    @PostMapping("/v1/query/hub/cdc")
    @ApiOperation(value = "对接hub同步数据，hub激活同步租户信息到os")
    public BaseResp<Boolean> syncHubEnterprise(@RequestBody HubEnterpriseSyncDTO params) {
        log.info("hub同步租户数据：{}", JSON.toJSONString(params));
        return new BaseResp<>(tUserManagerService.syncHubEnterprise(params));
    }

    @Token
    @GetMapping("/v1/query/token/change")
    @ApiOperation(value = "切换租户-对外-对接hub")
    public BaseResp<OmHubLoginVO> getHubChange(@Validated @RequestParam("tenantId") String tenantId, @Validated @RequestParam("token") String token) {
        if (StrUtil.isBlank(tenantId)) {
            throw new BusinessException("租户ID不能为空");
        }
        if (StrUtil.isBlank(token)) {
            throw new BusinessException("token不能为空");
        }
        return new BaseResp<>(tUserManagerService.changeHubTenant(tenantId, token));
    }

    //    @Token
    @PostMapping("/v1/query/hub/info")
    @ApiOperation(value = "获取用户信息-对外-对接hub")
    public BaseResp<OmHubFreshDTO> getHubTokenInfo(@Validated @RequestBody HubInfoGetRequest hubInfoGetRequest) {
        String token = hubInfoGetRequest.getToken();
        String tenantId = hubInfoGetRequest.getTenantId();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException("token不能为空");
        }
        if (StrUtil.isBlank(tenantId)) {
            throw new BusinessException("租户ID不能为空");
        }
        OmHubFreshDTO hubTenantMenuInfo = tUserManagerService.getHubTenantMenuInfo(token, tenantId);
        if (StrUtil.isNotBlank(DubboDataContext.getMessageHolder())) {
            hubTenantMenuInfo.setMessage(DubboDataContext.getMessageHolder());
        }
        return new BaseResp<>(hubTenantMenuInfo);
    }

    @Token
    @GetMapping("/v1/query/hub/akSk")
    @ApiOperation(value = "获取用户信息-对外-对接hub")
    public BaseResp<OmHubAkSkDTO> getHubAkSkInfo() {
        String appKey = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserManagerService.getUserInfo(appKey);
        OmHubAkSkDTO omHubAkSk = new OmHubAkSkDTO();
        omHubAkSk.setAppKey(tUserAuthentication.getAppKey());
        omHubAkSk.setAppSecret(tUserAuthentication.getAppSecret());
        return new BaseResp<>(omHubAkSk);
    }

    @GetMapping("/v1/query/hub/machineCode")
    @ApiOperation(value = "获取机器码-对接hub")
    public BaseResp<String> getHubMachineCode() {

        return new BaseResp<>(AuthorizationUtils.getMachineCode(null));
    }

    @GetMapping("/v1/query/hub/license")
    @ApiOperation(value = "授权信息")
    public BaseResp<OmHubLicenceGetDTO> getLicense() {
        return new BaseResp<>(authenticationManagerService.getLicenceInfo());
    }

    /**
     * 离线授权文件
     *
     * @param multipartFile
     * @return
     */
    @PostMapping("/service/cms/uploadLicenseFile")
    public BaseResp inlineLicenseFile(@NotNull(message = "文件不可为空") @RequestParam(value = "file") MultipartFile multipartFile) {
        UploadFileVo uploadFileVo = new UploadFileVo();
        uploadFileVo.setFile(multipartFile);
        return new BaseResp<>(authenticationManagerService.cmsUploadLicenseFile(uploadFileVo));
    }


    @GetMapping("/v1/query/hub/statistics")
    @ApiOperation(value = "获取用户信息-对外-对接hub")
    public BaseResp<List<OmHubDayDTO>> getHubStatisticsInfo(@Validated @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
            throw new BusinessException("token不能为空");
        }
        LocalDateTime startDateT = LocalDateTimeUtil.parse(startDate, "yyyy-MM-dd");
        LocalDateTime endDateT = LocalDateTimeUtil.parse(endDate, "yyyy-MM-dd");
        long between = LocalDateTimeUtil.between(startDateT, endDateT, ChronoUnit.DAYS);
        if (between < 0) {
            throw new BusinessException("结束时间不能小于开始时间");
        }
        if (between > 31) {
            throw new BusinessException("查询间隔不能超过一个月");
        }
        return new BaseResp<>(tUserManagerService.getHubStatisticsInfo(startDate, endDate));
    }

    @Token
    @ApiOperation(value = "获取指定算法应用特征库信息")
    @PostMapping("/service/comparison/get")
    public BaseResp<List<ComparisonGroupRspDTO>> getComparisonSample(@Validated(ComparisonSampleGet.class) @RequestBody ComparisonSampleParams request) {
        return new BaseResp<List<ComparisonGroupRspDTO>>(apiService.getComparisonSample(request));
    }

    @Token
    @ApiOperation(value = "删除指定算法特征库")
    @PostMapping("/service/comparison/delete")
    public BaseResp<Boolean> deleteComparisonSample(@Validated(ComparisonSampleDelete.class) @RequestBody ComparisonSampleParams request) {
        return new BaseResp<Boolean>(apiService.deleteComparisonSample(request));
    }

    @Token
    @ApiOperation(value = "修改指定算法应用特征库")
    @PostMapping("/service/comparison/update")
    public BaseResp<ComparisonGroupRspDTO> updateComparisonSample(@Validated(ComparisonSampleUpdate.class) @RequestBody ComparisonSampleParams request) {
        return new BaseResp<ComparisonGroupRspDTO>(apiService.updateComparisonSample(request));
    }

    @Token
    @ApiOperation(value = "创建指定算法应用特征库")
    @PostMapping("/service/comparison/create")
    public BaseResp<ComparisonGroupRspDTO> createComparisonSample(@Validated(ComparisonSampleCreate.class) @RequestBody ComparisonSampleParams request) {
        return new BaseResp<ComparisonGroupRspDTO>(apiService.createComparisonSample(request));
    }

    @Token
    @ApiOperation(value = "添加样本图片")
    @PostMapping("/service/comparison/photo/create")
    public BaseResp<ComparisonGroupRspDTO> comparisonPhotoCreate(@Validated(ComparisonSampleUpdate.class) @RequestBody ComparisonSampleParams request) {
        return new BaseResp<ComparisonGroupRspDTO>(apiService.comparisonPhotoCreate(request));
    }


    @Token
    @GetMapping("/service/api/app/list")
    @ApiOperation(value = "应用管理列表")
    public BasePaginRespVo<AppManageListRespVo> appList(@RequestParam(required = false, defaultValue = "1", value = "page") Integer page,
                                                        @RequestParam(required = false, defaultValue = "10", value = "pageSize") Integer pageSize,
                                                        @RequestParam(required = false, value = "searchText") String searchText,
                                                        @RequestParam(required = false, value = "appStatus") Integer appStatus,
                                                        @RequestParam(required = false, value = "actionId") String actionId,
                                                        @RequestParam(required = false, value = "abilityType") String abilityType,
                                                        @RequestParam(required = false, value = "orderType") String orderType) {

        return taskTypeService.appList(page, pageSize, searchText, appStatus, actionId, orderType, abilityType);
    }

    @Token
    @GetMapping("/service/api/app/detail")
    @ApiOperation(value = "应用详情")
    public BaseResp<AbilityDetailInfoDTO> appDetail(@RequestParam(value = "actionId") String actionId) {
        return new BaseResp(taskTypeService.appDetail(actionId));
    }

    @Token
    @GetMapping("/service/api/deviceList")
    @ApiOperation(value = "设备列表查询")
    public BasePaginRespVo<DataInfoVideoExternal> deviceList(@RequestParam(required = false, defaultValue = "1", value = "page") Integer pageStart, @RequestParam(required = false, defaultValue = "10", value = "pageSize") Integer pageSize, @RequestParam(required = false, defaultValue = "", value = "content") String content, @RequestParam(required = false, defaultValue = "", value = "company") String company, @RequestParam(required = false, defaultValue = "", value = "status") Integer status, @RequestParam(required = false, defaultValue = "", value = "isPage") Integer isPage, @RequestParam(required = false, defaultValue = "", value = "companyType") String companyType) {
        DeviceListParam deviceListParam = new DeviceListParam();
        deviceListParam.setIsPage(isPage);
        deviceListParam.setStatus(status);
        deviceListParam.setCompany(company);
        deviceListParam.setContent(content);
        deviceListParam.setPage(pageStart);
        deviceListParam.setSize(pageSize);
        deviceListParam.setCompanyType(companyType);
        PageInfo<DataInfoVideo> dataInfoVideoIPage = iDataInfoVideoService.deviceList(deviceListParam);

        List<DataInfoVideoExternal> list = new ArrayList<>();

        BasePaginRespVo<DataInfoVideoExternal> iPage = new BasePaginRespVo<>(null, null);
        if (ObjectUtil.isNotEmpty(dataInfoVideoIPage)) {
            PageInfo<DataInfoVideo> dataInfoVideoPageInfo = dataInfoVideoIPage;
            List<DataInfoVideo> dataInfoVideoList = dataInfoVideoPageInfo.getList();

            if (CollectionUtil.isNotEmpty(dataInfoVideoList)) {
                list = dataInfoVideoList.stream().map(tbDataInfoVideo -> new DataInfoVideoExternal(tbDataInfoVideo.getSetName(), tbDataInfoVideo.getVideoCode(), tbDataInfoVideo.getDeviceStatus(), tbDataInfoVideo.getCompany(), tbDataInfoVideo.getCompanyType())).collect(Collectors.toList());
            }
            iPage.setData(list);
            iPage.setTotalPage((long) dataInfoVideoPageInfo.getPages());
            iPage.setTotal(dataInfoVideoPageInfo.getTotal());
            return iPage;
        }
        return iPage;
    }

    @Token
    @PostMapping("/service/api/stream/deviceAdd")
    public BaseResp deviceAdd(@RequestBody StreamUrlReq streamUrlReq) {

        DataInfoVideoAddParam param = new DataInfoVideoAddParam();
        param.setCompany(CompanyEnum.STREAM_URL.getCode());
        param.setStreamUrlReq(streamUrlReq);
        BaseResp<DataInfoVideo> baseResp = iDataInfoVideoService.deviceAdd(param);
        DataInfoVideo dataInfoVideo = baseResp.getData();
        if (ObjectUtil.isNotEmpty(dataInfoVideo)) {
            return new BaseResp(dataInfoVideo.getVideoCode());
        }
        return new BaseResp("50", "添加失败");
    }

    @Token
    @PostMapping("/service/api/local/deviceAdd")
    public BaseResp deviceAdd(@RequestBody LocalVideoReqExternal localVideoReqExternal) {

        List<String> videoCodeList = new ArrayList<>();
        localVideoReqExternal.getFiles().forEach(localFile -> {
            SaveBatchLocalVideoReq localVideoReq = new SaveBatchLocalVideoReq();

            List<String> ossUrl = new ArrayList<>();
            ossUrl.add(uploadService.uploadFileNetUrl(localFile.getFileUrl()));

            List<String> localName = new ArrayList<>();
            localName.add(localFile.getLocalName());

            localVideoReq.setLocalName(localName);
            localVideoReq.setOutOss(ossUrl);
            localVideoReq.setInOss(ossUrl);

            DataInfoVideoAddParam param = new DataInfoVideoAddParam();
            param.setCompany(CompanyEnum.LOCAL.getCode());
            param.setLocalVideoReq(localVideoReq);
            BaseResp<List<DataInfoVideo>> baseResp = iDataInfoVideoService.deviceAdd(param);
            DataInfoVideo dataInfoVideo = baseResp.getData().get(0);
            videoCodeList.add(dataInfoVideo.getVideoCode());
        });
        if (ObjectUtil.isNotEmpty(videoCodeList)) {
            return new BaseResp(StringUtils.join(videoCodeList, ","));
        }
        return new BaseResp("50", "添加失败");
    }

    @Token
    @PostMapping("/service/api/deviceUpdate")
    public BaseResp deviceUpdate(@RequestBody DataInfoVideoUpdateParam dataInfoVideoUpdateParam) {
        return new BaseResp(iDataInfoVideoService.updateDate(dataInfoVideoUpdateParam));
    }

    @Token
    @GetMapping("/service/api/deviceDelete")
    @ApiOperation(value = "设备删除")
    public BaseResp deviceDelete(@RequestParam(required = true) String videoCode) {
        iDataInfoVideoService.deleteData(videoCode);
        return new BaseResp();
    }

    @Token
    @ApiOperation("获取任务列表")
    @GetMapping("/service/api/taskList")
    public BasePaginRespVo<TaskExternal> taskList(@RequestParam(required = false, defaultValue = "true", value = "isPage") Boolean isPage, @RequestParam(required = false, defaultValue = "1", value = "page") Integer pageStart, @RequestParam(required = false, defaultValue = "10", value = "pageSize") Integer pageSize) {
        TaskListParam taskListParam = new TaskListParam();
        taskListParam.setIsPage(isPage);
        taskListParam.setPage(pageStart);
        taskListParam.setPageSize(pageSize);
        BaseResp baseResp = iTaskService.taskList(taskListParam);

        BasePaginRespVo<TaskExternal> iPage = new BasePaginRespVo<>(null, null);

        if (ObjectUtil.isNotEmpty(baseResp.getData())) {
            IPage<Task> listVoPageInfo = (IPage<Task>) baseResp.getData();
            List<Task> records = listVoPageInfo.getRecords();
            List<TaskExternal> list = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(records)) {
                list = records.stream().map(taskListVo -> new TaskExternal(taskListVo.getTaskCode(), taskListVo.getName(), String.valueOf(taskListVo.getStatus()), taskListVo.getEnable(), taskListVo.getErrorMsg(), taskListVo.getHashConsume())).collect(Collectors.toList());
            }
            iPage.setData(list);
            iPage.setTotalPage((long) listVoPageInfo.getPages());
            iPage.setTotal(listVoPageInfo.getTotal());
            return iPage;
        }
        return iPage;
    }

    @Token
    @PostMapping("/service/api/taskAdd")
    @ApiOperation("任务添加")
    public BaseResp saveVideoTask(@RequestBody VideoTaskAddParam param) {
        Task task = param.getTask();
        task.setIsVideoCut(0);
        task.setIsAiSpeed(0);
        task.setCheckFlag(0);
        task.setPatrolInterval(0);
        task.setPatrolVideoNum(0);
        task.setTellErrInterval(0);
        param.setTask(task);

        VideoTaskSetting taskSetting = param.getTaskSetting();
        taskSetting.setType(1);
        taskSetting.setVideoIds(taskSetting.getVideoCodes());
        param.setTaskSetting(taskSetting);
        BaseResp<VideoTaskVo> baseResp = iTaskService.saveVideoTask(param, true);
        JSONObject result = new JSONObject();
        result.put("taskCode", baseResp.getData().getTask().getTaskCode());
        return new BaseResp(result);
    }

    /**
     * 删除api
     */
    @FunctionPoint(interfaceType = InterfaceType.API)
    @PostMapping("/api/editGrain")
    public BaseResp editGrain(@RequestBody TbApiGrainEntity tbApiGrain) {
        apiService.editGrain(tbApiGrain);
        return new BaseResp(tbApiGrain);
    }

    @Token
    @PostMapping("/service/api/taskUpdate")
    @ApiOperation("任务修改")
    public BaseResp updateVideoTask(@RequestBody VideoTaskAddParam param) {
        Task paramTask = param.getTask();
        paramTask.setIsVideoCut(0);
        paramTask.setIsAiSpeed(0);
        paramTask.setCheckFlag(0);
        paramTask.setPatrolInterval(0);
        paramTask.setPatrolVideoNum(0);
        paramTask.setTellErrInterval(0);
        param.setTask(paramTask);

        VideoTaskSetting taskSetting = param.getTaskSetting();
        taskSetting.setType(1);
        taskSetting.setVideoIds(taskSetting.getVideoCodes());
        param.setTaskSetting(taskSetting);
        iTaskService.updateVideoTask(param);
        return new BaseResp();
    }

    @Token
    @GetMapping("/service/api/taskDelete")
    @ApiOperation("任务删除")
    public BaseResp deleteTask(@RequestParam(required = false, defaultValue = "", value = "taskCode") String taskCode) {
        return iTaskService.deleteTask(taskCode);
    }

    @Token
    @GetMapping("/service/api/taskEnable")
    @ApiOperation("任务启用禁用")
    public BaseResp enableTask(@RequestParam(required = false, defaultValue = "", value = "taskCode") String taskCode, @ApiParam("1开启，0关闭") @RequestParam(required = false, value = "enable") Integer enable) {
        iTaskService.enableTask(taskCode, enable);
        return new BaseResp<>();
    }

    @Token
    @GetMapping("/service/api/taskInfo")
    @ApiOperation("任务详情")
    public BaseResp<VideoTaskVoExternal> taskInfo(@RequestParam(required = true, value = "taskCode") String taskCode) {
        BaseResp<VideoTaskVo> baseResp = iTaskService.getTaskInfo(taskCode);
        VideoTaskVo vo = baseResp.getData();
        VideoTaskVoExternal videoTaskVoExternal = new VideoTaskVoExternal();
        List<TTaskSubExternal> tTaskSubExternals = BeanCopyUtil.copyList(vo.getSubList(), TTaskSubExternal.class);
        List<TbVideoTaskTimeExternal> tbVideoTaskTimeExternals = BeanCopyUtil.copyList(vo.getTaskTimeList(), TbVideoTaskTimeExternal.class);
        TbVideoTaskSettingExternal tbVideoTaskSettingExternal = new TbVideoTaskSettingExternal();

        BeanUtil.copyProperties(vo.getTaskSetting(), tbVideoTaskSettingExternal);
        TaskExternal taskExternal = new TaskExternal(vo.getTask().getTaskCode(), vo.getTask().getName(), String.valueOf(vo.getTask().getStatus()), vo.getTask().getEnable(), vo.getTask().getErrorMsg(), vo.getTask().getHashConsume());
        videoTaskVoExternal.setTask(taskExternal);
        videoTaskVoExternal.setSubList(tTaskSubExternals);
        videoTaskVoExternal.setTaskTimeList(tbVideoTaskTimeExternals);
        videoTaskVoExternal.setTaskSetting(tbVideoTaskSettingExternal);
        List<DataInfoVideoExternal> dataInfoVideoExternals = new ArrayList<>();
        vo.getVideoList().forEach(tbDataInfoVideo -> {
            DataInfoVideoExternal dataInfoVideoExternal = new DataInfoVideoExternal(tbDataInfoVideo.getSetName(), tbDataInfoVideo.getVideoCode(), tbDataInfoVideo.getDeviceStatus(), tbDataInfoVideo.getCompany(), tbDataInfoVideo.getCompanyType());
            dataInfoVideoExternals.add(dataInfoVideoExternal);
        });
        videoTaskVoExternal.setVideoList(dataInfoVideoExternals);
        return new BaseResp<>(videoTaskVoExternal);
    }

    /**
     * @description: 查询样本库详情(不分页)
     * @param:
     * @return:
     */
    @Token
    @PostMapping(value = "/service/sampleLibrary/listTSampleFeedback")
    public BaseResp listTSampleFeedback(@RequestBody SampleLibraryPhotoFeedbackListDTO sampleLibraryPhotoFeedbackListDTO) {
        PageInfo<SampleLibraryFeedbackPhoto> page = iSampleLibraryFeedbackService.listTSampleFeedback(sampleLibraryPhotoFeedbackListDTO);
        List<SampleLibraryFeedbackPhotoVO> voList = BeanCopyUtil.copyList(page.getList(), SampleLibraryFeedbackPhotoVO.class);
        BasePaginRespVo<SampleLibraryFeedbackPhotoVO> paginRespVo = new BasePaginRespVo<>(page.getTotal(), voList);
        paginRespVo.setTotalPage(Long.valueOf(page.getPages()));
        return paginRespVo;
    }

    /**
     * @description: 删除样本
     * @param:
     * @return:
     */
    @Token
    @PostMapping(value = "/service/sampleLibraryFeedback/deleteList")
    public BaseResp deleteList(@RequestBody SampleLibraryPhotoFeedbackListDTO sampleLibraryPhotoFeedbackListDTO) {
        iSampleLibraryFeedbackService.deleteList(sampleLibraryPhotoFeedbackListDTO.getFeedbackPhotoIdList());
        return new BaseResp();
    }

    /**
     * 循环学习模型-反馈样本库-编辑(点赞点踩)
     *
     * @return
     */
    @Token
    @PostMapping("/service/sampleLibrary/goodOrBad")
    public BaseResp<Boolean> goodOrBad(@Validated @RequestBody SampleLibraryFeedbackPhotoSaveParam saveParam) {
        if (CollectionUtil.isEmpty(saveParam.getPhotoCoordinateDTOList())) {
            throw new BusinessException("参数不合法,画框信息不能为空");
        }
        return new BaseResp<>(iSampIeLibraryService.goodOrBad(saveParam));
    }

    @Token
    @PostMapping("/service/task/identifyImages")
    @ApiOperation("视网识图接口")
    public BaseResp identifyImages(@Validated @RequestBody IdentifyImagesDTO identifyImagesDTO) {
        return new BaseResp(iTaskService.identifyImages(identifyImagesDTO));
    }

    @Token
    @ApiOperation(value = "AI效果查看")
    @PostMapping("/service/sendToAi")
    public BaseResp<AiCallFinalReturnDTO> sendToAi(@RequestBody @Valid AiAbilityCallParams params) {
        log.info("AI效果查看，参数：{}", JSON.toJSONString(params));
        if (params.getAbilityConfig() != null && params.getAbilityConfig().getSelfLearnConfig() != null) {
            //自我学习参数处理
            SelfLearningConfigParams selfLearnConfig = params.getAbilityConfig().getSelfLearnConfig();
            Integer studyType = selfLearnConfig.getStudyType();
            if (studyType != null && studyType != 1) {
                AbilityOlProcessParams abilityOlProcessParams = new AbilityOlProcessParams();
                abilityOlProcessParams.setStudyType(studyType);
                switch (studyType) {
                    case 2:
                        abilityOlProcessParams.setFilterMethod("image");
                        abilityOlProcessParams.setImageId(selfLearnConfig.getSampleList());
                        break;
                    case 3:
                        abilityOlProcessParams.setFilterMethod("video");
                        abilityOlProcessParams.setVideoId(selfLearnConfig.getSampleList());
                        break;
                    case 4:
                        abilityOlProcessParams.setFilterMethod("video");
                        abilityOlProcessParams.setVideoId(selfLearnConfig.getVideoCodeList());
                        break;
                    case 5:
                        abilityOlProcessParams.setFilterMethod("all");
                        break;
                    default:
                        throw new IllegalStateException("Unexpected value: " + studyType);
                }
                params.getAbilityConfig().setOlProcessConfig(abilityOlProcessParams);
            }
        }
        AiPreViewCallDTO aiPreViewCall = aiConfigConvertMapper.sendToAiParamsToDto(params);

        String authIdHolder = DubboDataContext.getAuthIdHolder();
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        if (StrUtil.isNotBlank(params.getAppKey())) {
            TUserAuthentication tUserAuthentication = tUserAuthenticationManagerService.getUserInfo(params.getAppKey());
            if (null == tUserAuthentication) {
                throw new BusinessException("参数错误，appKey缺失");
            }
            authIdHolder = tUserAuthentication.getAuthenticationId();
            appIdHolder = tUserAuthentication.getAppId();
        } else {
            if (StrUtil.isBlank(authIdHolder)) {
                throw new BusinessException("参数错误，appKey缺失");
            }
        }
        aiPreViewCall.setAppId(appIdHolder);
        aiPreViewCall.setOperator(authIdHolder);
        AiCallFinalReturnDTO aiConfig = aiExecutionRpcService.executeAiOnce(aiPreViewCall);
        return new BaseResp(aiConfig);
    }

    @Token
    @ApiOperation("获取默认配置")
    @PostMapping("/service/config/origin/get")
    public BaseResp getAiConfigOriginGet(@RequestBody AiConfigGetParams params) {
        return new BaseResp(aiConfigManagerService.getAiConfigOriginGet(params));
    }

    @Token
    @ApiOperation("业务侧获取AI配置参数列表")
    @PostMapping("/service/config/biz/getList")
    public BaseResp getAiConfigForBizGetList(@RequestBody AiConfigForBizParams params) {
        Page<TaskTypeConfigVideoEntity> page = aiConfigManagerService.getAiConfigForBizGetList(params);
        BasePaginRespVo<TaskTypeConfigVideoEntity> basePaginRespVo = new BasePaginRespVo<>(page.getTotal(), page.getRecords());
        basePaginRespVo.setTotalPage(page.getPages());
        return basePaginRespVo;
    }

    @Token
    @ApiOperation("业务侧获取AI配置参数")
    @PostMapping("/service/config/biz/get")
    public BaseResp<AiConfigForBizParams> getAiConfigForBiz(@Validated(AiConfigForBizGet.class) @RequestBody AiConfigForBizParams params) {
        if (CollectionUtil.isEmpty(params.getVideoCodes())) {
            List<String> defaultVideo = new ArrayList<>();
            defaultVideo.add(CommonConstant.DEFAULT_VIDEO_CODE);
            params.setVideoCodes(defaultVideo);
        }
        AiConfigForBizVO aiConfigForBizVO = aiConfigManagerService.getAiConfigForBiz(params);
        if (ObjectUtil.isEmpty(aiConfigForBizVO.getAiConfig())) {
            return new BaseResp<>("50", "应用配置不存在");
        }
        return new BaseResp(aiConfigForBizVO);
    }

    @Token
    @ApiOperation("默认ai配置参数")
    @PostMapping("/service/config/origin/aiGet")
    public BaseResp<AbilityConfigForShowDTO> getOriginAiConfig(@RequestBody AiConfigForBizParams params) {
        if (CollectionUtil.isEmpty(params.getVideoCodes())) {
            List<String> defaultVideo = new ArrayList<>();
            defaultVideo.add(CommonConstant.DEFAULT_VIDEO_CODE);
            params.setVideoCodes(defaultVideo);
        }
        return new BaseResp<AbilityConfigForShowDTO>(aiConfigManagerService.getOriginAiConfig(params));
    }

    @Token
    @ApiOperation("业务侧设置AI配置参数")
    @PostMapping("/service/config/biz/save")
    public BaseResp<String> saveAiConfigForBiz(@Validated(AiConfigForBizSave.class) @RequestBody AiConfigForBizParams params) {
        if (CollectionUtil.isEmpty(params.getVideoCodes())) {
            List<String> defaultVideo = new ArrayList<>();
            defaultVideo.add(CommonConstant.DEFAULT_VIDEO_CODE);
            params.setVideoCodes(defaultVideo);
        }
        return new BaseResp<String>(aiConfigManagerService.saveAiConfigForBiz(params));
    }

    @Token
    @ApiOperation("业务侧修改AI配置参数")
    @PostMapping("/service/config/biz/edit")
    public BaseResp<Boolean> editAiConfigForBiz(@Validated(AiConfigForBizEdit.class) @RequestBody AiConfigForBizParams params) {
        if (CollectionUtil.isEmpty(params.getVideoCodes())) {
            List<String> defaultVideo = new ArrayList<>();
            defaultVideo.add(CommonConstant.DEFAULT_VIDEO_CODE);
            params.setVideoCodes(defaultVideo);
        }
        return new BaseResp<Boolean>(aiConfigManagerService.editAiConfigForBiz(params));
    }

    @Token
    @ApiOperation("业务侧删除AI配置")
    @PostMapping("/service/config/biz/delete")
    public BaseResp<Boolean> deleteAiConfigForBiz(@Validated(AiConfigForBizDelete.class) @RequestBody AiConfigForBizParams params) {
        if (CollectionUtil.isEmpty(params.getVideoCodes())) {
            List<String> defaultVideo = new ArrayList<>();
            defaultVideo.add(CommonConstant.DEFAULT_VIDEO_CODE);
            params.setVideoCodes(defaultVideo);
        }
        return new BaseResp<Boolean>(aiConfigManagerService.deleteAiConfigForBiz(params));
    }

    @ApiOperation("视频流算法启停控制")
    @PostMapping("/service/task/videoAbilityTaskControl")
    public BaseResp videoAbilityTaskControl(@RequestBody AiVideoControlForBizParams params) {

        String orgCode = DubboDataContext.getAuthIdHolder();
        List<TypeInventedSub> taskTypeInventedVOList = taskTypeMapper.actionIdByCode(orgCode);
        Map<String, TypeInventedSub> map = taskTypeInventedVOList.stream().collect(Collectors.toMap(TypeInventedSub::getActionId, x -> x, (var1, var2) -> var1));

        TypeInventedSub taskTypeInventedVO = map.get(params.getActionId());
        params.setTaskTypeCode(taskTypeInventedVO.getTaskTypeCode());
        String errorMsg = controlService.videoStreamControl(videoControlConvertMapper.convert(params));
        return errorMsg != null ? new BaseResp(RespCodeEnum.BUSINESS_EXCEPTION.getCode(), errorMsg) : new BaseResp();
    }

    /**
     * @description: 视频流算法异步回调
     * @param: req
     * @return:
     */
    @RequestMapping(value = "/service/callback/videoStreamTask")
    public JSONObject videoStreamTaskCallBack(@RequestBody JSONObject req) {
        printWithLimit("视频流算法回调成功，req={}", JSON.toJSONString(req));
        req.put("create", false);
        callbackService.videoStreamCallback(req);
        return new JSONObject();
    }

    private void printWithLimit(String description, String data) {
        if (data.length() > 2000) {
            data = data.substring(0, 2000);
        }
        log.info(description, data);
    }

    /**
     * 上传.OM安装包到automated路径
     */

    @Token
    @ApiOperation("上传.OM安装包到automated路径")
    @PostMapping("/service/app/manage/uploadOMToAutomat")
    public BaseResp uploadOMToAutomat(@NotNull(message = "文件不可为空") @RequestParam(value = "file", required = false) MultipartFile omFile) {
        log.info("上传om包到webdav");
        return appManageService.uploadOMToAutomat(omFile);
    }

    /**
     * 在线部署上传.OM安装包到automated路径
     */

    @Token
    @ApiOperation("在线部署上传.OM安装包到automated路径")
    @PostMapping("/service/app/manage/uploadOMToOnline")
    public BaseResp uploadOMToOnline(@RequestBody OnlineInstallReqVO onlineInstallReqVO) {
        log.info("上传om包到webdav");
        return appManageService.uploadOMToOnline(onlineInstallReqVO);
    }

    /**
     * om包应用安装
     */

    @Token
    @ApiOperation("om包应用安装")
    @PostMapping("/service/app/manage/install")
    public BaseResp install(@RequestBody AppManageReqVO reqVO) {
        log.info("om包应用安装");
        return appManageService.install(reqVO);
    }


    /**
     * om包应用信息获取
     */

    @Token
    @ApiOperation("比对om包应用信息")
    @PostMapping("/service/app/manage/getOmInfo")
    public BaseResp getOmInfo(@RequestBody @Valid OmInfoGetDTO omInfoGet) {
        log.info("om包应用信息获取");
        return new BaseResp(appManageService.getOmInfo(omInfoGet));
    }

    /**
     * om包应用信息获取
     */

    @Token
    @ApiOperation("比对om包应用更新")
    @PostMapping("/service/app/manage/omInfoUpdate")
    public BaseResp omInfoUpdate(@RequestBody @Valid OmInfoUpdateDTO omInfo) {
        if (omInfo.getCustomStrategy() != null) {
            if (StrUtil.isBlank(omInfo.getCustomStrategy().getHost()) || StrUtil.isBlank(omInfo.getCustomStrategy().getGpuId())) {
                omInfo.setCustomStrategy(null);
            }
        }
        if (StrUtil.isNotBlank(omInfo.getGpuType())) {
            Set<String> clusterCudaEnumInfo = statisticRpcService.getClusterCudaEnumInfo();
            if (!clusterCudaEnumInfo.contains(omInfo.getGpuType())) {
                throw new BusinessException("请选择系统支持的gpuType");
            }
        }
        return new BaseResp(appManageService.omInfoUpdate(omInfo));
    }


    /**
     * 更新om包应用安装策略
     */

    @Token
    @ApiOperation("更新om包应用安装策略")
    @PostMapping("/service/app/manage/install/strategy")
    public BaseResp installStrategy(@RequestBody AbilityInstallStrategyRequest request) {
        log.info("更新om包应用安装策略，{}", JSON.toJSONString(request));
        if (StrUtil.isBlank(request.getTaskTypeCode())) {
            throw new BusinessException("模型标识不能为空");
        }
        if (request.getCustomStrategy() == null && StrUtil.isBlank(request.getAppName()) && StrUtil.isBlank(request.getGpuType())) {
            throw new BusinessException("模型安装策略不能为空");
        }

        if (StrUtil.isNotBlank(request.getGpuType())) {
            Set<String> clusterCudaEnumInfo = statisticRpcService.getClusterCudaEnumInfo();
            if (!clusterCudaEnumInfo.contains(request.getGpuType())) {
                throw new BusinessException("请选择系统支持的gpuType");
            }
        }
        if (request.getCustomStrategy() != null) {
            if (StrUtil.isBlank(request.getCustomStrategy().getHost()) || StrUtil.isBlank(request.getCustomStrategy().getGpuId())) {
                request.setCustomStrategy(null);
            }
        }

        return new BaseResp(taskTypeService.editeOmGpuStrategy(request));
    }

    /**
     * om包应用安装-V3大模型
     */

    @Token
    @ApiOperation("om包应用安装")
    @PostMapping("/service/app/manage/installV3")
    public BaseResp installV3() {
        log.info("om包应用安装");
        return appManageService.installV3();
    }

    /**
     * om包应用安装-V3大模型
     */

    @Token
    @ApiOperation("om包应用安装")
    @PostMapping("/service/app/manage/installV35")
    public BaseResp installV35(@RequestBody AppManageReqVO reqVO) {
        log.info("om包应用安装");
        return appManageService.installV35(reqVO);
    }

    /**
     * om包应用卸载
     */

    @Token
    @ApiOperation("om包应用卸载")
    @PostMapping("/service/app/manage/uninstall")
    public BaseResp uninstall(@RequestBody AppManageReqVO reqVO) {
        log.info("om包应用卸载");
        return appManageService.uninstall(reqVO);
    }


    /**
     * om包应用更新
     */

    @Token
    @ApiOperation("om包应用更新")
    @PostMapping("/service/app/manage/update")
    public BaseResp update(@RequestBody AppManageReqVO reqVO) {
        log.info("om包应用更新");
        return appManageService.update(reqVO);
    }

    /**
     * om应用启用禁用
     */

    @Token
    @ApiOperation("om应用启用禁用")
    @PostMapping("/service/app/manage/enable")
    public BaseResp enable(@RequestBody AppManageReqVO reqVO) {
        return appManageService.enable(reqVO);
    }

    /**
     * om应用便捷开关：控制算法pod
     */
    @Token
    @ApiOperation("om应用启用禁用")
    @PostMapping("/service/app/manage/switch")
    public BaseResp switchApp(@RequestBody AppManageSwitchDTO reqVO) {
        return new BaseResp(appManageService.switchApp(reqVO));
    }

    /**
     * 一键安装
     */
    @Token
    @ApiOperation("一键安装")
    @PostMapping("/service/app/manage/sync/install")
    public BaseResp sync(@RequestBody AbilityAddWithOutOmDTO abilityAdd) {
        Assert.notNull(abilityAdd.getAbilityId(), "能力ID不能为空");
        Assert.notNull(abilityAdd.getAbilityName(), "能力名称不能为空");
        Assert.notNull(abilityAdd.getServerUrl(), "能力url不能为空");
        Assert.notNull(abilityAdd.getIsMatchType(), "是否比对算法不能为空");
        abilityAdd.setAppKey(DubboDataContext.getAppKeyHolder());
        abilityAdd.setAuthenticationId(DubboDataContext.getAuthIdHolder());
        if (abilityAdd.getAbilityId().length() > 128) {
            throw new AppManageException("500", "能力ID长度不可大于128");
        }
        if (abilityAdd.getAbilityEnum() == 44 && StrUtil.isBlank(abilityAdd.getChatModelName())) {
            abilityAdd.setChatApiKey(abilityAdd.getAbilityId());
        }
        abilityAdd.setAbilityId(abilityAdd.getAbilityId() + "-" + DubboDataContext.getAppKeyHolder());

        //高级配置参数
        if (StrUtil.isNotBlank(abilityAdd.getApplicationJson())) {
            try {
                JSONArray.parseArray(abilityAdd.getApplicationJson(), JSONObject.class);
            } catch (Exception e) {
                throw new AppManageException("500", "高级配置参数不可解析");
            }
        }
        if (abilityAdd.getAbilityConcurrent() != 2) {
            abilityAdd.setMaxOperator(abilityAdd.getAbilityConcurrent());
        }
        return appManageService.syncInstall(abilityAdd);
    }

    /**
     * 注册算法信息更新
     */
    @Token
    @ApiOperation("注册算法信息更新")
    @PostMapping("/service/app/manage/sync/update")
    public BaseResp syncUpdate(@RequestBody @Valid RegisterAbilityUpdateRequest request) {
        if (StrUtil.isBlank(request.getAbilityName()) && StrUtil.isBlank(request.getAbilityRemark()) && null == request.getAbilityConcurrent()) {
            throw new AppManageException("500", "注册算法信息更新参数缺失");
        }
        TaskTypeEntity userAbility = cacheService.getUserAbility(DubboDataContext.getAuthIdHolder() + "#" + request.getActionId());
        if (null == userAbility) {
            throw new V2BusinessException(V2RespCodeEnum.UNAVAILABLE_EXCEPTION);
        }
        if (userAbility.getStatus() != 0) {
            throw new V2BusinessException(V2RespCodeEnum.BUSINESS_EXCEPTION);
        }
        //高级配置参数
        if (StrUtil.isNotBlank(request.getApplicationJson())) {
            try {
                JSONArray.parseArray(request.getApplicationJson(), JSONObject.class);
            } catch (Exception e) {
                throw new AppManageException("500", "高级配置参数不可解析");
            }
        }
        return new BaseResp(appManageService.syncInstallUpdate(request, userAbility));
    }

    /**
     * 一键安装
     */
    @Token
    @ApiOperation("一键安装地址连接校验")
    @GetMapping("/service/app/manage/sync/installCheck")
    public BaseResp syncCheck(@RequestParam(value = "url") String url) {
        if (StrUtil.isBlank(url)) {
            throw new BusinessException("地址不能为空");
        }
        try {
            URL uri = new URL(url);
            Socket socket = new Socket();
            InetSocketAddress address = new InetSocketAddress(uri.getHost(), uri.getPort() == -1 ? uri.getDefaultPort() : uri.getPort());
            socket.connect(address, 1000);
            socket.close();
            return new BaseResp(true);
        } catch (IOException e) {
            log.error("{},连接错误:", url);
            throw new BusinessException("地址不可用，连接出错");
        }
    }

    /**
     * 获取算法类型
     */

    @PostMapping("/service/app/manage/abilityEnum")
    public BaseResp abilityEnum() {
        return new BaseResp(AbilityEnum.buildShowInfo());
    }

    /**
     * om包应用在线安装
     */

    @Token
    @ApiOperation("om包应用在线安装")
    @PostMapping("/service/app/manage/onlineInstall")
    public BaseResp onlineInstall(@RequestBody AppManageReqVO reqVO) {
        log.info("om包应用安装");
        return appManageService.onlineInstall(reqVO);
    }

    /**
     * om应用离线激活
     */

    @Token
    @ApiOperation("om应用离线激活")
    @PostMapping("/service/app/manage/offlineActivation")
    public BaseResp offlineActivation(@RequestBody AppManageReqVO reqVO) {
        return appManageService.offlineActivation(reqVO);
    }

    /**
     * 新增租户-返回appkey及appSecret
     *
     * @return
     */
    @Token
    @PostMapping("/service/tUserAuthentication/add")
    public BaseResp addUser(@Validated(ComparisonTUserCreate.class) @RequestBody TUserAuthenticationVO tUserAuthenticationVO) {
        return tUserAuthenticationManagerService.addUser(tUserAuthenticationVO);
    }

    /**
     * 修改租户
     *
     * @return
     */
    @Token
    @PostMapping("/service/tUserAuthentication/edit")
    public BaseResp updateUser(@Validated(ComparisonTUserUpdate.class) @RequestBody TUserAuthenticationVO tUserAuthenticationVO) {
        return tUserAuthenticationManagerService.updateUser(tUserAuthenticationVO);
    }

    /**
     * 删除用户
     */
    @Token
    @PostMapping("/service/tUserAuthentication/remove")
    public BaseResp remove(@RequestBody TUserAuthenticationVO tUserAuthenticationVO) {
        return tUserAuthenticationManagerService.deleteUser(tUserAuthenticationVO);
    }

    /**
     * 获取本机注册码
     *
     * @return
     */
    @PostMapping("/service/authentication/getRegistrationCode")
    public BaseResp getRegistrationCode() {
        return new BaseResp(authenticationManagerService.getRegistrationCode());
    }

    /**
     * 上传授权文件
     * 1.上传授权文件至该系统的相对licens文件夹目录下
     * 2.判断文件后缀.lic 若不是则返回失败
     * 3.上传时先判断对应文件夹目录下使用已有文件，已有则进行删除
     *
     * @return
     */
    @PostMapping("/service/authentication/uploadLicenseFile")
    public BaseResp uploadLicenseFile(@NotNull(message = "文件不可为空") @RequestParam(value = "file") MultipartFile multipartFile) {

        UploadFileVo uploadFileVo = new UploadFileVo();

        uploadFileVo.setFile(multipartFile);

        BaseResp result = authenticationManagerService.uploadLicenseFile(uploadFileVo);

        if ("200".equals(result.getCode())) {
            stringRedisTemplate.opsForValue().set(RedisKey.AUTH_CODE, "true");
            log.info("开启任务下发");
        }
        return result;
    }

    /**
     * 在线激活
     * 1.调取OA授权系统获取licence
     * 2.上传授权文件至该系统的相对licens文件夹目录下
     * 3.上传时先判断对应文件夹目录下使用已有文件，已有则进行删除
     *
     * @return
     */
    @PostMapping("/service/authentication/onlineActivation")
    public BaseResp onlineActivation(@RequestBody AuthenticationVO authenticationVO) {
        BaseResp result = authenticationManagerService.onlineActivation(authenticationVO);
        if ("200".equals(result.getCode())) {
            stringRedisTemplate.opsForValue().set(RedisKey.AUTH_CODE, "true");
            log.info("开启任务下发");
        }
        return result;
    }

    /**
     * 获取授权解密信息
     *
     * @return
     */
    @GetMapping("/service/authentication/getDecryptData")
    public BaseResp getDecryptData() {
        long startTime = System.currentTimeMillis();
        BaseResp result = authenticationManagerService.getDecryptData();
        long endTime = System.currentTimeMillis();
        if (0 != Integer.valueOf(result.getCode())) {
            stringRedisTemplate.opsForValue().set(RedisKey.AUTH_CODE, "false");
            log.info("关闭任务下发");
        } else {
            stringRedisTemplate.opsForValue().set(RedisKey.AUTH_CODE, "true");
            log.info("开启任务下发");
        }
        log.info("获取授权解密信息接口完成，耗时：{}毫秒", (endTime - startTime));
        return result;
    }

    /**
     * 获取本机注册码
     *
     * @return
     */
    @GetMapping("/service/authentication/getEnterpriseCode")
    public BaseResp getEnterpriseCode(@RequestParam(required = false, value = "appKey") String appKey) {
        return new BaseResp(authenticationManagerService.getEnterpriseCode(appKey));
    }

    @Token
    @ApiOperation(value = "集群使用率统计")
    @PostMapping("/statistic/useInfo")
    public BaseResp<List<StatisticResourceItemDTO>> useInfo() {
        List<StatisticResourceItemDTO> clusterUsage = statisticRpcService.getClusterUsage();
        return new BaseResp<>(clusterUsage);
    }

    @Token
    @ApiOperation(value = "消息列表")
    @PostMapping("/statistic/alarmInfo")
    public BaseResp<List<AlarmMessageDTO>> alarmInfo() {
        List<AlarmMessageDTO> alarmMessage = statisticRpcService.getAlarmMessage(false);
        return new BaseResp<>(alarmMessage);
    }

    @Token
    @ApiOperation(value = "消息列表")
    @PostMapping("/statistic/alarmWarnInfo")
    public BaseResp<List<AlarmMessageDTO>> alarmWarnInfo() {
        List<AlarmMessageDTO> alarmMessage = statisticRpcService.getAlarmMessage(true);
        return new BaseResp<>(alarmMessage);
    }


    @Token
    @ApiOperation(value = "显卡资源列表")
    @PostMapping("/statistic/gpuInfo")
    public BaseResp<List<CudaInfoDTO>> gpuInfo() {
        List<CudaInfoDTO> clusterCudaInfo = statisticRpcService.getClusterCudaInfo();
        //添加算法最近调用频次
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries("API:CALL:LAST_CALL");
        if (MapUtil.isNotEmpty(entries) && CollectionUtil.isNotEmpty(clusterCudaInfo)) {
            for (CudaInfoDTO c : clusterCudaInfo) {
                List<AbilityPodGpuInfoDTO> abilityList = c.getAbilityList();
                if (CollectionUtil.isNotEmpty(abilityList)) {
                    for (AbilityPodGpuInfoDTO a : abilityList) {
                        if (entries.containsKey(a.getAbilityCode())) {
                            String callData = entries.get(a.getAbilityCode()).toString();
                            if (StrUtil.isNotBlank(callData)) {
                                List<String> split = StrUtil.split(callData, "|");
                                a.setLastCallTime(CollectionUtil.getLast(split));
                                a.setLastCallCount(Integer.parseInt(CollectionUtil.getFirst(split)));
                            }
                        }
                    }
                }
            }
        }
        return new BaseResp<>(clusterCudaInfo);
    }

    @Token
    @ApiOperation(value = "显卡资源类型")
    @PostMapping("/statistic/gpuEnum")
    public BaseResp gpuEnum() {
        return new BaseResp<>(statisticRpcService.getClusterCudaEnumInfo());
    }


    //    @Token
    @ApiOperation(value = "租户统计信息")
    @PostMapping("/statistic/tenantUseInfo")
    public BaseResp<TenantStatisticsDTO> tenantUseInfo(@RequestParam(required = true, value = "appKey") String appKey) {
        if (StrUtil.isBlank(appKey)) {
            throw new BusinessException("appKey缺失");
        }
        TUserAuthentication userInfo = tUserManagerService.getUserInfo(appKey);
        if (null == userInfo) {
            throw new BusinessException("appKey错误");
        }
        return new BaseResp<>(tUserManagerService.getTenantUseInfo(userInfo.getAuthenticationId()));
    }


    @Token
    @ApiOperation(value = "显卡主机资源列表")
    @PostMapping("/statistic/gpuHostInfo")
    public BaseResp<List<CudaResourceInfoDTO>> gpuResourceInfo() {

        return new BaseResp<>(statisticRpcService.getClusterCudaResourceInfo());
    }

    @Token
    @ApiOperation(value = "应用资源列表")
    @PostMapping("/statistic/abilityInfo")
    public BaseResp<Page<AbilityResourceItemDTO>> abilityInfo(@RequestBody AbilityResourceParams params) {
        AbilityResourcePageDTO query = apiConvertMapper.paramsToRpcDto(params);
        query.setAppKey(DubboDataContext.getAppKeyHolder());
        Page<AbilityResourceItemDTO> abilityResource = statisticRpcService.getAbilityResource(query);

        if (CollectionUtil.isNotEmpty(abilityResource.getRecords())) {
            List<LineCountMessageQueueDTO> lineCountQueueInfo = localTaskQueueInfoRpcService.getLineCountQueueInfo();
            if (CollectionUtil.isNotEmpty(lineCountQueueInfo)) {
                Map<String, LineCountMessageQueueDTO> collect = lineCountQueueInfo.stream().collect(Collectors.toMap(LineCountMessageQueueDTO::getAbilityId, x -> x, (var1, var2) -> var1));
                for (AbilityResourceItemDTO item : abilityResource.getRecords()) {
                    String id = item.getAbilityId().toString();
                    if (collect.containsKey(id)) {
                        item.setAbilityTrafficNum(collect.get(id).getLineCount().intValue());
                    }

                }
            }
        }
        return new BaseResp<>(abilityResource);
    }

    @Token
    @ApiOperation(value = "修改应用扩容信息")
    @PostMapping("/statistic/updateScale")
    public BaseResp<Void> updateScale(@RequestBody AbilityScaleUpdateParams params) {
        AbilityScaleUpdateDTO abilityScaleUpdateDTO = apiConvertMapper.updateParamsToUpdateCmd(params);
        statisticRpcService.updateScale(abilityScaleUpdateDTO);
        return new BaseResp<>();
    }

    //    @Token
    @ApiOperation(value = "迁移应用到另一个集群")
    @PostMapping("/statistic/updateMigrateAbility")
    public BaseResp<Void> migrateAbility(@RequestBody MigrateAbilityParams params) {
        MigrateAbilityDTO abilityScaleUpdateDTO = apiConvertMapper.migrateAbility2DTO(params);
        statisticRpcService.migrateAbility(abilityScaleUpdateDTO);
        return new BaseResp<>();
    }

    //    @Token
    @ApiOperation(value = "机房信息")
    @PostMapping("/statistic/salveInfo")
    public BaseResp<List<SlaveListDTO>> salveInfo() {
        List<SlaveListDTO> abilityResource = statisticRpcService.getSlaveList();
        return new BaseResp<>(abilityResource);
    }

    /**
     * 获取输出管理列表
     *
     * @return
     */
    @Token
    @GetMapping("/service/tbApi/getApiField")
    public BaseResp getApiField() {
        return tbApiManagerService.getOutputFields(-1L);
    }

    /**
     * 获取应用详情
     *
     * @return
     */
    @Token
    @GetMapping("/service/app/manage/detail")
    public BaseResp getOmDetail(@RequestParam(required = false, value = "taskTypeCode") String taskTypeCode) {
        return taskTypeService.taskTypeOmDetail(taskTypeCode);
    }

    /**
     * 编辑应用详情：名称
     *
     * @return
     */
    @Token
    @PostMapping("/service/app/manage/editAppName")
    public BaseResp editeOmDetailName(@Valid @RequestBody AppNameEditRequest appNameEditRequest) {

        return new BaseResp(taskTypeService.editeOmDetailName(appNameEditRequest));

    }

    /**
     * 编辑应用详情：gpu
     *
     * @return
     */
    @Token
    @PostMapping("/service/app/manage/editAppGpu")
    public BaseResp editeOmDetailGpu(@Valid @RequestBody AppGpuEditRequest appGpuEditRequest) {

        return new BaseResp(taskTypeService.editeOmDetailGpu(appGpuEditRequest));
    }


    /**
     * 编辑应用详情：gpu
     *
     * @return
     */
    @Token
    @PostMapping("/service/app/manage/editAppApi")
    public BaseResp editeOmDetailApi(@Valid @RequestBody AppApiEditRequest appApiEditRequest) {

        return new BaseResp(taskTypeService.editeOmDetailApi(appApiEditRequest));
    }

    /**
     * 判断是否为超管
     *
     * @return
     */
    @Token
    @GetMapping("/service/api/determine")
    public BaseResp getOmDetail() {
        String appKey = DubboDataContext.getAppKeyHolder();
        TUserAuthentication tUserAuthentication = tUserManagerService.getUserInfo(appKey);
        boolean flag = false;
        if (tUserAuthentication.getIsAdmin() == 1) {
            flag = true;
        }
        return new BaseResp(flag);
    }

    /**
     * 创建一个分片上传任务
     *
     * @return
     */
    @PostMapping("/service/api/upload/initMultiPartUpload")
    public BaseResp<InitMultiPartUploadResp> initMultiPartUpload(@RequestBody InitMultiPartReq initMultiPartReq) {
        LinkerStorageProperties.MinIO minio = linkerStorageProperties.getMinio().stream().filter(LinkerStorageProperties.MinIO::getEnableStorage).findFirst()
                .orElseThrow(() -> new RuntimeException("minio未配置"));
        InitMultiPartUploadResp initMultiPartUploadResp = linkerStorageService.initMultiPartUpload(minio.getPlatform(), initMultiPartReq);
        return new BaseResp(initMultiPartUploadResp);
    }

    /**
     * 分片上传合并
     *
     * @return
     */
    @PostMapping("/service/api/upload/mergeMultipart")
    public BaseResp<MultiUploadMergeResp> mergeMultipart(@RequestBody MultiUploadMergeReq multiUploadMergeReq) {
        LinkerStorageProperties.MinIO minio = linkerStorageProperties.getMinio().stream().filter(LinkerStorageProperties.MinIO::getEnableStorage).findFirst()
                .orElseThrow(() -> new RuntimeException("minio未配置"));
        MultiUploadMergeResp multiUploadMergeResp = linkerStorageService.multiPartMerge(minio.getPlatform(), multiUploadMergeReq);
        return new BaseResp(multiUploadMergeResp);
    }

    /**
     * 中止分片上传
     *
     * @return
     */
    @PostMapping("/service/api/upload/abortMultipartUpload")
    public BaseResp<MultiUploadMergeResp> abortMultipartUpload(@RequestBody MultiUploadMergeReq multiUploadMergeReq) {
        LinkerStorageProperties.MinIO minio = linkerStorageProperties.getMinio().stream().filter(LinkerStorageProperties.MinIO::getEnableStorage).findFirst()
                .orElseThrow(() -> new RuntimeException("minio未配置"));
        Boolean abort = linkerStorageService.abortMultipartUpload(minio.getPlatform(), multiUploadMergeReq);
        return new BaseResp(abort);
    }

    @SentinelResource(value = "dataToVector")
    @Token
    @ApiOperation(value = "转换为向量")
    @PostMapping("/server/vector/dataToVector")
    public BaseResp dataToVector(@RequestBody @Valid VectorByDataDTO vectorByDataDTO) {
        List<VectorSubmitImageDTO> dataList = vectorByDataDTO.getDataList();
        for (VectorSubmitImageDTO v : dataList) {
            if (StrUtil.isBlank(v.getDeviceId())) {
                v.setDeviceId("default");
            }
        }
        if (StrUtil.isNotBlank(vectorByDataDTO.getActionId())) {
            String authIdHolder = DubboDataContext.getAuthIdHolder();
            String abilityId = vectorAbilityCheck(authIdHolder, vectorByDataDTO.getActionId());
            vectorByDataDTO.setActionId(abilityId);
        }

        return new BaseResp(iTaskService.dataToVectorByKernel(vectorByDataDTO));
    }


    /**
     * 校验
     *
     * @param authIdHolder
     * @param actionId
     * @return
     */
    private String vectorAbilityCheck(String authIdHolder, String actionId) {

        TaskTypeEntity userAbility = cacheService.getUserAbility(authIdHolder + "#" + actionId);
        if (null == userAbility) {
            throw new BusinessException("租户无次算法权限");
        }
        if (userAbility.getStatus() != 0) {
            throw new BusinessException("租户算法:" + actionId + "已停用");
        }
        Integer abilityEnum = userAbility.getAbilityEnum();
        if (abilityEnum != 9 && abilityEnum != 5 && abilityEnum != 6 && abilityEnum != 7) {
            throw new BusinessException("此算法非向量化算法，不可用");
        }
        return userAbility.getAbility();
    }

//
//    @SentinelResource(value = "dataToVector")
//    @Token
//    @ApiOperation(value = "转换为向量")
//    @PostMapping("/v2/server/vector/dataToVector")
//    public BaseResp dataToVectorV2(@RequestBody @Valid VectorToDataRequestDTO vectorByData) {
//        List<VectorItemDTO> dataList = vectorByData.getDataList();
//        for (VectorItemDTO v : dataList) {
//            if (StrUtil.isBlank(v.getDeviceId())) {
//                v.setDeviceId("default");
//            }
//        }
//        return new BaseResp(iTaskService.dataToVectorByKernel(vectorByDataDTO));
//    }

    @Token
    @SentinelResource(value = "health")
    @PostMapping("/service/tTask/fileTaskUpload")
    @ApiOperation("虚拟任务执行")
//    @LogMetric
    public BaseResp fileTaskUpload(@RequestBody SaveInvented saveInvented) {
        if (StrUtil.isBlank(saveInvented.getVideoCode())) {
            saveInvented.setVideoCode("default");
        }
        if (saveInvented.getShouldReflux() == null) {
            saveInvented.setShouldReflux(refluxProperties.getOpenV2Reflux());
        }
        return new BaseResp(iTaskService.fileTaskUploadByKernel(saveInvented));
    }

    @Token
    @ApiOperation(value = "API V3-Serving")
    @SentinelResource(value = "sendToV3AiAsync")
    @PostMapping("/calculate/sendToV3AiAsync")
    public BaseResp sendToV3AiAsync(@RequestBody V3AiAbilityAsyncCallParams params) {
        if (params.getShouldReflux() == null) {
            params.setShouldReflux(refluxProperties.getOpenV3Reflux());
        }
        return new BaseResp(iTaskService.sendToV3AiAsyncByKernel(params));
    }

    @Token
    @ApiOperation(value = "API V3.5-Serving")
    @SentinelResource(value = "sendToAiForEvent")
    @PostMapping("/calculate/sendToAiForEvent")
    public BaseResp sendToAiForEvent(@RequestBody @Valid V35AiAbilityAsyncCallParams params) {
        if (params.getShouldReflux() == null) {
            params.setShouldReflux(refluxProperties.getOpenV35Reflux());
        }
        return new BaseResp(iTaskService.sendToAiForEventByKernel(params));
    }

    @Token
    @SentinelResource(value = "vector")
    @ApiOperation(value = "入库向量")
    @PostMapping("/server/vector/submit")
    public BaseResp vectorSubmit(@RequestBody @Valid VectorSubmitDTO vectorSubmitDTO) {
        if (StrUtil.isNotBlank(vectorSubmitDTO.getActionId())) {
            String authIdHolder = DubboDataContext.getAuthIdHolder();
            String abilityId = vectorAbilityCheck(authIdHolder, vectorSubmitDTO.getActionId());
            vectorSubmitDTO.setActionId(abilityId);
        }
        return new BaseResp(apiService.vectorSubmit(vectorSubmitDTO));
    }

    @Token
    @SentinelResource(value = "vectorSearch")
    @ApiOperation(value = "搜索向量")
    @PostMapping("/server/vector/search")
    public BaseResp vectorSearch(@RequestBody @Valid VectorSearchDTO vectorSearchDTO) {
        if (StrUtil.isNotBlank(vectorSearchDTO.getActionId())) {
            String authIdHolder = DubboDataContext.getAuthIdHolder();
            String abilityId = vectorAbilityCheck(authIdHolder, vectorSearchDTO.getActionId());
            vectorSearchDTO.setActionId(abilityId);
        }
        return new BaseResp(apiService.vectorSearch(vectorSearchDTO));
    }

    @Token
    @ApiOperation(value = "删除入库向量")
    @PostMapping("/server/vector/delete")
    public BaseResp deleteVector(@RequestBody @Valid VectorDeleteDTO vectorDeleteDTO) {
        return new BaseResp(apiService.deleteVector(vectorDeleteDTO));
    }

    @Token
    @ApiOperation(value = "应用趋势统计")
    @PostMapping("/server/trend/stat")
    public BaseResp abilityTrendStat(@RequestBody @Valid AppTrendStatDTO appTrendStatDTO) {
        List<TaskAbilityTrendStatVO> taskCountVOS = apiService.abilityTrendStat(appTrendStatDTO);
        return new BaseResp(taskCountVOS);
    }

}