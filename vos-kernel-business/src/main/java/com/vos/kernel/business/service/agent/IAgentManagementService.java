package com.vos.kernel.business.service.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.vos.kernel.business.dto.agent.AgentDetailInfoDTO;
import com.vos.kernel.business.dto.agent.AgentInfoDTO;
import com.vos.kernel.business.dto.agent.ListInfoQueryDTO;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.dto.agent.PackageCreateDTO;
import com.vos.kernel.business.dto.agent.BatchOperateDTO;
import com.vos.kernel.business.dto.agent.EventTriggerInfoDTO;
import com.vos.kernel.business.dto.agent.OperateResultDTO;

import java.util.List;

public interface IAgentManagementService {
    IPage<AgentInfoDTO> getAgentList(ListInfoQueryDTO queryDTO);

    AgentDetailInfoDTO getAgentDetail(String tenantId, String agentId);

    IPage<EventTriggerInfoDTO> getEventTriggerList(ListInfoQueryDTO queryDTO);

    AgentInfoEntity getAgentInfo(String agentId);

    void deleteAgent(String agentId);

    /**
     * 创建智能体
     * @param createDTO 创建智能体请求参数
     * @return 智能体ID
     */
    String createAgent(PackageCreateDTO createDTO);

    /**
     * 批量停止智能体
     * @param operateDTO 操作参数
     * @return 每个智能体的操作结果
     */
    List<OperateResultDTO> stopAgent(BatchOperateDTO operateDTO);

    /**
     * 批量启动智能体
     * @param operateDTO 操作参数
     * @return 每个智能体的操作结果
     */
    List<OperateResultDTO> startAgent(BatchOperateDTO operateDTO);
}
