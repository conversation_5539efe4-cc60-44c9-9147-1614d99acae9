package com.vos.kernel.business.dto.agent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月04日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class KnowledgeAgentTemplateDTO implements Serializable {


    /**
     * 知识库配置
     */
    @ApiModelProperty("知识库配置")
    private KnowledgeConfig knowledgeConfig;

    /**
     * 问题分类
     */
    @ApiModelProperty("问题分类")
    private QuestionClassifier questionClassifier;

    /**
     * 词库
     */
    @ApiModelProperty("词库")
    private WordGroup wordGroup;

    /**
     * 输入设置
     */
    @ApiModelProperty("输入设置")
    private InputSetting inputSetting;

    /**
     * 推荐回复信息
     */
    @ApiModelProperty("推荐回复信息")
    private SuggestReplyInfo suggestReplyInfo;

    /**
     * 人设配置
     */
    private String prompt;

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 文件列表
     */
    private List<ResourceInfoDTO> resList;


    @Data
    public static class QuestionClassifier {

        /**
         * 是否开启问题分类
         */
        @ApiModelProperty("是否开启问题分类")
        private Boolean questionClassifierEnable;

        /**
         * 问题分类背景知识
         */
        @ApiModelProperty("问题分类背景知识")
        private String questionClassifierBackground;

        /**
         * 分类项列表
         */
        @ApiModelProperty("分类项列表")
        private List<ClassifierItem> questionClassifierList;
    }

    @Data
    public static class ClassifierItem {

        /**
         * 知识库配置
         */
        @ApiModelProperty("知识库配置")
        private KnowledgeConfig knowledgeConfig;

        /**
         * 固定话术内容
         */
        @ApiModelProperty("固定话术内容")
        private String fixedMessage;

        /**
         * 分类名称
         */
        @ApiModelProperty("分类名称")
        private String name;

        /**
         * 分类类型
         */
        @ApiModelProperty("分类类型")
        private Integer type;
    }

    @Data
    public static class WordGroup {

        /**
         * 敏感词库ids
         */
        @ApiModelProperty("敏感词库ids")
        private List<Long> sensitiveGroupIds;

        /**
         * 敏感词拒识话术
         */
        @ApiModelProperty("敏感词拒识话术")
        private String sensitiveRefuseMessage;

        /**
         * 专业词库ids
         */
        @ApiModelProperty("专业词库ids")
        private List<Long> proGroupIds;
    }

    @Data
    public static class InputSetting {

        /**
         * 启用上传图片
         */
        @ApiModelProperty("启用上传图片")
        private Boolean enableUploadImage;

        /**
         * 启用上传文档
         */
        @ApiModelProperty("启用上传文档")
        private Boolean enableUploadDoc;
    }
}
