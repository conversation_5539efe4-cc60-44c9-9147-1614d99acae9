package com.vos.kernel.business.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/9
 * @description: com.linker.user.api.dto
 */
@Data
public class RoleMenuInfoVo implements Serializable {
    private static final long serialVersionUID = 6327236810613319970L;

    private Long roleId;
    private String roleName;
    private String roleCode;
    private Integer roleType;

    private List<MenuInfo> menuInfoList;

    public boolean equals(Object obj) {
        if (this == obj) { //判断一下如果是同一个对象直接返回true，提高效率
            return true;
        }
        if (obj == null || obj.getClass() != this.getClass()) { //如果传进来的对象为null或者二者为不同类，直接返回false
            return false;
        }
        RoleMenuInfoVo roleMenuInfo = (RoleMenuInfoVo) obj; //向下转型
        return Objects.equals(roleMenuInfo.getRoleId(), this.getRoleId());
    }

    @Data
    public static class MenuInfo{
        private Long menuId;
        private String menuUrl;
        private String functionPoint;

        public boolean equals(Object obj) {
            if (this == obj) { //判断一下如果是同一个对象直接返回true，提高效率
                return true;
            }
            if (obj == null || obj.getClass() != this.getClass()) { //如果传进来的对象为null或者二者为不同类，直接返回false
                return false;
            }
            MenuInfo menuInfo = (MenuInfo) obj; //向下转型
            return Objects.equals(menuInfo.getMenuId(), this.getMenuId());
        }
    }
}