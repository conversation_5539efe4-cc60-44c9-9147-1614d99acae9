package com.vos.kernel.business.dto.agent;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class AgentDetailInfoDTO {
    @JsonProperty("agent_id")
    private String agentId;

    @JsonProperty("tenant_id")
    private String tenantId;

    @JsonProperty("agent_name")
    private String agentName;

    private String description;

    @JsonProperty("icon")
    private String iconPath;

    @JsonProperty("create_time")
    private LocalDateTime createTime;

    @JsonProperty("update_time")
    private LocalDateTime updateTime;

    @JsonProperty("last_start_time")
    private LocalDateTime lastStartTime;

    @JsonProperty("running")
    private Integer running;

    @JsonProperty("welcome")
    private String welcome;

    @JsonProperty("preset_questions")
    private List<String> presetQuestions;

}
