package com.vos.kernel.business.service.agentChat;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.core.base.exception.BusinessException;
import com.linker.omagent.starter.manager.RedisStreamManager;
import com.vos.kernel.business.constants.AgentosConstants;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.enums.StatusEnum;
import com.vos.kernel.business.service.agent.IAgentManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/12/10 14:39
 * @DESC:
 */
@Slf4j
@Service
public class ChatServiceImpl implements IChatService {

    @Autowired
    private StreamEventHandler streamEventHandler;

    @Autowired
    private IAgentManagementService agentManagementService;

    @Autowired
    private RedisStreamManager redisStreamManager;

    @Override
    public SseEmitter stream(StreamChatRequest streamChatRequest) {
        String conversationId = streamChatRequest.getMessage().getConversationId();
        String agentId = streamChatRequest.getAgentId();

        boolean isDebug = streamChatRequest.isDebug();
        String workflow = null;
        String workflowVersion = null;
        if(isDebug){
            Map<String,String> extraParams = streamChatRequest.getExtraParams();
            if(extraParams != null){
                workflow = extraParams.get("workflow");
                workflowVersion = extraParams.get("workflowVersion");
            }
        } else{
            AgentInfoEntity agentInfoEntity = agentManagementService.getAgentInfo(agentId);
            if (agentInfoEntity == null) {
                throw new BusinessException("智能体不存在|" + agentId);
            }
            if (!Objects.equals(StatusEnum.RUNNING.type, agentInfoEntity.getRunning())) {
                throw new BusinessException("智能体非运行状态|" + agentId);
            }
            workflow = agentInfoEntity.getWorkflow();
            workflowVersion = agentInfoEntity.getWorkflowVersion();
        }

        /**
         * sse客户端会有重试逻辑
         * 当重试时，已存在会话处理器则直接返回
         */
        log.info("receive stream request, streamChatRequest: {}", streamChatRequest);
        ConversationProcessor processor = new ConversationProcessor(agentId, conversationId);
        try {
            processor.getSseEmitter().send(SseEmitter.event().name("ready").data("ready"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        streamEventHandler.startChat(streamChatRequest, workflow, workflowVersion, processor);
        return processor.getSseEmitter();
    }

    @Override
    public void askComplete(StreamChatRequest streamChatRequest) {
        //TODO 请求中应该带上 channel
        String channel = "";
        JSONObject obj = new JSONObject();
        obj.put("agent_id",streamChatRequest.getAgentId());
        obj.put("messages",streamChatRequest.getMessage().getHistory());
        obj.put("kwargs",streamChatRequest.getMetaData());
        Map<String,String> map = new HashMap<>();
        map.put("payload",obj.toJSONString());
        redisStreamManager.sendToStream(AgentosConstants.getRedisStreamInputKey(channel), JSON.toJSONString(map));
    }

}
