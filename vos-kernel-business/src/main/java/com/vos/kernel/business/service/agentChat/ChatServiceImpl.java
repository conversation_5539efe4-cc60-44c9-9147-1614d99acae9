package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.exception.BusinessException;
import com.linker.core.utils.IdWorker;
import com.linker.omagent.starter.manager.RedisStreamManager;
import com.vos.kernel.business.api.IV3ApiBackService;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.constants.AgentosConstants;
import com.vos.kernel.business.dto.ChatDTO;
import com.vos.kernel.business.dto.ErrorInfo;
import com.vos.kernel.business.dto.ModelConfigDTO;
import com.vos.kernel.business.dto.ModelConfigurationDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.StreamMessageDTO;
import com.vos.kernel.business.dto.TemplateAgentRunRequest;
import com.vos.kernel.business.dto.WorkflowTerminateRequest;
import com.vos.kernel.business.dto.agent.AgentImageMetaDTO;
import com.vos.kernel.business.dto.agent.AgentMetaDTO;
import com.vos.kernel.business.dto.agent.WorkflowAsyncRequest;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.entity.ChatHistoryEntity;
import com.vos.kernel.business.entity.ConversationEntity;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.entity.UserVo;
import com.vos.kernel.business.enums.StatusEnum;
import com.vos.kernel.business.service.agent.IAgentManagementService;
import com.vos.kernel.business.service.batisplus.IAgentAuthorizeService;
import com.vos.kernel.business.service.chat.IChatHistoryService;
import com.vos.kernel.business.service.conversation.IConversationService;
import com.vos.kernel.business.service.impl.AppManageManagerServiceImpl;
import com.vos.kernel.business.utils.CompletableFutureExpandUtils;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.entity.R;
import com.vos.kernel.common.enums.ChatEventEnum;
import com.vos.task.automated.api.model.entity.SlaveEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/10 14:39
 * @DESC:
 */
@Slf4j
@Service
public class ChatServiceImpl implements IChatService {

    @Resource
    private WorkflowProperties workflowProperties;

    @Resource
    private IV3ApiBackService apiBackService;

    @Autowired
    private StreamEventHandler streamEventHandler;

    @Autowired
    private IAgentManagementService agentManagementService;

    @Autowired
    private RedisStreamManager redisStreamManager;

    @Resource
    TemplateChatBuilder templateChatBuilder;

    @Resource
    InstructionFunctionCallBuilder instructionFunctionCallBuilder;

    @Resource
    AppManageManagerServiceImpl appManageManagerService;

    @Resource
    DtpExecutor v2Executor;

    @Resource
    IAgentAuthorizeService agentAuthorizeService;

    @Resource
    private IChatHistoryService chatHistoryService;

    @Autowired
    private IConversationService conversationService;
 
    @Value("${sse.stream.timeout:1800000}")
    private long timeouts = 30 * 60 * 1000;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public SseEmitter stream(StreamChatRequest streamChatRequest) {
        try {
            String streamChatRequestJson = objectMapper.writeValueAsString(streamChatRequest);
            log.info("receive stream request, streamChatRequest: {}", streamChatRequestJson);
            // 校验消息体不能为空
            if (streamChatRequest.getMessage() == null) {
                throw new BusinessException("消息体不能为空");
            }
            String conversationId = streamChatRequest.getMessage().getConversationId();
            // 校验会话ID不能为空
            if (StrUtil.isBlank(conversationId)) {
                throw new BusinessException("会话ID不能为空");
            }
            String agentId = streamChatRequest.getAgentId();
            Map<String, Object> context = new HashMap<>();
            Map<String, Object> entryParams = new HashMap<>();
            boolean isDebug = streamChatRequest.isDebug();
            AgentInfoEntity agentInfoEntity = new AgentInfoEntity();
            boolean isSystem = false;
            if (isDebug) {
                Map<String, String> extraParams = streamChatRequest.getExtraParams();
                if (extraParams != null) {
                    agentInfoEntity.setWorkflow(extraParams.get("workflow"));
                    agentInfoEntity.setWorkflowVersion(extraParams.get("workflowVersion"));
                    if (StrUtil.isNotBlank(extraParams.get("agentType")) && NumberUtil.isNumber(extraParams.get("agentType"))) {
                        agentInfoEntity.setAgentType(Integer.valueOf(extraParams.get("agentType")));
                    }
                    if (StrUtil.isNotBlank(extraParams.get("operateType"))) {
                        agentInfoEntity.setAgentType(3);
                        agentInfoEntity.setOperateType(Integer.valueOf(extraParams.get("operateType")));
                    }
                    if (null != streamChatRequest.getMetaData()
                            && streamChatRequest.getMetaData().get("template_params_str") != null) {
                        agentInfoEntity.setTemplateParams(streamChatRequest.getMetaData().get("template_params_str").toString());
                        streamChatRequest.getMetaData().remove("template_params_str");
                    }
                }
            } else {
                agentInfoEntity = agentManagementService.getAgentInfo(agentId);
                if (agentInfoEntity == null) {
                    throw new BusinessException("智能体不存在|" + agentId);
                }
                if (!Objects.equals(StatusEnum.RUNNING.type, agentInfoEntity.getRunning())) {
                    throw new BusinessException("智能体非运行状态|" + agentId);
                }
                String authIdHolder = DubboDataContext.getAuthIdHolder();
                if (BooleanUtil.isTrue(streamChatRequest.isAuthorizeCall())) {
                    Boolean authorizeAll = agentInfoEntity.getAuthorizeAll();
                    //非授权调用，判断是否是系统模板智能体
                    if (!BooleanUtil.isTrue(authorizeAll) && !authIdHolder.equals(agentInfoEntity.getTenantId())) {
                        if (!agentAuthorizeService.isAgentAuthorized(authIdHolder, agentId)) {
                            throw new BusinessException("智能体无权限调用|" + agentId);
                        }
                    }
                    if (!authIdHolder.equals(agentInfoEntity.getTenantId())) {
                        isSystem = true;
                    }
                } else {
                    if (StrUtil.isNotBlank(authIdHolder) && !authIdHolder.equals(agentInfoEntity.getTenantId())) {
                        throw new BusinessException("智能体非当前租户|" + agentId);
                    }
                }
            }
            //系统模板智能体；模板信息组装
            if (agentInfoEntity.getAgentType() != null && templateChatBuilder.isSupport(agentInfoEntity.getAgentType())) {
                templateChatBuilder.buildTemplateParams(streamChatRequest, agentInfoEntity);
            }
            //指令配置
            instructionFunctionCallBuilder.buildTemplateParams(isDebug, streamChatRequest, agentInfoEntity, context, entryParams);
            /**
             * sse客户端会有重试逻辑
             * 当重试时，已存在会话处理器则直接返回
             */
            ConversationProcessor processor = new ConversationProcessor(agentId, conversationId, timeouts);
            SseEmitter sseEmitter = processor.getSseEmitter();
            try {
                sseEmitter.send(SseEmitter.event().name("ready").data("ready"));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            String chatId = String.valueOf(UUID.randomUUID().toString());
            processor.setChatId(chatId);
            context.put("chatId", chatId);
            context.put("conversationId", conversationId);
            context.put("userCode", DubboDataContext.getUserHolder());
            context.put("departmentCodes", DubboDataContext.getDepartHolder());
            // 检查会话表是否存在，不存在则创建
            Long agentIdLong = Long.parseLong(agentId);
            ConversationEntity conversation = conversationService.getByConversationIdAndAgentId(conversationId, agentIdLong);
            if (conversation == null) {
                conversation = new ConversationEntity();
                conversation.setConversationId(conversationId);
                conversation.setAgentId(agentIdLong);
                conversation.setAgentType(agentInfoEntity.getAgentType());
                // 名称用第一个问题内容，限制200字符
                String name = null;
                if (streamChatRequest.getMessage() != null && streamChatRequest.getMessage().getContent() != null) {
                    name = streamChatRequest.getMessage().getContent();
                }
                if (name != null && name.length() > 200) {
                    name = name.substring(0, 200);
                }
                conversation.setName(name);
                conversation.setCreatorId(DubboDataContext.getAuthIdHolder());
                conversation.setUserId(DubboDataContext.getUserHolder());
                conversation.setTenantId(DubboDataContext.getAuthIdHolder());
                conversation.setCreateTime(LocalDateTime.now());
                conversation.setUpdateTime(LocalDateTime.now());
                if (streamChatRequest.isExperience()) {
                    conversation.setDebug(true);
                    agentInfoEntity.setConversationId(conversationId);
                    agentManagementService.updateAgent(agentInfoEntity);
                } else {
                    conversation.setDebug(streamChatRequest.isDebug());
                }
                conversation.setDeleted(0);
                conversationService.createConversation(conversation);
            } else {
                conversation.setUpdateTime(LocalDateTime.now());
                conversationService.updateConversation(conversation);
            }

            streamEventHandler.startChat(streamChatRequest, agentInfoEntity, context, entryParams, processor, isSystem, streamChatRequestJson);
            return processor.getSseEmitter();
        } catch (Exception e) {
            log.error("Stream chat error, conversationId: {}", streamChatRequest.getMessage() != null ? streamChatRequest.getMessage().getConversationId() : "N/A", e);

            SseEmitter sseEmitter = new SseEmitter(-1L);

            ChatDTO failedChatDTO = new ChatDTO();
            if (streamChatRequest.getMessage() != null) {
                failedChatDTO.setConversationId(streamChatRequest.getMessage().getConversationId());
            }
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setMessage(e.getMessage());
            failedChatDTO.setError(errorInfo);
            failedChatDTO.setStatus(ChatEventEnum.CHAT_FAILED.getEvent());

            StreamMessageDTO failedStreamMessageDTO = new StreamMessageDTO();
            failedStreamMessageDTO.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());
            failedStreamMessageDTO.setData(JSON.toJSONString(failedChatDTO));

            StreamMessageDTO doneStreamMessageDTO = new StreamMessageDTO();
            doneStreamMessageDTO.setEvent(ChatEventEnum.DONE.getEvent());
            doneStreamMessageDTO.setData(JSON.toJSONString(new ChatDTO()));

            try {
                sseEmitter.send(SseEmitter.event().name(failedStreamMessageDTO.getEvent()).data(failedStreamMessageDTO.getData(),
                        MediaType.APPLICATION_JSON));
                sseEmitter.send(SseEmitter.event().name(doneStreamMessageDTO.getEvent()).data(doneStreamMessageDTO.getData(),
                        MediaType.APPLICATION_JSON));
                sseEmitter.complete();
            } catch (IOException ex) {
                log.error("Failed to send sse error event", ex);
            }
            return sseEmitter;
        }
    }

    @Override
    public String runAsync(TemplateAgentRunRequest agentRunRequest) {
        String agentId = agentRunRequest.getAgentId();
        AgentInfoEntity agentInfoEntity = agentManagementService.getAgentInfo(agentId);
        if (agentInfoEntity == null) {
            throw new BusinessException("智能体不存在|" + agentId);
        }
        if (!Objects.equals(StatusEnum.RUNNING.type, agentInfoEntity.getRunning())) {
            throw new BusinessException("智能体非运行状态|" + agentId);
        }
        if (agentInfoEntity.getAgentType() != 3) {
            throw new BusinessException("非模板智能体|" + agentId);
        }
        AgentMetaDTO metaData = agentRunRequest.getMetaData();

        if (BooleanUtil.isTrue(workflowProperties.getCheckImageUrl())) {
            //图片校验
            validImage(metaData.getImages().stream().map(AgentImageMetaDTO::getImageUrl).collect(Collectors.toList()));
        }

        Map<String, Object> input = buildRequest(CollectionUtil.getFirst(metaData.getImages()));
        //选区处理
        if (ObjectUtil.isNotEmpty(agentRunRequest.getIdent_area()) && ObjectUtil.isNotEmpty(agentRunRequest.getInclude_area())) {
            input.put("ident_area", agentRunRequest.getIdent_area());
            input.put("include_area", agentRunRequest.getInclude_area());
        } else {
            input.put("ident_area", new JSONArray());
            input.put("include_area", true);
        }
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("operateType", agentInfoEntity.getOperateType().toString());
        String templateParamsStr = metaData.getTemplateParamsStr();
        ModelConfigurationDTO modelConfiguration = JSON.parseObject(agentInfoEntity.getTemplateParams(), ModelConfigurationDTO.class);
        if (StrUtil.isNotBlank(templateParamsStr)) {
            //需要替换自定义参数
            ModelConfigurationDTO modelConfigurationCustom = JSON.parseObject(templateParamsStr, ModelConfigurationDTO.class);
            //替换大语言告警事件
            if (StrUtil.isNotBlank(modelConfiguration.getLlmEventDesc()) && !modelConfiguration.getLlmEventDesc().equals(modelConfigurationCustom.getLlmEventDesc())) {
                modelConfiguration.setLlmEventDesc(modelConfigurationCustom.getLlmEventDesc());
            }
            //过滤配置
            if (CollectionUtil.isNotEmpty(modelConfiguration.getFilterModelConf())
                    && CollectionUtil.isNotEmpty(modelConfigurationCustom.getFilterModelConf())) {
                validModelConfig(modelConfiguration.getFilterModelConf(), modelConfigurationCustom.getFilterModelConf());
                modelConfiguration.setFilterModelConf(modelConfigurationCustom.getFilterModelConf());
            }
            //目标配置
            if (CollectionUtil.isNotEmpty(modelConfiguration.getOdModelConf())
                    && CollectionUtil.isNotEmpty(modelConfigurationCustom.getOdModelConf())) {
                validModelConfig(modelConfiguration.getOdModelConf(), modelConfigurationCustom.getOdModelConf());
                modelConfiguration.setOdModelConf(modelConfigurationCustom.getOdModelConf());
            }

        }
        //设置过滤的labels
        if (CollectionUtil.isNotEmpty(modelConfiguration.getFilterModelConf())) {
            Set<String> collect = modelConfiguration.getFilterModelConf().stream().map(ModelConfigDTO::getLabel).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(collect)) {
                extraParams.put("filterLabels", collect);
            }
        }
        templateChatBuilder.buildTemplateParamsInner(agentInfoEntity.getOperateType(), modelConfiguration, input);
        Map<String, Object> wrapperInput = new HashMap<>();
        StreamEventHandler.ConversationInfo conversationInfo = new StreamEventHandler.ConversationInfo();
        conversationInfo.setChannel("");
        wrapperInput.put("message", conversationInfo);
        wrapperInput.put("meta_data", input);
        wrapperInput.put("extra_params", extraParams);
        WorkflowAsyncRequest workflowAsyncRequest = new WorkflowAsyncRequest();
        workflowAsyncRequest.setId(agentInfoEntity.getWorkflow());
        workflowAsyncRequest.setSystemSwitch(true);
        workflowAsyncRequest.setPassthrough(MapUtil.isNotEmpty(agentRunRequest.getExtraParams()) ? JSON.toJSONString(agentRunRequest.getExtraParams()) : null);
        workflowAsyncRequest.setCallback(agentRunRequest.getCallbackUrl());
        workflowAsyncRequest.setInput(wrapperInput);

        return apiToWorkflow(workflowAsyncRequest);
    }


    /**
     * 构造基础请求参数
     *
     * @param agentImageMeta
     * @return
     */
    private Map<String, Object> buildRequest(AgentImageMetaDTO agentImageMeta) {
        Map<String, Object> input = new HashMap<>();
        input.put("image_id", agentImageMeta.getImageId());
        input.put("image_url", agentImageMeta.getImageUrl());
        return input;
    }


    /**
     * 验证请求图片是否可以访问
     *
     * @param images
     */
    private void validImage(List<String> images) {
        List<CompletableFuture<Void>> collect = images.stream().map(imageUrl -> CompletableFutureExpandUtils.orTimeout(CompletableFuture.runAsync(RunnableWrapper.of(() -> {
            //检测连接
            if (StrUtil.isBlank(imageUrl)) {
                return;
            } else {
                if (!imageUrl.startsWith("http")) {
                    throw new BusinessException("图片URL必须以http开头");
                }
                try {
                    // 尝试连接URL验证图片是否可访问
                    java.net.URL url = new java.net.URL(imageUrl);
                    java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("HEAD");
                    connection.setConnectTimeout(workflowProperties.getCheckImageTimeOut());
                    connection.setReadTimeout(workflowProperties.getCheckImageTimeOut());
                    int responseCode = connection.getResponseCode();
                    if (responseCode != 200) {
                        throw new BusinessException("图片URL无法访问: " + imageUrl + ", 响应码: " + responseCode);
                    }
                } catch (java.io.IOException e) {
                    log.error("图片URL验证失败: {}", imageUrl, e);
                    throw new BusinessException("图片URL验证失败: " + imageUrl + ", 错误: " + e.getMessage());
                }
            }
        }), v2Executor), workflowProperties.getCheckImageTimeOut() + 100, TimeUnit.MILLISECONDS, false)).collect(Collectors.toList());
        //等待所有结束
        collect.stream().map(CompletableFuture::join).collect(Collectors.toList());
    }


    /**
     * 验证模型配置是否正确
     *
     * @param origin
     * @param custom
     */
    private void validModelConfig(List<ModelConfigDTO> origin, List<ModelConfigDTO> custom) {
        if (origin.size() != custom.size()) {
            throw new BusinessException("自定义模型配置数量不一致");
        }
        Set<String> collectOrigin = origin.stream().map(ModelConfigDTO::getModelId).collect(Collectors.toSet());
        Set<String> collectCustom = custom.stream().map(ModelConfigDTO::getModelId).collect(Collectors.toSet());
        if (!collectOrigin.equals(collectCustom)) {
            throw new BusinessException("自定义模型配置模型ID不一致");
        }

    }


    /**
     * 请求workflow 异步调用接口
     *
     * @param workflowAsyncRequest
     * @return
     */
    private String apiToWorkflow(WorkflowAsyncRequest workflowAsyncRequest) {

        String linkerHeader = appManageManagerService.generateLinkerHeader();
        BaseResp<String> resp = apiBackService.workflowAsyncRequest(workflowProperties.getEndpoint() + workflowProperties.getAsyncRunWorkflowRouter(),
                linkerHeader,
                workflowAsyncRequest);
        if (resp.isSuccess()) {
            return resp.getData();
        } else {
            throw new BusinessException("workflow request error,resp " + resp);
        }
    }

    @Override
    public void askComplete(StreamChatRequest streamChatRequest) {
        //TODO 请求中应该带上 channel
        String channel = "";
        JSONObject obj = new JSONObject();
        obj.put("agent_id", streamChatRequest.getAgentId());
        obj.put("messages", streamChatRequest.getMessage().getHistory());
        obj.put("kwargs", streamChatRequest.getMetaData());
        Map<String, String> map = new HashMap<>();
        map.put("payload", obj.toJSONString());
        redisStreamManager.sendToStream(AgentosConstants.getRedisStreamInputKey(channel), JSON.toJSONString(map));
    }

    @Override
    public boolean cancel(String chatId) throws UnsupportedEncodingException {
        // 通过chatId查找会话历史，获取workflowId和tenantId
        ChatHistoryEntity chatHistory = chatHistoryService.getByChatId(chatId);
        if (chatHistory == null || chatHistory.getWorkflowId() == null) {
            log.error("Cancel failed: chat history or workflowId not found for chatId={}", chatId);
            return false;
        }
        String workflowId = chatHistory.getWorkflowId();
        String tenantId = chatHistory.getTenantId();
        if (workflowId == null || tenantId == null) {
            log.error("Cancel failed: workflowId or tenantId is null for chatId={}", chatId);
            return false;
        }
        WorkflowTerminateRequest workflowTerminateRequest = new WorkflowTerminateRequest();
        workflowTerminateRequest.setWorkflowInstanceId(workflowId);
        UserContext userInfo = new UserContext();
        UserVo userVo = new UserVo();
        userVo.setUserCode(DubboDataContext.getUserHolder());
        userInfo.setUser(userVo);
        UserContext.TenantInfoDTO tenantInfoDTO = new UserContext.TenantInfoDTO();
        tenantInfoDTO.setTenantId(tenantId);
        userInfo.setTenantInfoDTO(tenantInfoDTO);
        String json = URLEncoder.encode(JSON.toJSONString(userInfo), "utf-8");

        BaseResp<String> resp = apiBackService.terminateWorkflow(workflowProperties.getEndpoint() + workflowProperties.getTerminateWorkflowRouter(),
                json,
                workflowTerminateRequest);
        if (resp.isSuccess()) {
            return true;
        } else {
            log.error("Cancel failed: terminate workflow failed for chatId={}, resp.code={}, resp.message={}", chatId, resp.getCode(), resp.getMessage());
            return false;
        }
    }

}
