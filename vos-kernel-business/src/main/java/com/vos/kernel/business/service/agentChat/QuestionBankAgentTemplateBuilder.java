package com.vos.kernel.business.service.agentChat;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.vos.kernel.business.dto.ModelConfigurationDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.agent.QuestionBankAgentTemplateDTO;
import com.vos.kernel.business.entity.AgentInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年06月05日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class QuestionBankAgentTemplateBuilder implements ITemplateChatBuilderService {

    private final static Integer TEMPLATE_TYPE = 7;

    @Resource
    TemplateChatBuilder templateChatBuilder;


    @PostConstruct
    public void init() {
        templateChatBuilder.registerTemplateChatBuilder(TEMPLATE_TYPE, this);
    }

    @Override
    public void buildTemplateParamsInner(Integer templateType, ModelConfigurationDTO modelConfiguration, Map<String, Object> metaData) {

    }

    @Override
    public void buildTemplateParams(StreamChatRequest streamChatRequest, AgentInfoEntity agentInfoEntity) {
        String templateParams = agentInfoEntity.getTemplateParams();
        if (streamChatRequest.getMetaData() == null) {
            streamChatRequest.setMetaData(MapUtil.newHashMap());
        }
        QuestionBankAgentTemplateDTO questionBankAgentTemplateDTO = JSON.parseObject(templateParams, QuestionBankAgentTemplateDTO.class);
        //培训智能体独有字段
        streamChatRequest.getMetaData().put("questionAgentDataInfo", questionBankAgentTemplateDTO);

    }
}
