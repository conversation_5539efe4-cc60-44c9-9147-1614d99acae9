package com.vos.kernel.business.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ConversationVO implements Serializable {
    private String conversationId;
    private String agentId;
    private String name;
    private String creatorId;
    private String userId;
    private String tenantId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Integer agentType;
} 