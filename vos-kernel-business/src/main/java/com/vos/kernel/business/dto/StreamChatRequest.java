package com.vos.kernel.business.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/12/10 11:05
 * @DESC: 发起聊天请求接口
 */
@Data
public class StreamChatRequest implements Serializable {
    /**
     * 智能体ID
     * 必选字段，用于标识具体的智能体
     */
    @JsonProperty("agent_id")
    private String agentId;

    private Message message;

//    /**
//     * 智能体中定义的变量
//     * 可选字段，在智能体 prompt 中设置变量 {{key}} 后，通过此字段传入变量值
//     */
//    private Map<String, String> customVariables;

    /**
     * 附加信息
     * 可选字段，通常用于封装一些业务相关的字段
     */
    @JsonProperty("meta_data")
    private Map<String, Object> metaData;

    /**
     * 附加参数
     * 可选字段，通常用于特殊场景下指定一些必要参数，例如经纬度
     */
    @JsonProperty("extra_params")
    private Map<String, String> extraParams;

    /**
     * 是否debug模式
     */
    private boolean debug = false;
}
