package com.vos.kernel.business.dto;

import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 模型操作请求实体类
 *
 * <AUTHOR>
 * @date 2024年
 * @version: 1.0
 * @description: 模型上下架操作请求实体
 */
@Data
public class SyncPlatformModelRequest implements Serializable {


    /**
     * 模型标志
     */
    @NotBlank(message = "模型标志不能为空")
    private String taskTypeCode;


    /**
     * 模型上下架状态
     * 1: 上架，上面标的必传都需要传输
     * 0: 下架，只需要传modelId
     */
    @NotNull(message = "模型上下架状态不能为空")
    private Integer status;


    /**
     * od模型识别标签
     */
    private List<String> ddModelLabels;
}
