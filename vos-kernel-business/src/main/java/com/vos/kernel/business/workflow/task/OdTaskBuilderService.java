package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.ModelItemDTO;
import com.linker.omos.client.domain.request.OcrRequest;
import com.linker.omos.client.domain.request.OdModelRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.dto.AppConfigThirdContainsResponse;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.meta.OdAndAtrrDetModelMetaInfo;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.utils.StringUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class OdTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {
    @Resource
    Validator validator;

    public OdTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.OD.getCode());
    }

    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        OdModelRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, OdModelRequest.class);
        } catch (Exception e) {
            throw new BusinessException("od调用参数解析失败");
        }
        ModelItemDTO modelItem = CollectionUtil.getFirst(abilityRequest.getSubList());
        modelItem.setModel(taskAddRequest.getModel());
        Set<ConstraintViolation<OdModelRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        String inputType = abilityRequest.getInputType();
        if (abilityRequest.getVideoCode().length() > 64) {
            throw new DubboBaseException("设备标识长度不可超出64");
        }
        if (!"base64".equals(inputType) && !"image_url".equals(inputType)) {
            throw new BusinessException("输入类型只能是base64|image_url的一种");
        }
        if (StrUtil.isBlank(abilityRequest.getVideoCode())) {
            abilityRequest.setVideoCode("default");
        }
        if (abilityRequest.getSubList().size() != 1) {
            throw new BusinessException("od调用详细参数解析失败");
        }

        TaskTypeAndAbility taskAbility = getTaskAbility(modelItem.getModel(), workflowTaskTypeEnum);
        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        //获取高级配置
        List<AppConfigThirdContainsResponse> configList = getConfigList(modelItem.getConfigCode());
        //构造下发数据
        String callbackUrl = taskAddRequest.getCallbackUrl();
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.OD.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        //构造基础任务信息
        taskAdd.setSubList(buildTaskInfo(configList, abilityRequest, taskAbility, taskAdd.getAppSourceId()));

        //缓存元数据
        String image = BooleanUtil.isTrue(refluxProperties.getOpenV2Reflux()) ? abilityRequest.getInput() : "";
        OdAndAtrrDetModelMetaInfo odAndAtrrDetModelMetaInfo = new OdAndAtrrDetModelMetaInfo(abilityRequest.getVideoCode(), false, taskAbility.getAbilityName(), image, abilityRequest.getRegionInfo());
        odAndAtrrDetModelMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        odAndAtrrDetModelMetaInfo.setWorkflow(true);
        odAndAtrrDetModelMetaInfo.setModel(taskAbility.getActionId());
        odAndAtrrDetModelMetaInfo.setCallBackUrl(callbackUrl);
        odAndAtrrDetModelMetaInfo.setOrgCode(DubboDataContext.getAuthIdHolder());
        odAndAtrrDetModelMetaInfo.setEventTime(new Date().getTime());
        odAndAtrrDetModelMetaInfo.setEventTime(new Date().getTime());
        taskAdd.setMetaInfo(JSON.toJSONString(odAndAtrrDetModelMetaInfo));

        return taskAdd;
    }

    /**
     * 构造比对算法参数
     *
     * @param
     * @param ability
     * @return
     */
    private List<TaskSubDTO> buildTaskInfo(List<AppConfigThirdContainsResponse> modelCustomConfig, OdModelRequest abilityRequest, TaskTypeAndAbility ability, String appSourceId) {

        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(abilityRequest.getTaskOrder(), abilityRequest.getVideoClusterSource(), ability);
        //base64图片处理
        String s = handleImage(abilityRequest.getInputType(), abilityRequest.getInput(), appSourceId);
        abilityRequest.setInput(s);
        taskSubDTO.setRequestJson(buildRequestStr(modelCustomConfig, abilityRequest));
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }


    /**
     * 构造算法请求参数
     *
     * @param abilityRequest
     * @return
     */
    private String buildRequestStr(List<AppConfigThirdContainsResponse> modelCustomConfig, OdModelRequest abilityRequest) {

        String eventTime = DateUtil.getSimpleYMDHMS(LocalDateTime.now());
        String videoValidation = String.valueOf(IdWorker.nextId());
        String taskId = String.valueOf(IdWorker.nextId());
        AiExecuteImageParamDTO aiExecuteImageParamDTO = new AiExecuteImageParamDTO();
        aiExecuteImageParamDTO.setImageId("0");
        aiExecuteImageParamDTO.setVideoId("od");
        aiExecuteImageParamDTO.setSrcType(EmbeddingInputEnum.matchApiType(abilityRequest.getInputType()));
        aiExecuteImageParamDTO.setData(abilityRequest.getInput());
        aiExecuteImageParamDTO.setEventTime(eventTime);
        aiExecuteImageParamDTO.setTaskId(taskId);
        aiExecuteImageParamDTO.setOrgId(DubboDataContext.getAuthIdHolder());
        aiExecuteImageParamDTO.setVideoValidation(videoValidation);
        ModelItemDTO modelItem = CollectionUtil.getFirst(abilityRequest.getSubList());

        //高级配置参数
        JSONObject jsonObject = buildConfig(modelCustomConfig);
        jsonObject.put("displayAllBboxes", false);
        jsonObject.put("videoId", abilityRequest.getVideoCode());
        jsonObject.put("displayScore", true);
        jsonObject.put("boxEnlargeSize", 1);
        //置信度
        if (modelItem.getConf() != null && NumberUtil.isNumber(modelItem.getConf())) {
            jsonObject.put("classesThreshold", modelItem.getConf());
        }
        aiExecuteImageParamDTO.setKwargs(jsonObject);
        return buildModelRequestStr(ListUtil.of(aiExecuteImageParamDTO), taskId);
    }

}
