package com.vos.kernel.business.workflow;

import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.common.workflow.ScheduleStrategyService;
import com.vos.kernel.common.workflow.TaskBuilderHolder;
import com.vos.kernel.common.workflow.WorkflowTaskException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ScheduleStrategyProxy implements ScheduleStrategyService {

    @Resource
    TaskBuilderHolder taskBuilderHolder;

    @Resource
    WorkflowProperties workflowProperties;


    @Override
    public TaskAddResponse addTask(TaskAddRequest taskAddRequest) {
        WorkflowTaskTypeEnum.matchStrategyType(taskAddRequest.getTaskType());

        String bizMeta = taskAddRequest.getBizMeta();
        if (StrUtil.isNotBlank(bizMeta) && bizMeta.length() > workflowProperties.getBizMetaLength()) {
            throw new WorkflowTaskException("业务元数据长度不可超出：" + workflowProperties.getBizMetaLength());
        }

        return taskBuilderHolder.routeStrategy(taskAddRequest.getTaskType()).addTask(taskAddRequest);
    }
}

