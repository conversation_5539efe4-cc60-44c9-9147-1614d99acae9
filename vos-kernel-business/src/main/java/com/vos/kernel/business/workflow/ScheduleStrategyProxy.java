package com.vos.kernel.business.workflow;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.common.workflow.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年12月04日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ScheduleStrategyProxy implements ScheduleStrategyService {

    @Resource
    TaskBuilderHolder taskBuilderHolder;

    @Resource
    WorkflowProperties workflowProperties;


    @Override
    public TaskAddResponse addTask(TaskAddRequest taskAddRequest) {
        WorkflowTaskTypeEnum.matchStrategyType(taskAddRequest.getTaskType());

        String bizMeta = taskAddRequest.getBizMeta();
        if (StrUtil.isNotBlank(bizMeta)) {
            if (bizMeta.length() > workflowProperties.getBizMetaLength()) {
                throw new WorkflowTaskException("业务元数据长度不可超出：" + workflowProperties.getBizMetaLength());
            }
            try {
                JSONObject jsonObject = JSON.parseObject(bizMeta);
                String workflowInstanceId = jsonObject.getString("workflowInstanceId");
                String taskId = jsonObject.getString("taskId");
                Long taskTimeOut = jsonObject.getLong("taskTimeOut");
                taskAddRequest.setWorkflowInstanceId(workflowInstanceId);
                taskAddRequest.setConductorTaskId(taskId);
                if (null != taskTimeOut) {
                    taskAddRequest.setWaitTime(taskTimeOut.intValue() * 1000);
                }
            } catch (Exception e) {
                log.warn("业务元数据格式错误：{}", bizMeta);
            }
        }
        return taskBuilderHolder.routeStrategy(taskAddRequest.getTaskType()).addTask(taskAddRequest);
    }

    @Override
    public ConductorTaskQueueResponse getTaskQueue(ConductorTaskQueueRequest conductorTaskQueueRequest) {
        WorkflowTaskTypeEnum.matchStrategyType(conductorTaskQueueRequest.getTaskType());
        return taskBuilderHolder.routeStrategy(conductorTaskQueueRequest.getTaskType()).getTaskQueue(conductorTaskQueueRequest);
    }
}

