package com.vos.kernel.business.service.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.vos.kernel.business.dto.agent.*;

import java.util.List;

public interface IWorkflowManagementService {
    IPage<WorkflowInfoDTO> getWorkflowList(ListInfoQueryDTO queryDTO);

    void deleteWorkflow(SingleOperateDTO operateDTO);

    String createWorkflow(PackageCreateDTO createDTO);

    List<OperateResultDTO> stopWorkflow(BatchOperateDTO operateDTO);

    List<OperateResultDTO> startWorkflow(BatchOperateDTO operateDTO);

    String runWorkflow(WorkflowTriggerParamDTO triggerParamDTO);

    /**
     * 更新工作流配置
     *
     * @param updateDTO 更新参数
     */
    boolean updateWorkflowConfig(WorkflowConfigUpdateDTO updateDTO);
}
