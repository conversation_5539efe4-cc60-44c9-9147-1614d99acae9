package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.OcrRequest;
import com.linker.omos.client.domain.request.OvdModelRequest;
import com.linker.omos.client.domain.request.ReRankAbilityRequestDTO;
import com.linker.omos.client.domain.request.ReRankRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.conver.ApiConvertMapper;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ReRankTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Resource
    ApiConvertMapper apiConvertMapper;

    @Resource
    Validator validator;

    public ReRankTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.RE_RANK.getCode());
    }


    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        ReRankRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, ReRankRequest.class);
        } catch (Exception e) {
            throw new BusinessException("ocr调用参数解析失败");
        }
        abilityRequest.setModel(taskAddRequest.getModel());
        Set<ConstraintViolation<ReRankRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        TaskTypeAndAbility taskAbility = getTaskAbility(abilityRequest.getModel(), workflowTaskTypeEnum);

        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        abilityRequest.setModelId(taskAbility.getChatModelName());
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.RE_RANK.getKey());
        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setSubList(buildReRankTaskInfo(abilityRequest, taskAbility));

        //缓存元数据
        ModelMetaInfo embeddingMetaInfo = new ModelMetaInfo(taskAddRequest.getCallbackUrl(), DubboDataContext.getAuthIdHolder(), taskAbility.getActionId(), new Date().getTime());
        embeddingMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        embeddingMetaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(embeddingMetaInfo));
        return taskAdd;
    }

    /**
     * 构造文本向量化参数
     *
     * @param reRankRequest
     * @param abilityEntity
     * @return
     */
    private List<TaskSubDTO> buildReRankTaskInfo(ReRankRequest reRankRequest, TaskTypeAndAbility abilityEntity) {
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(reRankRequest.getTaskOrder(), reRankRequest.getVideoClusterSource(), abilityEntity);
        ReRankAbilityRequestDTO reRankAbilityRequestDTO = apiConvertMapper.reRankAbilityRequestConvert(reRankRequest);
        taskSubDTO.setRequestJson(JSON.toJSONString(reRankAbilityRequestDTO));
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }


}
