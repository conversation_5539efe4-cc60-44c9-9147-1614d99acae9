package com.vos.kernel.business.controller.chat;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.omagent.starter.manager.RedisStreamManager;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.constants.AgentosConstants;
import com.vos.kernel.business.dto.ChatDTO;
import com.vos.kernel.business.dto.StreamChatRequest;
import com.vos.kernel.business.dto.StreamChatRequestTest;
import com.vos.kernel.business.dto.StreamMessageDTO;
import com.vos.kernel.business.dto.agent.WorkflowDTO;
import com.vos.kernel.business.service.agentChat.IChatService;
import com.vos.kernel.business.service.agentChat.StreamEventHandler;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.ChatEventEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

import static com.vos.kernel.business.dto.agent.WorkflowDTO.WorkflowStatus.COMPLETED;

/**
 * <AUTHOR>
 * @Date 2024/12/9 11:01
 * @DESC: 聊天相关对外接口
 */
@RestController
@RequestMapping("api/chat")
public class ChatController {

    @Autowired
    private IChatService chatService;

    @Autowired
    private RedisStreamManager redisStreamManager;

    @Autowired
    private StreamEventHandler streamEventHandler;

    /**
     * 发起聊天
     *
     * @return
     */
    @Token
    @PostMapping(value = "/v1/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter stream(@RequestBody StreamChatRequest request) {
        return chatService.stream(request);
    }

    @PostMapping(value = "/v1/streamForK", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamForK(@RequestBody StreamChatRequest request,
                                 @RequestHeader("tenant_id") String tenantId,
                                 @RequestHeader("user_code") String userCode
                                 ) {
        DubboDataContext.setAuthIdHolder(tenantId);
        DubboDataContext.setUserHolder(userCode);
        return chatService.stream(request);
    }

    @PostMapping(value = "/v1/callback", produces = MediaType.APPLICATION_JSON_VALUE)
    public String callback(@RequestBody WorkflowDTO workflow) {
        if (workflow != null && !workflow.getStatus().isSuccessful() && workflow.getStatus().isTerminal()) {
            Map<String, Object> input = workflow.getInput();
            if (input != null && input.containsKey("message")) {
                Map<String, Object> message = (Map<String, Object>) input.get("message");
                if (message != null && message.containsKey("chatId")) {
                    String conversationId = (String) message.get("conversation_id");
                    /**
                     * completed事件处理
                     */
                    ChatDTO failedChatDTO = new ChatDTO();
                    failedChatDTO.setAgentId(message.get("agentId").toString());
                    failedChatDTO.setConversationId(conversationId);
                    failedChatDTO.setChatId(message.get("chatId").toString());
                    failedChatDTO.setStatus(ChatEventEnum.CHAT_FAILED.getStatus());
                    StreamMessageDTO failedStreamMessageDTO = new StreamMessageDTO();
                    failedStreamMessageDTO.setEvent(ChatEventEnum.CHAT_FAILED.getEvent());
                    failedStreamMessageDTO.setData(JSON.toJSONString(failedChatDTO));
                    streamEventHandler.eventHandler(failedStreamMessageDTO);

                    /**
                     * completed事件处理
                     */
                    ChatDTO doneChatDTO = new ChatDTO();
                    doneChatDTO.setAgentId(message.get("agentId").toString());
                    doneChatDTO.setConversationId(conversationId);
                    doneChatDTO.setChatId(message.get("chatId").toString());
                    doneChatDTO.setStatus(ChatEventEnum.DONE.getStatus());
                    StreamMessageDTO doneStreamMessageDTO = new StreamMessageDTO();
                    doneStreamMessageDTO.setEvent(ChatEventEnum.DONE.getEvent());
                    doneStreamMessageDTO.setData(JSON.toJSONString(doneChatDTO));
                    streamEventHandler.eventHandler(doneStreamMessageDTO);
                }
            }
        } else if (workflow != null && workflow.getStatus() == COMPLETED) {
            Map<String, Object> input = workflow.getInput();
            if (input != null && input.containsKey("message")) {
                Map<String, Object> message = (Map<String, Object>) input.get("message");
                if (message != null && message.containsKey("chatId")) {
                    String conversationId = (String) message.get("conversation_id");
                    /**
                     * completed事件处理
                     */
                    ChatDTO complatedChatDTO = new ChatDTO();
                    complatedChatDTO.setAgentId(message.get("agentId").toString());
                    complatedChatDTO.setConversationId(conversationId);
                    complatedChatDTO.setChatId(message.get("chatId").toString());
                    complatedChatDTO.setStatus(ChatEventEnum.CHAT_COMPLETED.getStatus());
                    StreamMessageDTO completedStreamMessageDTO = new StreamMessageDTO();
                    completedStreamMessageDTO.setEvent(ChatEventEnum.CHAT_COMPLETED.getEvent());
                    completedStreamMessageDTO.setData(JSON.toJSONString(complatedChatDTO));
                    streamEventHandler.eventHandler(completedStreamMessageDTO);

                    /**
                     * completed事件处理
                     */
                    ChatDTO doneChatDTO = new ChatDTO();
                    doneChatDTO.setAgentId(message.get("agentId").toString());
                    doneChatDTO.setConversationId(conversationId);
                    doneChatDTO.setChatId(message.get("chatId").toString());
                    doneChatDTO.setStatus(ChatEventEnum.DONE.getStatus());
                    StreamMessageDTO doneStreamMessageDTO = new StreamMessageDTO();
                    doneStreamMessageDTO.setEvent(ChatEventEnum.DONE.getEvent());
                    doneStreamMessageDTO.setData(JSON.toJSONString(doneChatDTO));
                    streamEventHandler.eventHandler(doneStreamMessageDTO);
                }
            }
        }
        return "success";
    }

    /**
     * 发起聊天
     *
     * @return
     */
    @Token
    @PostMapping(value = "/v1/askComplete")
    public BaseResp<Void> askComplete(@RequestBody StreamChatRequest request) {
        chatService.askComplete(request);
        return new BaseResp<>();
    }

    /**
     * 发起聊天
     *
     * @return
     */
    @PostMapping(value = "/v1/streamTest", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamTest(@RequestBody StreamChatRequestTest request) {
        return chatService.stream(request);
    }

    @Deprecated
    @RequestMapping("/sendMsg")
    public BaseResp<Void> sendMsg(@RequestBody JSONObject jsonObject) {
        Map<String, String> map = new HashMap<>();
        map.put("content", jsonObject.getJSONObject("content").toJSONString());
        String streamKey = AgentosConstants.getRedisStreamKey(jsonObject.getString("chatId"));
        redisStreamManager.sendToStream(streamKey, JSON.toJSONString(map));
        return new BaseResp<>();
    }
}
