package com.vos.kernel.business.service.comparison;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.response.ComparisonDetailItemResponse;
import com.linker.omos.client.domain.response.ComparisonDetailResponse;
import com.linker.omos.client.domain.response.ComparisonResponse;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;
import com.vos.kernel.business.api.WorkflowApiService;
import com.vos.kernel.business.config.DisruptorProperties;
import com.vos.kernel.business.disruptor.EventListener;
import com.vos.kernel.business.disruptor.ParallelQueue<PERSON>andler;
import com.vos.kernel.business.dto.*;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.service.SyncResult;
import com.vos.kernel.business.service.common.SyncWaitResultService;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.vos.kernel.common.constant.RedisKey;
import com.vos.kernel.common.dubbo.IComparisonBusinessCallBackService;
import com.vos.kernel.common.entity.ComparisonItemResponse;
import com.linker.omos.client.domain.workflow.callback.TaskCallExceptionDTO;
import com.vos.kernel.common.meta.ComparisonModelMetaInfo;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.support.MessageBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年07月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@DubboService
@Resource
public class ComparisonAbilityCallBackServiceImpl implements IComparisonBusinessCallBackService {

    @Resource
    SyncWaitResultService syncWaitResultService;

    @Resource
    ICacheService cacheService;

    @Resource
    IComparisonAbilityService comparisonAbilityService;

    @Resource
    RocketMQTemplate rocketMqTemplate;

    @Resource
    WorkflowApiService workflowApiService;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "comparison-queue-";

    @Resource
    DisruptorProperties disruptorProperties;

    /**
     * disruptor处理器
     */
    private ParallelQueueHandler<TaskCallBackDto> parallelQueueHandler;

    /**
     * 构造器
     */
    public ComparisonAbilityCallBackServiceImpl() {
        ParallelQueueHandler.Builder<TaskCallBackDto> builder = new ParallelQueueHandler.Builder<TaskCallBackDto>()
                .setThreads(Runtime.getRuntime().availableProcessors())
                .setProducerType(ProducerType.MULTI)
                .setNamePrefix(THREAD_NAME_PREFIX)
                .setWaitStrategy(new BlockingWaitStrategy());

        BatchEventListenerProcessor batchEventListenerProcessor = new BatchEventListenerProcessor();
        builder.setListener(batchEventListenerProcessor);
        this.parallelQueueHandler = builder.build();
    }

    @PostConstruct
    public void init() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.start();
        }
    }

    @PreDestroy
    public void destroy() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.shutDown();
        }
    }

    @Override
    public void comparisonCallBack(TaskCallBackDto taskCallBackDto) {
        if (BooleanUtil.isTrue(disruptorProperties.getOpenComparison())) {
            if (this.parallelQueueHandler != null) {
                this.parallelQueueHandler.add(taskCallBackDto);
            } else {
                log.error("embedding回调异常，parallelQueueHandler未初始化");
            }
        } else {
            comparisonCallBackDo(taskCallBackDto);
        }
    }

    /**
     * 批量事件监听
     */
    public class BatchEventListenerProcessor implements EventListener<TaskCallBackDto> {

        @Override
        public void onEvent(TaskCallBackDto event) {
            if (null != event) {
                comparisonCallBackDo(event);
            }
        }

        @Override
        public void onException(Throwable ex, long sequence, TaskCallBackDto event) {
            log.error("comparison 请求写回失败，request:{},errMsg:{} ", event, ex.getMessage(), ex);
            comparisonCallBackDo(event);
        }
    }


    /**
     * 真实回调处理
     *
     * @param taskCallBackDto
     */
    public void comparisonCallBackDo(TaskCallBackDto taskCallBackDto) {
        Long abilityId = taskCallBackDto.getAbilityId();
        String appSourceId = taskCallBackDto.getAppSourceId();
        TaskCallExceptionDTO exception = taskCallBackDto.getException();
        if (log.isDebugEnabled()) {
            log.debug("appSourceId：{},comparison接收回调,是否异常：{}", appSourceId, exception);
        }
        //是否为异步请求
        ComparisonModelMetaInfo metaInfo = StrUtil.isNotBlank(taskCallBackDto.getMetaInfo()) ?
                JSON.parseObject(taskCallBackDto.getMetaInfo(), ComparisonModelMetaInfo.class) : comparisonAbilityService.getMetaInfo(appSourceId);
        if (metaInfo == null) {
            log.warn("comparison回调超时！appSourceId：{}", appSourceId);
            return;
        }
        String callBackUrl = metaInfo.getCallBackUrl();
        ComparisonResponse comparisonResponse = new ComparisonResponse();
        comparisonResponse.setTook(new Date().getTime() - metaInfo.getEventTime());
        comparisonResponse.setModel(metaInfo.getModel());
        comparisonResponse.setSourceId(appSourceId);
        //是否异常
        if (exception != null) {
            comparisonResponse.setCode(Integer.valueOf(exception.getCode()));
            comparisonResponse.setError(exception.getMessage());
        } else {
            comparisonResponse.setCode(200);
            //设置结果
            ComparisonDetailResponse comparisonDetailResponse = buildComparisonResult(taskCallBackDto, metaInfo);
            comparisonResponse.setData(comparisonDetailResponse);
        }

        //异步回调
        if (StrUtil.isNotBlank(callBackUrl)) {
            if (BooleanUtil.isTrue(metaInfo.getWorkflow())) {
                //工作流返回
                WorkflowCallBackContent workflowCallBackContent = new WorkflowCallBackContent();
                if (StrUtil.isNotBlank(metaInfo.getBizMeta()) && metaInfo.getBizMeta().contains("{")) {
                    workflowCallBackContent.setTransmissionParams(com.alibaba.fastjson2.JSON.parseObject(metaInfo.getBizMeta()));
                }
                WorkflowCallBackContentItem workflowCallBackContentItem = new WorkflowCallBackContentItem();
                workflowCallBackContentItem.setSourceId(appSourceId);
                workflowCallBackContentItem.setStartTime(metaInfo.getEventTime());

                workflowCallBackContentItem.setStatus(exception != null ? WorkflowStatusEnum.FAILED : WorkflowStatusEnum.COMPLETED);
                if (exception != null) {
                    workflowCallBackContentItem.setReasonForIncompletion(exception.getMessage());
                }
                workflowCallBackContentItem.setOutputData(comparisonResponse);
                workflowCallBackContent.setTaskResult(workflowCallBackContentItem);
                workflowApiService.batchRequestWorkflowEngine(callBackUrl, ListUtil.of(workflowCallBackContent));
            } else {
                rocketMqTemplate.syncSend(MqConstants.COMPARISON_CALLBACK_TOPIC, MessageBuilder.withPayload(new ComparisonAsyncBack(callBackUrl, comparisonResponse)).build());
            }
        } else {
            //同步返回数据
            SyncResult syncFuture = syncWaitResultService.getSyncFuture(RedisKey.DATA_TO_COMPARION + appSourceId);
            if (null != syncFuture) {
                syncFuture.setSyncResult(appSourceId, comparisonResponse);
            }
        }

        //统计计数
//        if (BooleanUtil.isTrue(metricProperties.getOpenCallMetric())) {
//            TaskTypeEntity abilityByTaskAbilityId = cacheService.getAbilityByTaskAbilityId(abilityId.toString());
//            if (null != abilityByTaskAbilityId) {
//                apiExecuteCountService.incrementApiAbilitySuccessExecuteNum(metaInfo.getOrgCode(), abilityByTaskAbilityId.getActionId(), abilityByTaskAbilityId.getAbilityEnum(), 1L);
//            }
//        }
//        if (BooleanUtil.isTrue(metricProperties.getOpenMetric())) {
//            taskCacheRpcService.addAbilityTaskCount(abilityId, AppTaskCountEnum.CALLBACK_COUNT.getCode(), 1L);
//        }
    }


    /**
     * 构造返回值
     *
     * @param taskCallBackDto
     * @return
     */
    private ComparisonDetailResponse buildComparisonResult(TaskCallBackDto taskCallBackDto, ComparisonModelMetaInfo metaInfo) {
        ComparisonDetailResponse comparisonDetailResponse = new ComparisonDetailResponse();
        comparisonDetailResponse.setVideoCode(metaInfo.getVideoCode());
        comparisonDetailResponse.setModel(metaInfo.getModel());
        ArrayList<ComparisonDetailItemResponse> comparisonDetailItemResponses = new ArrayList<>();

        List<ComparisonItemResponse> requestParseResult = JSONArray.parseArray(JSON.toJSONString(taskCallBackDto.getRequestParseResult()), ComparisonItemResponse.class);

        if (CollectionUtil.isNotEmpty(requestParseResult)) {
            //最大的匹配值
            long abilityComparisonMatchMaxId = cacheService.getAbilityComparisonMatchMaxId();

            List<String> comparisonIds = metaInfo.getComparisonId();
            for (ComparisonItemResponse first : requestParseResult) {
                if (StrUtil.isNotBlank(first.getMatch_id()) && Long.parseLong(first.getMatch_id()) <= abilityComparisonMatchMaxId) {
                    ComparisonMatchDTO abilityComparisonMatch = cacheService.getAbilityComparisonMatch(first.getMatch_id());
                    log.info("comparison 回调数据：{},abilityComparisonMatch:{},comparisonIds:{}", JSON.toJSONString(first), abilityComparisonMatch, comparisonIds);
                    if (null != abilityComparisonMatch) {
                        if (CollectionUtil.isNotEmpty(comparisonIds)) {
//                            if (comparisonIds.contains(abilityComparisonMatch.getGroupId().toString())) {
//                                comparisonDetailItemResponses.add(setValue(first, abilityComparisonMatch));
//                            }
                            comparisonDetailItemResponses.add(setValue(first, abilityComparisonMatch));
                        } else {
                            comparisonDetailItemResponses.add(setValue(first, abilityComparisonMatch));
                        }
                    } else {
                        comparisonDetailItemResponses.add(setValue(first, null));
                    }
                } else {
                    comparisonDetailItemResponses.add(setValue(first, null));
                }
            }
        }
        comparisonDetailResponse.setBboxList(comparisonDetailItemResponses);
        return comparisonDetailResponse;
    }


    /**
     * 设置值
     *
     * @param first
     * @param abilityComparisonMatch
     */
    private ComparisonDetailItemResponse setValue(ComparisonItemResponse first, ComparisonMatchDTO abilityComparisonMatch) {
        ComparisonDetailItemResponse comparisonDetailItemResponse = new ComparisonDetailItemResponse();

        comparisonDetailItemResponse.setConf(CollectionUtil.getFirst(first.getConf()));
        comparisonDetailItemResponse.setMatchConf(first.getMatch_conf());
        comparisonDetailItemResponse.setMatchId(first.getMatch_id());
        comparisonDetailItemResponse.setStatus(first.getStatus());
        comparisonDetailItemResponse.setBbox(first.getBbox());
        comparisonDetailItemResponse.setComparisonId(abilityComparisonMatch == null ? 0L : abilityComparisonMatch.getGroupId());
        comparisonDetailItemResponse.setComparisonName(abilityComparisonMatch == null ? "" : abilityComparisonMatch.getGroupName());
        comparisonDetailItemResponse.setMatchUrl(abilityComparisonMatch == null ? "" : abilityComparisonMatch.getImageData());
        comparisonDetailItemResponse.setBusinessImageId(abilityComparisonMatch == null ? "" : abilityComparisonMatch.getBusinessImageId());
        return comparisonDetailItemResponse;
    }
}
