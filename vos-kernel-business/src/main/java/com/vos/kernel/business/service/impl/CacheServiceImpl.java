package com.vos.kernel.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.business.dto.ComparisonGroupResponse;
import com.vos.kernel.business.dto.ComparisonMatchDTO;
import com.vos.kernel.business.mapper.ComparisonGroupMapper;
import com.vos.kernel.business.mapper.TaskTypeMapper;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.vo.TypeInventedSub;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.core.api.domain.AbilityEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.DataRefluxDTO;
import com.vos.kernel.data.api.model.req.SaveInventedSub;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/6
 * @description: com.vos.kernel.business.service.impl
 */
@Service
@Slf4j
public class CacheServiceImpl implements ICacheService {

    @Resource
    TaskTypeMapper taskTypeMapper;


    @Resource
    ComparisonGroupMapper comparisonGroupMapper;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    /**
     * chat回流key
     */
    public static final String CHAT_FLUX = "chat_flux:";

    @Override
    @Cached(name = "cache:v3TaskTypeEntityCache", expire = 300, cacheType = CacheType.BOTH)
    public TaskTypeEntity getUserV3Ability(String orgCodeActionId) {
        List<String> split = StrUtil.split(orgCodeActionId, '#');
        String orgCode = split.get(0);
        String actionId = split.get(1);
        if (StrUtil.isBlank(actionId)) {
            actionId = "V300101000";
        }
        return taskTypeMapper.getUserV3Ability(orgCode, actionId);
    }

    @Override
    @CacheInvalidate(name = "cache:v3TaskTypeEntityCache")
    public void deleteUserV3Ability(String orgCodeActionId) {
        log.info("删除缓存v3TaskTypeEntityCache：{}", orgCodeActionId);
    }

    @Override
    @CacheInvalidate(name = "cache:v35TaskTypeEntityCache")
    public void deleteUserV35Ability(String orgCodeActionId) {
        log.info("删除缓存v35TaskTypeEntityCache：{}", orgCodeActionId);
    }

    @Override
    @CacheInvalidate(name = "cache:v2TaskTypeActionIdCache-")
    public void deleteActionIdByCode(String orgCode) {
        log.info("删除缓存v2TaskTypeActionIdCache：{}", orgCode);
    }

    @Override
    @CacheInvalidate(name = "cache:v2FileTaskUploadCache-")
    public void deleteActionIdByUser(String orgCode) {
        log.info("删除缓存v2FileTaskUploadCache：{}", orgCode);
    }

    @Override
    @CacheInvalidate(name = "cache:userTaskTypeEntityCache")
    public void deleteUserAbility(String orgCodeActionId) {
        log.info("删除缓存userTaskTypeEntityCache：{}", orgCodeActionId);
    }


    @Override
    @Cached(name = "cache:getAbilityByTaskAbilityId", expire = 60, cacheType = CacheType.BOTH)
    public TaskTypeEntity getAbilityByTaskAbilityId(String abilityId) {
        return taskTypeMapper.getAbilityByTaskAbilityId(abilityId);
    }

    @Override
    public DataRefluxDTO getChatDataReflux(String appSourceId) {
        String chatFluxKey = CHAT_FLUX + appSourceId;
        try {
            RedisScript<String> script = new DefaultRedisScript<>(
                    "local value = redis.call('GET', KEYS[1])\n" +
                            "redis.call('DEL', KEYS[1])\n" +
                            "return value",
                    String.class);

            String execute = stringRedisTemplate.execute(script, Collections.singletonList(chatFluxKey));

            return JSON.parseObject(execute, DataRefluxDTO.class);
        } catch (Exception e) {
            stringRedisTemplate.delete(chatFluxKey);
            return null;
        }

    }

    @Override
    public void setChatDataReflux(String appSourceId, DataRefluxDTO dataReflux, Integer expireLong) {
        String chatFluxKey = CHAT_FLUX + appSourceId;
        stringRedisTemplate.opsForValue().set(chatFluxKey, JSONObject.toJSONString(dataReflux), expireLong, TimeUnit.MILLISECONDS);
    }

    @Override
    @Cached(name = "cache:v35TaskTypeEntityCache", expire = 300, cacheType = CacheType.BOTH)
    public TaskTypeEntity getUserV35Ability(String orgCodeActionId) {
        List<String> split = StrUtil.split(orgCodeActionId, '#');
        String orgCode = split.get(0);
        String actionId = split.get(1);
        if (StrUtil.isBlank(actionId)) {
            actionId = "V350101000";
        }
        return taskTypeMapper.getUserV35Ability(orgCode, actionId);
    }

    @Override
    @Cached(name = "cache:v2TaskTypeActionIdCache-", expire = 60, cacheType = CacheType.BOTH)
    public List<TypeInventedSub> getActionIdByCode(String orgCode) {
        List<TypeInventedSub> typeInventedSubs = taskTypeMapper.actionIdByCode(orgCode);
        return typeInventedSubs;
    }

    @Override
    @Cached(name = "cache:v2FileTaskUploadCache-", expire = 60, cacheType = CacheType.BOTH)
    public List<SaveInventedSub> getActionIdByUser(String orgCode) {
        log.debug("cache:v2FileTaskUploadCache-中没有，走sql====");
        return taskTypeMapper.actionIdByUser(orgCode);
    }

    @Override
    @Cached(name = "cache:userTaskTypeEntityCache", expire = 300, cacheType = CacheType.BOTH)
    public TaskTypeEntity getUserAbility(String orgCodeActionId) {
        List<String> split = StrUtil.split(orgCodeActionId, '#');
        String orgCode = split.get(0);
        String actionId = split.get(1);
        String rejectActionId = actionId + "-" + DubboDataContext.getAppKeyHolder();
        return taskTypeMapper.getUserAbility(orgCode, actionId, rejectActionId);
    }

    @Override
    @Cached(name = "cache:userAbilityIdCache", expire = 300, cacheType = CacheType.BOTH)
    public Long getAbilityId(String taskTypeCode) {
        return taskTypeMapper.getAbilityId(taskTypeCode);
    }

    @Override
    @Cached(name = "cache:TaskTypeEntityCache", expire = 300, cacheType = CacheType.BOTH)
    public TaskTypeEntity getUserDynamicAbility(String orgCode, String actionId, String type) {
        if (StrUtil.isBlank(actionId)) {
            actionId = "V350101000";
        }
        return taskTypeMapper.getUserDynamicAbility(orgCode, actionId);
    }

    @Override
    @Cached(name = "cache:comparisonGroupCache", expire = 300, cacheType = CacheType.BOTH)
    public List<ComparisonGroupResponse> getAbilityComparisonGroup(String taskTypeCodeAndBizType) {
        List<String> split = StrUtil.split(taskTypeCodeAndBizType, '#');
        String taskTypeCode = split.get(0);
        String bizType = split.get(1);
        List<ComparisonGroupResponse> comparisonGroupResponses = comparisonGroupMapper.comparisonGroupList(taskTypeCode, Integer.valueOf(bizType), null, null);

        return CollectionUtil.isNotEmpty(comparisonGroupResponses) ? comparisonGroupResponses : new ArrayList<>();
    }

    @Override
    @CacheInvalidate(name = "cache:comparisonGroupCache")
    public void deleteAbilityComparisonGroupCache(String taskTypeCodeAndBizType) {
        log.info("删除缓存ComparisonGroupCache：{}", taskTypeCodeAndBizType);
    }

    @Override
    @Cached(name = "cache:comparisonResCache", expire = 30000, cacheType = CacheType.BOTH)
    public ComparisonMatchDTO getAbilityComparisonMatch(String matchId) {

        return comparisonGroupMapper.getAbilityComparisonMatch(matchId);
    }

    @Override
    @Cached(name = "cache:comparisonMatchMaxIdCache", expire = 300, cacheType = CacheType.BOTH)
    public long getAbilityComparisonMatchMaxId() {
        Long abilityComparisonMatchMaxId = comparisonGroupMapper.getAbilityComparisonMatchMaxId();
        return abilityComparisonMatchMaxId == null ? 0 : abilityComparisonMatchMaxId;
    }

    @Override
    @CacheInvalidate(name = "cache:comparisonMatchMaxIdCache")
    public void deleteAbilityComparisonMatchMaxId() {
        log.info("删除缓存comparisonMatchMaxIdCache");
    }


//    @Override
//
//    public AbilityEntity getAbilityEntity(String abilityId) {
//        QueryWrapper<AbilityEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(AbilityEntity::getAbilityId, abilityId).eq(AbilityEntity::getDel, 0).last("limit 1");
//        return this.getOne(queryWrapper);
//    }


    @Override
    @Cached(name = "cache:abilityEntityByTypeCache-", expire = 300, cacheType = CacheType.BOTH)
    public TaskTypeAndAbility getAbilityEntityCache(String orgCodeActionId) {
        List<String> split = StrUtil.split(orgCodeActionId, '#');
        String orgCode = split.get(0);
        String actionId = split.get(1);
        String rejectActionId = actionId + "-" + DubboDataContext.getAppKeyHolder();
        return taskTypeMapper.getAbilityEntity(orgCode, actionId, rejectActionId);
    }

    @Override
    @CacheInvalidate(name = "cache:abilityEntityByTypeCache-")
    public void deleteAbilityEntityCache(String orgCodeActionId) {
        log.info("删除缓存abilityEntityByTypeCache：{}", orgCodeActionId);
    }
}
