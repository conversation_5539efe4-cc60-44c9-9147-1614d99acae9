package com.vos.kernel.business.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * agent信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@TableName("tb_agent_info")
public class AgentInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "agent_id", type = IdType.AUTO)
    private String agentId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 智能体模式,1:基于工作流的智能体，2:交互式智能体
     */
    @TableField("agent_type")
    private Integer agentType;

    /**
     * 图标路径
     */
    @TableField("icon_path")
    private String iconPath;

    /**
     * 配置包路径
     */
    @TableField("config_path")
    private String configPath;

    /**
     * agent名称
     */
    @TableField("agent_name")
    private String agentName;

    /**
     * agent版本字段
     */
    @TableField("agent_version")
    private String agentVersion;

    /**
     * agent描述
     */
    @TableField("description")
    private String description;

    /**
     * agent排序字段，值越小，排序越靠前
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 最后启动时间
     */
    @TableField("last_start_time")
    private LocalDateTime lastStartTime;

    /**
     * 删除标志, 0未删除，1删除
     */
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 0:停止，1:运行
     */
    @TableField("running")
    private Integer running;

    /**
     * 运行工作流ID
     */
    @TableField("workflow")
    private String workflow;

    /**
     * 运行工作流版本
     */
    @TableField("workflow_version")
    private String workflowVersion;

    /**
     * 触发器配置信息, json格式
     */
    @TableField("triggers")
    private String triggers;

    /**
     * 模型信息, json格式
     */
    @TableField("model")
    private String model;

    /**
     * 引导语, json格式
     */
    @TableField("pre_chat")
    private String preChat;

    /**
     * 推荐问题, json格式
     */
    @TableField("suggest")
    private String suggest;


}
