package com.vos.kernel.business.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * agent信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@TableName("tb_agent_info")
public class AgentInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "agent_id", type = IdType.AUTO)
    private String agentId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 智能体模式,1:基于工作流的智能体，2:交互式智能体, 3:k0基于模板的智能体 5:系统跳转智能体 6, "新知识智能体" 7, "培训智能体" 8, "哨兵智能体"
     */
    @TableField("agent_type")
    private Integer agentType;

    /**
     * 图标路径
     */
    @TableField("icon_path")
    private String iconPath;

    /**
     * 配置包路径
     */
    @TableField("config_path")
    private String configPath;

    /**
     * agent名称
     */
    @TableField("agent_name")
    private String agentName;

    /**
     * agent版本字段
     */
    @TableField("agent_version")
    private String agentVersion;

    /**
     * agent描述
     */
    @TableField("description")
    private String description;

    /**
     * agent排序字段，值越小，排序越靠前
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 最后启动时间
     */
    @TableField("last_start_time")
    private LocalDateTime lastStartTime;

    /**
     * 删除标志, 0未删除，1删除
     */
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 0:停止，1:运行
     */
    @TableField("running")
    private Integer running;

    /**
     * 运行工作流ID
     */
    @TableField("workflow")
    private String workflow;

    /**
     * 运行工作流版本
     */
    @TableField("workflow_version")
    private String workflowVersion;

    /**
     * 触发器配置信息, json格式
     */
    @TableField("triggers")
    private String triggers;

    /**
     * 模型信息, json格式
     */
    @TableField("model")
    private String model;

    /**
     * 引导语, json格式
     */
    @TableField("pre_chat")
    private String preChat;

    /**
     * 推荐问题, json格式
     */
    @TableField("suggest")
    private String suggest;


    /**
     * 模板智能体参数
     */
    @TableField("template_params")
    private String templateParams;


    /**
     * 模板智能体类型,1:大模型，2:od+大模型, 3:多od 4:多od+协同
     */
    @TableField("operate_type")
    private Integer operateType;

    /**
     * 是否官方推荐
     */
    private Boolean isOfficial;

    /**
     * 是否授权所有用户
     */
    private Boolean authorizeAll;


    /**
     * 官方推荐顺序
     */
    private Integer officialOrder;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * icon背景
     */
    private String iconLayer;

    /**
     * 厂家标签
     */
    private String sourceManufacturer;

    /**
     * 标签
     */
    private String tagList;

    /**
     * 智能体调用次数
     */
    private Long appCallNum;

    /**
     * 知识智能体映射ID
     */
    private Integer knowledgeAgentId;


    /**
     * 智能体配置参数： AI云盘配置&指令配置信息
     */
    @TableField("agent_config")
    private String agentConfig;

    /**
     * 体验模式下，会话ID
     */
    @TableField("conversation_id")
    private String conversationId;
}
