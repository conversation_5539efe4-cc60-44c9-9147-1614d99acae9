package com.vos.kernel.business.conver.agent;

import com.alibaba.fastjson2.JSON;
import com.vos.kernel.business.dto.agent.AgentConfig;
import com.vos.kernel.business.dto.agent.AgentDetailInfoDTO;
import com.vos.kernel.business.dto.agent.AgentInfoDTO;
import com.vos.kernel.business.dto.agent.EventTriggerInfoDTO;
import com.vos.kernel.business.entity.AgentInfoEntity;
import com.vos.kernel.business.entity.EventTriggerInfoEntity;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Value;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public abstract class IAgentManagementMapper {

    @Value("${event.trigger.inner.endpoint:http://vos-kernel-business.kernel:8181}")
    private String eventTriggerInnerEndpoint;

    @Value("${event.trigger.outer.endpoint:http://localhost:8081}")
    private String eventTriggerOuterEndpoint;

    private static final String EVENT_TRIGGER_PATH = "/trigger/v1/webhook/agentos/hook/";

    public abstract AgentInfoDTO toDTO(AgentInfoEntity agent);

    public AgentDetailInfoDTO toDetailDTO(AgentInfoEntity agent) {
        String preChat = agent.getPreChat();
        // preChat的内容为{"welcomeMessage": "欢迎来到中国", "presetQuestions": [{"enable": true, "question": "美国是哪里"}, {"enable": true, "question": "中国是哪里"}], "presetQuestionType": 1, "enablePresetQuestions": true}
        AgentConfig.PreChatInfo preChatConfig = JSON.parseObject(preChat, AgentConfig.PreChatInfo.class);
        List<String> presetQuestions = preChatConfig.getPresetQuestions().stream().filter(AgentConfig.PresetQuestion::getEnable).map(AgentConfig.PresetQuestion::getQuestion).collect(Collectors.toList());


        AgentDetailInfoDTO dto = new AgentDetailInfoDTO();
        dto.setAgentId(agent.getAgentId());
        dto.setAgentName(agent.getAgentName());
        dto.setTenantId(agent.getTenantId());
        dto.setCreateTime(agent.getCreateTime());
        dto.setUpdateTime(agent.getUpdateTime());
        dto.setLastStartTime(agent.getLastStartTime());
        dto.setRunning(agent.getRunning());
        dto.setWelcome(preChatConfig.getWelcomeMessage());
        dto.setPresetQuestions(presetQuestions);
        dto.setIconPath(agent.getIconPath());
        dto.setDescription(agent.getDescription());
        return dto;
    }

    /**
     * 将事件触发器实体转换为DTO
     * @param trigger 事件触发器实体
     * @param innerEndpoint 内部端点
     * @param outerEndpoint 外部端点
     * @return 事件触发器DTO
     */
    public EventTriggerInfoDTO toDTO(EventTriggerInfoEntity trigger) {
        EventTriggerInfoDTO dto = new EventTriggerInfoDTO();
        dto.setId(trigger.getTriggerId());
        dto.setAgentId(trigger.getAgentId());
        dto.setName(trigger.getTriggerName());
        dto.setToken(trigger.getToken());
        dto.setParamsSchema(trigger.getOutputSchema());
        dto.setInnerUrl(String.format("%s%s%s", eventTriggerInnerEndpoint, EVENT_TRIGGER_PATH, trigger.getTriggerId()));
        dto.setOuterUrl(String.format("%s%s%s", eventTriggerOuterEndpoint, EVENT_TRIGGER_PATH, trigger.getTriggerId()));

        AgentConfig.Action action = JSON.parseObject(trigger.getAction(), AgentConfig.Action.class);
        if (action == null || action.getWorkflowAction() == null) {
            return dto;
        }

        dto.setWorkflowId(action.getWorkflowAction().getWorkflowId());
        dto.setWorkflowVersion(String.valueOf(action.getWorkflowAction().getVersion()));
        dto.setWorkflowParams(action.getWorkflowAction().getParams());

        return dto;
    }
}
