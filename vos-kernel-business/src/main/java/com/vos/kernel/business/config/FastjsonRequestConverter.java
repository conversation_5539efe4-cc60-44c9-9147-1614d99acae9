package com.vos.kernel.business.config;

import com.alibaba.fastjson2.JSON;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import retrofit2.Converter;


/**
 * @Description 实现converter接口，重写convert方法，用于将相应的请求参数类型转化为RequestBody类型
 * @Param T
 * @return RequestBody
 **/
public class FastjsonRequestConverter<T> implements Converter<T, RequestBody> {
    private static final MediaType MEDIA_TYPE = MediaType.get("application/json; charset=UTF-8");

    public RequestBody convert(T value) {
        return RequestBody.create(MEDIA_TYPE, JSON.toJSONBytes(value));
    }
}
