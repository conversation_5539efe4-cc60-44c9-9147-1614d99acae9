package com.vos.kernel.business.workflow.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.exception.BusinessException;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.AttrDetRequest;
import com.linker.omos.client.domain.request.ComparisonRequest;
import com.linker.omos.client.domain.request.OcrRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.linker.omos.client.domain.workflow.TaskSubDTO;
import com.vos.kernel.business.dto.AppConfigThirdContainsResponse;
import com.vos.kernel.business.dto.ComparisonGroupResponse;
import com.vos.kernel.business.enums.EmbeddingInputEnum;
import com.vos.kernel.business.service.ICacheService;
import com.vos.kernel.business.service.appconfig.IModelConfigDesignService;
import com.vos.kernel.business.service.common.SyncWaitResultService;
import com.vos.kernel.common.cache.TaskTypeAndAbility;
import com.vos.kernel.common.common.constant.ScheduleConstants;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.meta.ModelMetaInfo;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.common.utils.ProcessUtils;
import com.vos.kernel.common.workflow.TaskAddBuilderService;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class ComparisonTaskBuilderService extends TaskAddBaseBuilder implements TaskAddBuilderService {

    @Resource
    IModelConfigDesignService modelConfigDesignService;

    @Resource
    ICacheService cacheService;

    @Resource
    Validator validator;

    public ComparisonTaskBuilderService() {
        this.type = Collections.singletonList(WorkflowTaskTypeEnum.COMPARE.getCode());
    }


    @Override
    public TaskAddDTO build(TaskAddRequest taskAddRequest, WorkflowTaskTypeEnum workflowTaskTypeEnum) {
        String parameters = taskAddRequest.getParameters();
        ComparisonRequest abilityRequest;
        try {
            abilityRequest = JSON.parseObject(parameters, ComparisonRequest.class);
            abilityRequest.setModel(taskAddRequest.getTaskName());
        } catch (Exception e) {
            throw new BusinessException("comparison调用参数解析失败");
        }
        abilityRequest.setModel(taskAddRequest.getModel());
        Set<ConstraintViolation<ComparisonRequest>> validate = validator.validate(abilityRequest);
        if (CollectionUtil.isNotEmpty(validate)) {
            throw new BusinessException(validate.iterator().next().getMessage());
        }
        String inputType = abilityRequest.getInputType();
        if (!"base64".equals(inputType) && !"image_url".equals(inputType)) {
            throw new BusinessException("comparison输入类型只能是base64|image_url的一种");
        }

        TaskTypeAndAbility taskAbility = getTaskAbility(abilityRequest.getModel(), workflowTaskTypeEnum);
        List<ComparisonGroupResponse> comparisonGroupList = cacheService.getAbilityComparisonGroup(taskAbility.getTaskTypeCode() + "#" + abilityRequest.getBusinessType());
        //验证样本库是否存在数据
        if (CollectionUtil.isEmpty(comparisonGroupList)) {
            throw new BusinessException("参数错误，comparisonId未查询到数据");
        }
        List<String> comparisonId = abilityRequest.getComparisonId();
        List<ComparisonGroupResponse> collect = comparisonGroupList.stream().filter(t -> comparisonId.contains(t.getComparisonId())).collect(Collectors.toList());
        if (comparisonId.size() != collect.size()) {
            throw new BusinessException("参数错误，comparisonId不存在");
        }
        //获取配置信息
        List<AppConfigThirdContainsResponse> modelCustomConfig = new ArrayList<>();
        if (StrUtil.isNotBlank(abilityRequest.getConfigCode())) {
            modelCustomConfig = modelConfigDesignService.getModelCustomConfig(abilityRequest.getConfigCode());
        }
        if (log.isDebugEnabled()) {
            String params = ProcessUtils.logData(abilityRequest);
            log.debug("workflow下发{}任务,参数：{}", taskAddRequest.getTaskName(), params);
        }
        TaskAddDTO taskAdd = new TaskAddDTO();
        taskAdd.setWaitTime(taskAddRequest.getWaitTime());
        taskAdd.setCallbackType(RpcCallBackInfoEnum.COMPARISON.getKey());
//        taskAdd.setCallbackInfo(ScheduleConstants.WORKFLOW);
        taskAdd.setCallbackInfo(SyncWaitResultService.getDubboHostIp());
        taskAdd.setAppSourceId(abilityRequest.getAppSourceId());
        taskAdd.setAbilityCode(taskAbility.getAbilityCode());
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        taskAdd.setAppId(appIdHolder);
        taskAdd.setSubList(buildComparisonTaskInfo(modelCustomConfig, CollectionUtil.getFirst(collect).getIndexId(), abilityRequest, taskAbility, taskAdd.getAppSourceId(), comparisonId));

        //缓存元数据
        ModelMetaInfo embeddingMetaInfo = new ModelMetaInfo(taskAddRequest.getCallbackUrl(), DubboDataContext.getAuthIdHolder(), taskAbility.getActionId(), new Date().getTime());
        embeddingMetaInfo.setBizMeta(taskAddRequest.getBizMeta());
        embeddingMetaInfo.setWorkflow(true);
        taskAdd.setMetaInfo(JSONObject.toJSONString(embeddingMetaInfo));
        return taskAdd;
    }

    /**
     * 构造文本向量化参数
     *
     * @param comparisonRequest
     * @param abilityEntity
     * @return
     */
    private List<TaskSubDTO> buildComparisonTaskInfo(List<AppConfigThirdContainsResponse> modelCustomConfig, String indexId, ComparisonRequest comparisonRequest,
                                                     TaskTypeAndAbility abilityEntity, String appSourceId, List<String> comparisonId) {
        List<TaskSubDTO> taskSubList = new ArrayList<>();
        TaskSubDTO taskSubDTO = buildBaseSubTaskInfo(comparisonRequest.getTaskOrder(), comparisonRequest.getVideoClusterSource(), abilityEntity);
        //base64图片处理
        String s = handleImage(comparisonRequest.getInputType(), comparisonRequest.getInput(), appSourceId);
        comparisonRequest.setInput(s);
        taskSubDTO.setRequestJson(buildRequestStr(modelCustomConfig, comparisonRequest, indexId, comparisonId));
        taskSubList.add(taskSubDTO);
        return taskSubList;
    }

    /**
     * 构造算法请求参数
     *
     * @param comparisonRequest
     * @return
     */
    private String buildRequestStr(List<AppConfigThirdContainsResponse> modelCustomConfig, ComparisonRequest comparisonRequest, String indexId, List<String> comparisonId) {

        String videoValidation = String.valueOf(IdWorker.nextId());
        String taskId = String.valueOf(IdWorker.nextId());
        AiExecuteImageParamDTO aiExecuteImageParamDTO = new AiExecuteImageParamDTO();
        aiExecuteImageParamDTO.setImageId("0");
        aiExecuteImageParamDTO.setVideoId("comparison");
        aiExecuteImageParamDTO.setSrcType(EmbeddingInputEnum.matchApiType(comparisonRequest.getInputType()));
        aiExecuteImageParamDTO.setData(comparisonRequest.getInput());
        aiExecuteImageParamDTO.setEventTime(DateUtil.getSimpleYMDHMS(LocalDateTime.now()));
        aiExecuteImageParamDTO.setTaskId(taskId);
        aiExecuteImageParamDTO.setIndexId(indexId);
        aiExecuteImageParamDTO.setOrgId(DubboDataContext.getAuthIdHolder());
        aiExecuteImageParamDTO.setVideoValidation(videoValidation);

        JSONObject jsonObject = buildConfig(modelCustomConfig);
        jsonObject.put("comparisonIdList", comparisonId);
        aiExecuteImageParamDTO.setKwargs(jsonObject);

        List<AiExecuteImageParamDTO> src = new ArrayList<>();
        src.add(aiExecuteImageParamDTO);

        return buildModelRequestStr(src, taskId);
    }


}
