package com.vos.kernel.business.controller.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linker.core.base.baseclass.BaseResp;
import com.linker.core.base.enums.RespCodeEnum;
import com.linker.core.base.exception.BusinessException;
import com.vos.kernel.business.api.IV3ApiBackService;
import com.vos.kernel.business.aspect.Token;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.business.dto.AgentDeleteRequest;
import com.vos.kernel.business.dto.agent.AgentInfoDTO;
import com.vos.kernel.business.dto.agent.ListInfoQueryDTO;
import com.vos.kernel.business.dto.agent.PackageCreateDTO;
import com.vos.kernel.business.dto.agent.SingleOperateDTO;
import com.vos.kernel.business.dto.agent.BatchOperateDTO;
import com.vos.kernel.business.dto.agent.EventTriggerInfoDTO;
import com.vos.kernel.business.dto.agent.WorkflowRequestHeaderDTO;
import com.vos.kernel.business.dto.agent.OperateResultDTO;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.service.agent.IAgentManagementService;

import com.vos.kernel.business.service.impl.AppManageManagerServiceImpl;
import com.vos.kernel.common.dubbo.DubboDataContext;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import org.springframework.http.MediaType;

/**
 * <p>
 * agent信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Slf4j
@RestController
@RequestMapping("/agent-management/v1")
public class AgentInfoController {
    @Resource
    private IAgentManagementService agentManagementService;

    @Resource
    private IV3ApiBackService apiBackService;

    @Resource
    WorkflowProperties workflowProperties;

    @Resource
    AppManageManagerServiceImpl appManageManagerService;

    @Token
    @GetMapping("/agents")
    public BaseResp<IPage<AgentInfoDTO>> getAgentList(
            ListInfoQueryDTO queryDTO) {

        queryDTO.setTenantId(DubboDataContext.getAuthIdHolder());

        IPage<AgentInfoDTO> page = agentManagementService.getAgentList(queryDTO);

        return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), page);
    }

    /**
     * 获取智能体的事件触发器列表
     */
    @Token
    @GetMapping("/agents/{id}/event-triggers")
    public BaseResp<IPage<EventTriggerInfoDTO>> getEventTriggerList(
            @PathVariable("id") String agentId,
            ListInfoQueryDTO queryDTO) {
        queryDTO.setId(agentId);
        IPage<EventTriggerInfoDTO> page = agentManagementService.getEventTriggerList(queryDTO);
        return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), page);
    }

    /**
     * 创建智能体
     */
    @Token
    @PostMapping(value = "/agents/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResp<String> createAgent(
            @Valid PackageCreateDTO createDTO,@com.vos.kernel.business.authentication.UserContext UserContext userContext) {
        try {
            WorkflowRequestHeaderDTO headerDTO = new WorkflowRequestHeaderDTO();
            headerDTO.setTenantId(DubboDataContext.getAuthIdHolder());
            headerDTO.setUserCode(DubboDataContext.getUserHolder());
            headerDTO.setUserInfo(userContext);
            createDTO.setHeaderDTO(headerDTO);

            String agentId = agentManagementService.createAgent(createDTO);
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), agentId);
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("创建智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "创建智能体失败");
        }
    }

    @PostMapping(value = "/agents/noauth/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResp<String> createAgentNoAuth(
            @RequestHeader("tenant_id") String tenantId,
            @RequestHeader("user_code") String userCode,
            @Valid PackageCreateDTO createDTO) {
        try {
            WorkflowRequestHeaderDTO headerDTO = new WorkflowRequestHeaderDTO();
            headerDTO.setTenantId(tenantId);
            headerDTO.setUserCode(userCode);
            createDTO.setHeaderDTO(headerDTO);

            log.info("创建智能体 createDTO: {}", createDTO);

            String agentId = agentManagementService.createAgent(createDTO);
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), agentId);
        } catch (BusinessException e) {
            log.error("创建智能体业务失败", e);
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("创建智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "创建智能体失败");
        }
    }

    /**
     * 停止智能体
     */
    @Token
    @PostMapping("/agents/stop")
    public BaseResp<List<OperateResultDTO>> stopAgent(
            @RequestBody @Valid BatchOperateDTO operateDTO) {
        try {
            operateDTO.setTenantId(DubboDataContext.getAuthIdHolder());
            List<OperateResultDTO> results = agentManagementService.stopAgent(operateDTO);
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), results);
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), RespCodeEnum.INVALID_ARGUMENT.getMessage());
        } catch (Exception e) {
            log.error("停止智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "停止智能体失败");
        }
    }

    /**
     * 启动智能体
     */
    @Token
    @PostMapping("/agents/start")
    public BaseResp<List<OperateResultDTO>> startAgent(
            @RequestBody @Valid BatchOperateDTO operateDTO) {
        try {
            operateDTO.setTenantId(DubboDataContext.getAuthIdHolder());
            List<OperateResultDTO> results = agentManagementService.startAgent(operateDTO);
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage(), results);
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), RespCodeEnum.INVALID_ARGUMENT.getMessage());
        } catch (Exception e) {
            log.error("启动智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "启动智能体失败");
        }
    }


    /**
     * 删除智能体
     */
    @Token
    @PostMapping("/agents/delete")
    public BaseResp<Boolean> deleteAgent(
            @RequestBody @Valid SingleOperateDTO operateDTO) {
        try {
            operateDTO.setTenantId(DubboDataContext.getAuthIdHolder());
            agentManagementService.deleteAgent(operateDTO.getId());
            return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage());
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), RespCodeEnum.INVALID_ARGUMENT.getMessage());
        } catch (Exception e) {
            log.error("删除智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "删除智能体失败");
        }
    }

    /**
     * 删除智能体同时删除构建的agent
     */
    @Token
    @PostMapping("/agents/deleteAndSync")
    public BaseResp<Boolean> deleteAgentAndSync(
            @RequestBody @Valid SingleOperateDTO operateDTO) {
        try {
            operateDTO.setTenantId(DubboDataContext.getAuthIdHolder());
            agentManagementService.deleteAgent(operateDTO.getId());
        } catch (BusinessException e) {
            return new BaseResp<>(RespCodeEnum.INVALID_ARGUMENT.getCode(), RespCodeEnum.INVALID_ARGUMENT.getMessage());
        } catch (Exception e) {
            log.error("删除智能体失败", e);
            return new BaseResp<>(RespCodeEnum.INTERNAL_ERROR.getCode(), "删除智能体失败");
        }
        //同步构建删除智能体
        AgentDeleteRequest agentDeleteRequest = new AgentDeleteRequest();
        agentDeleteRequest.setId(Long.valueOf(operateDTO.getId()));
        BaseResp<Boolean> resp = apiBackService.deleteAgent(workflowProperties.getBuilderEndpoint() + workflowProperties.getDeleteAgentRouter(),
                appManageManagerService.generateLinkerHeader(), agentDeleteRequest);
        if (!resp.isSuccess()) {
            throw new BusinessException("删除构建智能体失败,resp " + resp);
        }
        return new BaseResp<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage());
    }

}
