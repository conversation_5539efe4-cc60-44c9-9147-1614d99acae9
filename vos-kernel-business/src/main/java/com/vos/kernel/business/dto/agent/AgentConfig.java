package com.vos.kernel.business.dto.agent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class AgentConfig {
    @JsonProperty("agentId")
    private Long agentId;

    @JsonProperty("agentName")
    private String agentName;

    @JsonProperty("agentType")
    private Integer agentType;

    private String description;

    private String version;

    private String icon;

    private Integer sort;

    @JsonProperty("status")
    private Integer running;

    private AgentData data;

    @Data
    public static class AgentData {
        @JsonProperty("chatInfo")
        private ChatInfo chatInfo;

        @JsonProperty("modelInfo")
        private ModelInfo modelInfo;

        @JsonProperty("preChatInfo")
        private PreChatInfo preChatInfo;

        @JsonProperty("suggestReplyInfo")
        private SuggestReplyInfo suggestReplyInfo;

        private List<Trigger> triggers;
    }

    @Data
    public static class ChatInfo {
        @JsonProperty("workflowId")
        private String workflowId;

        @JsonProperty("workflowVersion")
        private String workflowVersion;
    }

    @Data
    public static class ModelInfo {
        @JsonProperty("modelId")
        private String modelId;

        private Double temperature;

        @JsonProperty("maxTokens")
        private Integer maxTokens;

        @JsonProperty("modelStyle")
        private Integer modelStyle;

        @JsonProperty("shortMemoryPolicy")
        private ShortMemoryPolicy shortMemoryPolicy;
    }

    @Data
    public static class ShortMemoryPolicy {
        @JsonProperty("historyRound")
        private Integer historyRound;
    }

    @Data
    public static class PreChatInfo {
        @JsonProperty("enablePresetQuestions")
        private Boolean enablePresetQuestions;

        @JsonProperty("presetQuestionType")
        private Integer presetQuestionType;

        @JsonProperty("presetQuestions")
        private List<PresetQuestion> presetQuestions;

        @JsonProperty("welcomeMessage")
        private String welcomeMessage;
    }

    @Data
    public static class PresetQuestion {
        private Boolean enable;
        private String question;
    }

    @Data
    public static class SuggestReplyInfo {
        @JsonProperty("enableSuggestion")
        private Boolean enableSuggestion;

        @JsonProperty("suggestReplyMode")
        private Integer suggestReplyMode;

        @JsonProperty("customizedSuggestPrompt")
        private String customizedSuggestPrompt;
    }

    @Data
    public static class Trigger {
        private String id;

        @JsonProperty("triggerType")
        private String triggerType;

        private String name;

        @JsonProperty("webhookData")
        private WebhookData webhookData;

        @JsonProperty("timeData")
        private TimeData timeData;

        private Action action;
    }

    @Data
    public static class WebhookData {
        private String url;

        @JsonProperty("bearerToken")
        private String bearerToken;

        @JsonProperty("outputSchema")
        private String outputSchema;
    }

    @Data
    public static class TimeData {
        @JsonProperty("cronExprList")
        private List<String> cronExprList;
    }

    @Data
    public static class Action {
        @JsonProperty("actionType")
        private String actionType;

        @JsonProperty("workflowAction")
        private WorkflowAction workflowAction;
    }

    @Data
    public static class WorkflowAction {
        @JsonProperty("workflowId")
        private String workflowId;

        @JsonProperty("workflowVersion")
        private Integer version;

        @JsonProperty("params")
        private String params;
    }
}