server:
  port: 8081
project:
  name: kernel-business
  moduleId: 1
spring:
  #授权解密私钥
  ras:
    prikey: MIIEogIBAAKCAQEAmLUp6bq9NeUP0tWtyUcIS2fwD37t7a2R1Fmx02BsCnw7EVIJjjntUrIWJYVrh8NZLX2gGoTqKbnIhWAO5c8pyanqUemE8iPTwZY52H9kuIWT7AjWgpvb09BfbThmHLw8T2O4kg5SR0mt7UCpwYcKnLKGvV7uC8u/Oo2VNN9cXX60Tp84RSJHOpUwn5UQT5oDF2dmcYjDSQ+wWaAvga96Bj1kZ5F/+dK8xBG8rX/Hxoek4IK4d7SzT+DissTWN+ZXQ1+sQFJ/i8SjWO0JLYIWZGTky2SFNrBF+GCqm8uC0cCZuiFlVBjrGvsFiY6Qv21KCXFEW9BXMJNpPYXNCXeygwIBAwKCAQAZc4b8Scozpi1Nzkehi9a3O/1X6nz88kL4uZ2jOrysagnYOFbtCaeNyFkGQOdBSzmHlPAEa3xcSaFrkAJ7oob28acNput9sKNK7l75apDJa5inVs5rGfn4orqSNBEEygoNO0ltrQ22jEenisb1loHEyGvKOn0B90qJwkOIz+S6P4bLYb5RJv14yus4L98wmTD8E6kdye8Ge7dXoFwJldw6O3mK7hdr3keJfIv4kn7z5LLpzdIZy967FYixGy2I3/oNVkFsgkMFVxpAWbx+57QKa9/E982hBPLZ4zlaIa4oHAk99LEp9gPFZcKYesyGqDkQpu8RQAzhlCLme18VgORPAoGBAMo6lGIiFch+uFE4Ya8SUk5/2ODhSv9IwhowI21tj/TNrL8Hghkg9fcFPmXS0DzifXJSQspMYqb7YTFsP3B28VVr02rSQF2AiGHUAFDbJzAZpBHT+dDx0IgWQWAp3wBbCizUA3znPikfl4YzrjqHSZaEgc8BY6s70tsMsOPcte0HAoGBAMFPwGA8Io3nG1wWFCYZsI6vGI7dginZZ0obupnanFvbK8weetpRpxiF6A8HQJEsrgETbQGQitPx/n0f4lSLtSWHhrjjBI/rMaQC0k1Y7K4MOZRZm+J+wgzGy/JVIm2GDjVyYcFxFqtbAO/C/4sQMl0hBdry7F6rR7zJ8K6rvGulAoGBAIbRuEFsDoWp0DYllnS24YmqkJXrh1TbLBF1bPOeX/iJHdSvrBDAo/oDfu6MitNBqPbhgdwy7G9SQMudf6BPS45H4kc21ZOrBZaNVYs8xMq7wraNUTX2iwVkK5VxP1WSBsiNV6iaKXC/ull3yXxaMQ8DATSrl8d9NzyzIJfoeUivAoGBAIDf1ZV9bF6aEj1kDW67ywnKEF8+VsaQ74a9JxE8aD08x91ppzw2b2WumrSvgGDIdAC3ngELBzf2qai/7DhdI25aWdCXWF/yIRgB4Yjl8x6y0Q2REpb/LAiEh/bjbEkECXj269ZLZHI8q0qB/7IKzD4WA+dMnZRyL9MxSx8dKEfDAoGAEZs+pdUTYTybvWdugwBKay+Mh9rmqVreGU0zA0xpYtF3BZ4fAxIVhohKlrO9JwBHKRQMiEF2YjH80+ldwqf9OHl1SKeM2QthkpP/g+N33KPVeP2LEJtb3MSkOcHRI3S/CI8AMDL4WaTpYnVu5KxzNHwY0+dhVbIzoMbc6xpYWbo=

  appid:
  appSecret:
  application:
    name: kernel-business
  cloud:
    nacos:
      config:
        server-addr: ***********:31223
        file-extension: yaml
        name: kernel-business
        namespace: public
        enabled: false   #local测试，不走nacos配置，直接本地环境
        shared-configs:

      discovery:
        server-addr: ***********:31223
        namespace: local
        enabled: false
    sentinel:
      datasource:
        flow:
          nacos:
            password:
            username:
            data-id: flow-rules
            group-id: SENTINEL_GROUP
            namespace: 69f8ac19-e75e-41f9-9df7-35f97299dda5
            rule-type: flow
            server-addr: **********:8848
      eager: true
      transport:
        dashboard: 192.168.142.200:8080
        port: 8721
      enabled: false
  datasource: #连接池配置
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************
    username: root
    password: Ff1z@TOFr^iwd%Ra
    type: com.alibaba.druid.pool.DruidDataSource
    initial-size: 8
    min-idle: 1
    max-active: 20
    max-wait: 60000
    time-between-eviction-runsMillis: 60000
    min-evictable-idle-timeMillis: 300000
    validation-query: select 'x' FROM DUAL
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    pool-prepared-statements: true
    max-open-prepared-statements: 20
    max-pool-prepared-statement-per-connection-size: 20
    filters: stat
    connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    use-global-data-source-stat: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 10000MB
      max-request-size: 10000MB

  redis:
    database: 0
    host: ***********
    port: 32222
    password: Ff1z@TOFr^iwd%Ra
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
    timeout: 3000
  redisson:
    config: |
      singleServerConfig:
        # 连接空闲超时，单位：毫秒
        idleConnectionTimeout: 10000
        # 连接超时，单位：毫秒
        connectTimeout: 10000
        # 命令等待超时，单位：毫秒
        timeout: 3000
        # 命令失败重试次数,如果尝试达到 retryAttempts（命令失败重试次数） 仍然不能将命令发送至某个指定的节点时，将抛出错误。
        # 如果尝试在此限制之内发送成功，则开始启用 timeout（命令等待超时） 计时。
        retryAttempts: 3
        # 命令重试发送时间间隔，单位：毫秒
        retryInterval: 1500
        #  # 重新连接时间间隔，单位：毫秒
        #  reconnectionTimeout: 3000
        #  # 执行失败最大次数
        #  failedAttempts: 3
        # 密码
         password: Ff1z@TOFr^iwd%Ra
        # 单个连接最大订阅数量
        subscriptionsPerConnection: 5
        # 客户端名称
        clientName: null
        #  # 节点地址
        address: redis://***********:32222
        # 发布和订阅连接的最小空闲连接数
        subscriptionConnectionMinimumIdleSize: 1
        # 发布和订阅连接池大小
        subscriptionConnectionPoolSize: 50
        # 最小空闲连接数
        connectionMinimumIdleSize: 32
        # 连接池大小
        connectionPoolSize: 64
        # 数据库编号
        database: 0
        # DNS监测时间间隔，单位：毫秒
        dnsMonitoringInterval: 5000
      # 线程池数量,默认值: 当前处理核数量 * 2
      threads: 8
      # Netty线程池数量,默认值: 当前处理核数量 * 2
      nettyThreads: 16
      # 编码
      codec: !<org.redisson.codec.JsonJacksonCodec> {}
      #codec: !<org.redisson.codec.MarshallingCodec> {}
      # 传输模式
      transportMode : "NIO"
  main:
    lazy-initialization: true

management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include:
  endpoint:
    shutdown:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: false
#arthas配置
arthas:
  tunnel-server: ws://**********:7777/ws   #tunnel-server配置

jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  hidePackages: com.vos.kernel.business
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
      limit: 256
      expireAfterWriteInMillis: 500

#      "redis-sentinel://127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381/?sentinelMasterId=mymaster"








######################### dubbo配置 ########################
dubbo:
  protocols:
    # 此为多协议的key，在@DubboService注解中指定时指定此key值
    tri:
      name: tri
      port: 21882
    dubbo:
      name: dubbo
      port: 21881
  application:
    name: kernel-business
    serialize-check-status: DISABLE
    register-mode: instance
    version: 1.0.0
  protocol:
    threadpool: cached
    serialization: kryo
    name: dubbo
    port: 21881
  # 注册中心配置
  registry:
    id: kernel-business
    parameters:
      # 命名空间
      namespace: public
    address: nacos://***********:31223
  # 消费者相关配置
  consumer:
    # 支持校验注解
    validation: true
    # 超时时间
    timeout: 30000
    # 初始化检查
    check: false
    retries: 0
    filter: dubboTraceFilter
#  metadata-report:
#    address: nacos://***********:31223
#    parameters:
#      namespace: kernel-meta-data
# seata 配置
seata:
  tx-service-group: kernel_tx_group
  # seata配置中心
  config:
    type: nacos
    nacos:
      namespace: 2922eced-5bb9-41b4-b0f7-a0b5f52ddf0d
      server-addr: **********:8848
      group: SEATA_GROUP
      data-id: seata_server.yaml
  # seata的注册中心
  registry:
    type: nacos
    nacos:
      server-addr: **********:8848
      namespace: 2922eced-5bb9-41b4-b0f7-a0b5f52ddf0d
      group: SEATA_GROUP
  enabled: false

huizhi:
  version: 1.0.0

#授权文件存储路径
license:
  bashPath: D:/lic

webdav:
  home: /data/hmy/webdav/
  url: http://**********/webdav/

omUpload:
  url:
  home:

oa:
  authorizeUrl:

# 应用堵塞异常限额
checkThread:
  lineCount: 10

api:
  imageMax: 100

token:
  expireTime: 30

rocketmq:
  name-server: http://***********:30838
  producer:
    group: default-business

invented:
  img_expire: 1
  persistent:



ability:
  file_task_upload:
    syncUrl: http://vos-kernel-business.vos:8181/task/v1/fileTaskUploadSync
    sendToV3AiAsyncUrl: http://vos-kernel-business.vos:8181/task/v1/sendToV3AiSync
    identifyImagesUrl: http://vos-kernel-business.vos:8181/task/v1/identifyImagesSync
    dataToVectorUrl: http://vos-kernel-business.vos:8181/task/v1/dataToVectorSync
    maxWaitMillis: 60000
    minWaitMillis: 1500
    sleepMillis: 500
    minSleepMillis: 100
app:
  swagger:
    enabled: true
    description: omi-server
    termsOfServiceUrl: "www.baidu.com"
    version: "2.8"
    basePackage: com.vos.kernel.business.controller

v35:
  selectionSwitch:

hub:
  open: true
  endpoint: http://***********:30432/hub/
disruptor:
  open-attrdet: true
  open-comparison: true
  open-embedding: true
  open-ocr: true
  open-od: true
  open-ovd: true

workflow:
  url: http://************:8080/orchestrator
  endpoint: http://************:8080/orchestrator
  timeout-ms: 3000
  max-http-client-num: 5
  max-requests: 1024
  max-requests-per-host: 32
  max-idle-connections: 30
  keep-alive-duration: 360
  biz-meta-length: 512
  ignore:
    - process_output
    - knowledge_center_complex_search
    - a

xxl:
  job:
    enabled: true
    admin:
      addresses: http://***********:30005/xxl-job-admin
    executor:
      appname: xxl-agentos-executor
      ip: ************
      port: 9790
      logpath: data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      executeTimeout: 600
    access-token: default_token

#对外
file:
  webdav:
    out-url: http://***********:32224/webdav/
    #对内读
    server-read-url: http://webdav-read.default:8080/webdav/
    #对内写
    server-write-url: http://webdav-write.default:8080/webdav/
    #对ai
    out-url-ai: http://***********:32224/webdav/
    #磁盘信息
    home: /data/hmy/webdav/
  minio:
    end-point: http://***********:30410
    server-url: http://***********:30410/osminio
    out-url: http://***********:30410/osminio

linker-storage:
  default-platform: kernel_minio
  ai-use-out-url: true
  gui-use-out-url: true
  webdav:
    - platform: webdav # 存储平台标识
      enable-storage: true  # 启用存储
      user: VisionOS
      password: IFdJySVmSTJP0P9b
      server: ${file.webdav.server-write-url}
      domain: ${file.webdav.out-url}
      domain-out: ${file.webdav.out-url}
      base-path: / # 基础路径
  minio:
    - platform: kernel_minio
      enable-storage: true
      end-point: ${file.minio.end-point}
      access-key: minio
      secret-key: W98rSm3SWNz^Ud^p
      domain: ${file.minio.server-url}
      domain-out: ${file.minio.out-url}
      base-path: /
      bucketName: osminio
event:
  trigger:
    inner:
      endpoint: http://vos-kernel-business.kernel:8181
    outer:
      # endpoint: http://127.0.0.1:8081 #需要替换成集群外部访问的endpoint
