<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.business.mapper.TaskTypeMapper">

    <select id="actionIdByUser" resultType="com.vos.kernel.data.api.model.req.SaveInventedSub">
        SELECT distinct a.action_id, a.task_type_code, a.is_match_type,a.ability_enum,a.is_register
        FROM t_task_type a
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        WHERE b.authentication_id = #{orgCode}
        and a.isdel = 0
        and a.status = 0
        and (a.ability_enum != 3 and a.ability_enum != 4)
        GROUP BY a.action_id,a.task_type_code, a.is_match_type,a.ability_enum,a.is_register
    </select>
    <select id="actionIdByOperator" resultType="java.lang.String">
        SELECT distinct a.action_id
        FROM t_task_type a
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        WHERE b.authentication_id = #{operator}
        and a.isdel = 0
        GROUP BY a.action_id
    </select>

    <select id="actionIdByCode" resultType="com.vos.kernel.business.vo.TypeInventedSub">
        SELECT distinct a.action_id, a.task_type_code, a.is_match_type
        FROM t_task_type a
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        WHERE b.authentication_id = #{orgCode}
        and a.isdel = 0
        and a.status = 0
        GROUP BY a.action_id, a.task_type_code, a.is_match_type
    </select>

    <select id="getUserV3Ability" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT action_id, task_type_code
        FROM t_task_type
        where orgcode = #{orgCode}
        and action_id = #{actionId}
        and isDel = 0

    </select>

    <select id="getUserV35Ability" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT distinct a.action_id, a.task_type_code
        FROM t_task_type a
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        where b.authentication_id = #{orgCode}
        and a.action_id = #{actionId}
        and a.isDel = 0
        and a.status = 0

    </select>

    <!--    //   and a.status = 0-->
    <select id="getUserAbility" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT distinct a.action_id,
        a.task_type_code,
        a.is_match_type,
        a.match_upload_url,
        a.ability,
        a.id,
        a.status,
        a.ability_enum,
        a.chat_model_name,
        a.chat_api_key
        FROM t_task_type a
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        WHERE (a.action_id = #{actionId} or a.action_id = #{rejectActionId})
        and a.isdel = 0 and b.authentication_id = #{orgCode}
    </select>

    <select id="getAbilityId" resultType="java.lang.Long">

        SELECT distinct b.operator_id
        FROM t_task_type a
        LEFT JOIN tb_ability b ON a.ability = b.ability_id
        WHERE a.task_type_code = #{taskTypeCode}
        and a.isdel = 0
        and a.status = 0
        and b.is_del = 0

    </select>

    <select id="getUserDynamicAbility" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT action_id, task_type_code
        FROM t_task_type
        where orgcode = #{orgCode}
        and action_id = #{actionId}
        and isDel = 0
    </select>

    <select id="getAbilityByTaskAbilityId" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT ability_code as actionId, ability_enum
        FROM tb_ability
        where operator_id = #{abilityId}
        and is_del = 0 limit 1
    </select>

    <select id="getAbilityEntity" resultType="com.vos.kernel.common.cache.TaskTypeAndAbility">
        SELECT distinct a.action_id,
        a.task_type_code,
        a.is_match_type,
        a.match_upload_url,
        a.ability,
        a.id,
        a.status,
        a.ability_enum,
        a.chat_model_name,
        a.name as abilityName,
        a.chat_api_key,tb.ability_code,tb.operator_id,tb.id as tbAbilityId
        FROM t_task_type a
        LEFT JOIN tb_ability tb ON a.ability = tb.ability_id and tb.is_del = 0
        LEFT JOIN t_apply_equity b ON a.task_type_code = b.task_type_code
        WHERE (a.action_id = #{actionId} or a.action_id = #{rejectActionId})
        and a.isdel = 0 and b.authentication_id = #{orgCode}
    </select>

    <select id="getActionId" resultType="com.vos.kernel.core.api.domain.TaskTypeEntity">
        SELECT id,action_id,model_status,is_register
        FROM t_task_type
        WHERE task_type_code = #{taskTypeCode}
    </select>

</mapper>
