<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.business.mapper.AgentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.kernel.business.entity.AgentInfoEntity">
        <id column="agent_id" property="agentId" />
        <result column="tenant_id" property="tenantId" />
        <result column="agent_type" property="agentType" />
        <result column="icon_path" property="iconPath" />
        <result column="config_path" property="configPath" />
        <result column="agent_name" property="agentName" />
        <result column="agent_version" property="agentVersion" />
        <result column="description" property="description" />
        <result column="sort" property="sort" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="last_start_time" property="lastStartTime" />
        <result column="is_deleted" property="deleted" />
        <result column="running" property="running" />
        <result column="workflow" property="workflow" />
        <result column="workflow_version" property="workflowVersion" />
        <result column="triggers" property="triggers" />
        <result column="model" property="model" />
        <result column="pre_chat" property="preChat" />
        <result column="suggest" property="suggest" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        agent_id, tenant_id, agent_type, icon_path, config_path, agent_name, agent_version, description, sort, create_time, update_time, last_start_time, is_deleted, running, workflow, workflow_version, triggers, model, pre_chat, suggest
    </sql>

    <select id="queryAgentInfoPage" resultType="com.vos.kernel.business.entity.AgentInfoEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_agent_info
        <where>
            AND is_deleted = 0
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
            <if test="agentName != null and agentName != ''">
                AND agent_name LIKE CONCAT('%', #{agentName}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="all != null and all != ''">
                AND (description LIKE CONCAT('%', #{all}, '%') OR agent_name LIKE CONCAT('%', #{all}, '%'))
            </if>
            <if test="running != null">
                AND running = #{running}
            </if>
        </where>
        ORDER BY last_start_time DESC
    </select>

    <!-- 删除智能体 -->
    <delete id="deleteById">
        DELETE FROM tb_agent_info WHERE agent_id = #{agentId}
    </delete>

</mapper>
